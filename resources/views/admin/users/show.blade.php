@section('title', 'Users Details')

<x-dashboard-layout>
    <div class="flex flex-col md:flex-row gap-6 px-4 py-8">
        {{-- Left Panel - User Info --}}
        <div class="w-full md:w-1/3 bg-white dark:bg-gray-800 shadow rounded-2xl p-6">
            <div class="text-center">
           <img class="w-24 h-24 rounded-full mx-auto mb-4" src="{{ Auth::user()->profile_photo_url ?? 'https://ui-avatars.com/api/?name=User&background=0E7D34&color=fff' }}" alt="User Avatar">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
                {{ Auth::user()->first_name . ' ' . Auth::user()->last_name }}
            </h2>                
            <p class="text-gray-500 dark:text-gray-400">{{ Auth::user()->email }}</p>
            {{-- <p class="text-gray-500 dark:text-gray-400">{{ Auth::user()->status }}</p> --}}

       <span class="badge {{ Auth::user()->status->badgeClass() }}">
                                                        {{ Auth::user()->status->label() }}
                                                    </span>
        <p class="text-gray-500 dark:text-gray-400">{{ Auth::user()->last_login->diffForHumans() }}</p>

            </div>

            {{-- Quick Stats --}}

             <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Quick Stats
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Contributions: <span class="text-gray-500 font-medium">{{ Auth::user()->contributions->count() }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Payouts collected: <span class="text-gray-500 font-medium">{{ Auth::user()->contributions->count() }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Groups Joined: <span class="text-gray-500 font-medium">{{ Auth::user()->groupMembers->count() }}</span></p></strong>


            </div>

             {{-- Bank --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Payout
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Payment method: <span class="text-gray-500 font-medium">Bank Transfer</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Account Number: <span class="text-gray-500 font-medium">{{ Auth::user()->bankAccounts->account_number }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Bank Name: <span class="text-gray-500 font-medium">{{ Auth::user()->bankAccounts?->bank?->name }}</span></p></strong>


            </div>

                {{-- Other info --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Other Info.
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Gender: <span class="text-gray-500 font-medium">{{ Auth::user()->gender}}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Account Number: <span class="text-gray-500 font-medium">{{ Auth::user()->bankAccounts->account_number }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Birthday: <span class="text-gray-500 font-medium"> {{ \Carbon\Carbon::parse(Auth::user()->data_of_birth)->format('j F, Y') }}</span></p></strong>


            </div>
                   {{-- Role --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Role
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Gender: <span class="text-gray-500 font-medium">{{ Auth::user()->gender}}</span></p></strong>
            </div>
        </div>

        {{-- Right Panel - Tabs --}}
        <div class="w-full md:w-2/3 bg-white dark:bg-gray-800 shadow rounded-2xl p-6">
            <div>
             <div class="mb-6">
                        <div class="sm:hidden">
                        <select id="tabs" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option>Profile</option>
                            <option>Groups</option>
                            <option>Transcations</option>
                            <option>Document</option>
                        </select>
                    </div>
                    <ul class="hidden w-full text-sm font-medium text-center text-gray-500 rounded-lg shadow-sm sm:flex dark:divide-gray-700 dark:text-gray-400">
                        <li class="w-full focus-within:z-10">
                            <a href="{{route('admin.users.show', $user->id)}}" class="inline-block w-full p-4 text-gray-900 bg-gray-100 border-r border-gray-200 dark:border-gray-700 rounded-s-lg focus:ring-4 focus:ring-blue-300 active focus:outline-none dark:bg-gray-700 dark:text-white" aria-current="page">Profile</a>
                        </li>
                        <li class="w-full focus-within:z-10">
                            <a href="{{route('admin.users.groups', $user->id)}}" class="inline-block w-full p-4 bg-white border-r border-gray-200 dark:border-gray-700 hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700">Groups</a>
                        </li>
                        <li class="w-full focus-within:z-10">
                            <a href="{{route('admin.users.transactions', $user->id)}}" class="inline-block w-full p-4 bg-white border-r border-gray-200 dark:border-gray-700 hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700">Transactions</a>
                        </li>
                        <li class="w-full focus-within:z-10">
                            <a href="{{route('admin.users.transactions', $user->id)}}" class="inline-block w-full p-4 bg-white border-s-0 border-gray-200 dark:border-gray-700 rounded-e-lg hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700">Documents</a>
                        </li>
                    </ul>

             </div>
           


                

                {{-- Profile Tab --}}
                    {{-- Profile Information --}}
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Profile Information</h2>

                <div class="flex space-x-2">
                    <!-- Suspend Button -->
                    <button class="px-4 py-2 text-sm font-medium text-yellow-700 bg-yellow-100 border border-yellow-300 rounded-lg hover:bg-yellow-200 dark:bg-yellow-800 dark:text-yellow-100 dark:border-yellow-600 dark:hover:bg-yellow-700">
                        Suspend
                    </button>

                    <!-- Delete Button -->
                    <button class="px-4 py-2 text-sm font-medium text-red-700 bg-red-100 border border-red-300 rounded-lg hover:bg-red-200 dark:bg-red-800 dark:text-red-100 dark:border-red-600 dark:hover:bg-red-700">
                        Delete
                    </button>
                </div>
            </div>

                <form class="">
                <div class="grid grid-cols-2 md:grid-cols-2 md:gap-6">
                    {{-- first name --}}
                    <div class="mb-5">
                        <label for="first_name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">First Name</label>
                        <input type="text" id="first_name" name="first_name" value="{{Auth::user()->first_name}}" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Enter First Name" required />
                    </div>
                    <x-input-error :messages="$errors->get('first_name')" class="mt-2" />

                    {{-- last name --}}

                    <div class="mb-5">
                        <label for="last_name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Last Name</label>
                        <input type="text" id="last_name" name="last_name" value="{{Auth::user()->last_name}}" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Enter Last Name" required />
                    </div>
                    <x-input-error :messages="$errors->get('last_name')" class="mt-2" />


                </div>

                {{-- Email and phonee number --}}

                  <div class="grid grid-cols-2 md:grid-cols-2 md:gap-6">
                    {{-- first name --}}
                    <div class="mb-5">
                        <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Email</label>
                        <input type="email" id="email" name="email" value="{{Auth::user()->email}}" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Enter First Name" required />
                    </div>
                    <x-input-error :messages="$errors->get('email')" class="mt-2" />

                    {{-- last name --}}

                    <div class="mb-5">
                        <label for="phone" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Phone Number</label>
                        <input type="text" id="phone" name="phone" value="{{Auth::user()->phone}}" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Enter Last Name" required />
                    </div>
                    <x-input-error :messages="$errors->get('phone')" class="mt-2" />


                </div>

                 {{-- Country and State --}}
                 <!-- locality -->
                <div class="grid col-span-3 gap-4 mb-2" x-data="{
                    loading:false,
                    response:{},
                        states: [],
                        async getStates(event) {
                        this.response = await (await fetch('/location/get/state/json'+event.target.value+'/json', {
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                        }

                        })).json();
                        if(this.response.status == 200){
                            this.loading=false;
                            this.states = this.response.data;
                        }
            }
                }">

                
                    <div class="col-md-4">
                        <x-input-label for="country" :value="__('Country')" class="block mb-2 text-md font-medium text-gray-900 dark:text-white " />
                        <div class="relative">

                        <x-select-input id="country" x-on:change="loading =! loading"  @change="getStates" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        name="country_id" autofocus >
                            <option value="">Select</option>
                    @foreach($countries as $country)
                        <option value="{{ $country->id }}" 
                                {{ (old('country_id') == $country->id || (isset($user) && $user->country_id == $country->id)) ? 'selected' : '' }}>
                                {{ $country->name }}
                            </option>                      
                      @endforeach
                        </x-select-input>
                        </div>
                        <x-input-error :messages="$errors->get('country')" class="mt-2" />
                    </div>

                    <div class="col-md-8 mb-1" >
                    <div class="grid grid-cols-2 gap-4" x-data="{
                        loading:false,
                        response:{},
                            cities: [],
                            async getCities(event) {
                            this.response = await (await fetch('/location/cities/'+event.target.value+'/json', {
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                            }

                            })).json();
                            if(this.response.status == 200){
                                this.loading=false;
                                this.cities = this.response.data;
                            }
                }
                    }">
                    <div class="col">
                        <div class="d-flex">
                            <x-input-label for="state" class="d-inline mb-2 text-md font-medium text-gray-900 dark:text-white flex ">{{ __('Select State') }}

                            </x-input-label>
                            <div role="status" x-show="loading" class="ml-3">
                                <svg aria-hidden="loading" width="16px" height="16px" class="w-5 h-5 text-gray-200 d-inline animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                    <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                </svg>
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>

                        <div class="relative mb-1">

                        <x-select-input id="state"
                        x-on:change="loading =! loading"  @change="getCities"
                        class="block mt-1 w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        name="state_id"   autofocus >
                        <option value="">Select</option>
                        @foreach ($states as $state)
                                <option
                                    {{ @old('state_id') == $state->id || $state->id == $user->state_id ? 'selected' : '' }}
                                    value="{{ $state->id }}">
                                    {{ $state->name }} </option>
                            @endforeach
                            <template x-for="state in states"
                                :key="state.id">
                                <option :value="state.id"
                                    x-text="state.name"></option>
                            </template>

                        </x-select-input>
                        </div>
                        <x-input-error :messages="$errors->get('state')" class="mt-2" />


                    </div>


                    <div class="col">
                        <div class="d-flex">
                        <x-input-label for="city" class="d-inline mb-2 text-md font-medium text-gray-900 dark:text-white flex ">{{ __('Select City') }}

                        </x-input-label>
                        <div role="status" x-show="loading" class="ml-3">
                            <svg aria-hidden="loading" width="16px" height="16px" class="w-5 h-5 d-inline text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                            </svg>
                            <span class="sr-only">Loading...</span>
                        </div>
                        </div>
                        <div class="relative mb-1">

                        <x-select-input id="city"

                        class="block mt-1 w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        name="city_id"   autofocus >
                            @foreach ($cities as $city)
                            <option
                                {{ @old('city_id') == $city->id || $city->id == $user->city_id ? 'selected' : '' }}
                                value="{{ $city->id }}">
                                {{ $city->name }} </option>
                        @endforeach
                        <template x-for="city in cities" :key="city.id">
                            <option :value="city.id" x-text="city.name"></option>
                        </template>
                    </x-select-input>
                        </div>
                        <x-input-error :messages="$errors->get('city')" class="mt-2" />
                    </div>
                    </div>
                    </div>
                </div>

                 {{-- end --}}

              
                               

                    {{-- Address --}}

                    <div class="mb-5">
                        <label for="address" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Address</label>
                        <input type="text" id="address" name="address" value="{{Auth::user()->address}}" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Enter Address" required />
                    </div>
                    <x-input-error :messages="$errors->get('address')" class="mt-2" />

                   {{-- password --}}
                    <h2 class="text-xl mb-4 mt-8 font-semibold text-gray-800 dark:text-white">
                    Password Information</h2>   

                    <div class="mb-5 col-md-6">
                        <label for="password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Change Password</label>
                        <input type="password" id="password" name="password" class="bg-gray-50 w-[400px] border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 block  p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Enter Address" required />
                    </div>
                    <x-input-error :messages="$errors->get('password')" class="mt-2" />     


               
                <div class="flex justify-end">
                <button type="submit" class="text-white bg-[#0E7D34] hover:bg-[#0E7D34] focus:ring-4 focus:outline-none focus:ring-[#0E7D34] font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Save Changes</button>

                </div>
                </form>

                    

                

                

              
            </div> 
            {{-- end --}}
        </div>


        

    </div>
</x-dashboard-layout>







