@section('title', 'Users')

<x-dashboard-layout>

    {{$errors}}
  

    <div class="w-full px-4 py-8">
        <div class="max-w-full max-auto sm:px-6 lg:px-6">
            <x-message-status></x-message-status>

            <div class="rounded-2xl border border-gray-200 p-5">

            <div class="flex justify-end mb-4">
                <!-- Modal toggle -->
                <button data-modal-target="authentication-modal" data-modal-toggle="authentication-modal"
                class="flex items-center gap-2 text-white bg-[#0E7D34] hover:bg-[#0E7D34] focus:ring-4 focus:outline-none focus:ring-[#0E7D34] font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-[#0E7D34] dark:hover:bg-[#0E7D34] dark:focus:ring-[#0E7D34]"
                type="button">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"></path>
                </svg>
                Create New User
            </button>

            </div>

                {{$dataTable->table()}}

            </div>

        </div>
    </div>



    {{-- modal --}}

    <!-- Main modal -->
<div id="authentication-modal" tabindex="-1" aria-hidden="true" class="hidden mt-6 overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-lg max-h-full">
        
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Create New User
                </h3>
                <button type="button" class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="authentication-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="p-4 md:p-5">
                <form  class="space-y-4" id="create-group-form" action="{{ route('admin.groups.store') }}" method="POST">
                    @csrf
                    <div>
                        <label for="first_name"  class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">First Name</label>
                        <input type="text" value="{{old('first_name')}}" name="first_name" id="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Enter First Name"  />
                    </div>
                    <x-input-error :messages="$errors->get('first_name')" class="mt-2" />

                    <div>
                        <label for="last_name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Last Name</label>
                        <input type="text" name="last_name" value="{{old('last_name')}}" id="last_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Enter Last Name"  />
                    </div>
                    <x-input-error :messages="$errors->get('last_name')" class="mt-2" />

                    <div>
                      <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Email</label>
                      <input type="email" name="email" value="{{old('email')}}" id="email" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Enter Email"  />
                    </div>
                    <x-input-error :messages="$errors->get('email')" class="mt-2" />
                    <div>
                      <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Password</label>
                      <input type="password" name="password" value="{{old('password')}}" id="email" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Enter Email"  />
                    </div>
                    <x-input-error :messages="$errors->get('password')" class="mt-2" />


                        <div>
                            <label for="role" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Role</label>
                            <select id="role" name="role" value="{{old('role')}}" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white">
                                <option>Select</option>
                               @foreach (\App\Enums\Role::cases() as $role)
                                <option value="{{ $role->value }}" {{ old('frequency') == $role->value ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('-', ' ', $role->value)) }}
                                </option>
                            @endforeach
                            </select>
                        <x-input-error :messages="$errors->get('role')" class="mt-2" />


                        </div>

                    {{--  --}}
                   
                   
              

                       
                   
                    <div class="flex justify-end">
                    <button type="submit" class="flex justify-content-end text-white bg-[#0E7D34] hover:bg-[#0E7D34] focus:ring-4 focus:outline-none focus:ring-[#0E7D34] font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-[#0E7D34] dark:hover:bg-[#0E7D34] dark:focus:ring-[#0E7D34]">Create New User</button>

                    </div>
                    
                </form>
            </div>
        </div>
    </div>
</div> 
    {{-- modal --}}

    {{-- Group success --}}

     


{{-- <div id="group-success-modal" tabindex="-1" class="hidden fixed inset-0 z-50 flex items-center justify-center  bg-opacity-50"> --}}
    <div id="group-success-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">

    <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6 ">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">🎉 Group Created Successfully</h3>
        
        <p class="text-sm text-gray-600 dark:text-gray-300 mb-6">
            Your group has been created. You can now manage contributions and invite members.
        </p>

        <div class="flex justify-between items-center gap-3">
            <a  href="{{route('admin.groups.index')}}" class="w-full text-center bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm">
                Go to Group
            </a>
           <button onclick="copyGroupLink(this)" type="button"
                data-link="{{ session('group_url') }}" id="goToGroupBtn"
                class="w-full cursor-pointer text-center bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 text-sm">
                Copy Group Link
            </button>
        </div>
    </div>
</div>



    {{--  --}}


@push('scripts')
{{ $dataTable->scripts(attributes: ['type' => 'module']) }}
@endpush


<script>
   function showGroupSuccessModal(groupUrl) {
    document.getElementById('group-success-modal').classList.remove('hidden');
    window.groupUrl = groupUrl;
}

//    function copyGroupLink(btn) {
//     const link = btn.dataset.link || window.groupUrl;
//     console.log('Link to copy:', link); // Debug line
//     if (!link) {
//         alert('No group link available to copy.');
//         return;
//     }
//     navigator.clipboard.writeText(link)
//         .then(() => alert('Group link copied to clipboard!'))
//         .catch(() => alert('Failed to copy link.'));
// }



function copyGroupLink(btn) {
    const link = btn.dataset.link || window.groupUrl;
    if (!link) {
        alert('No group link available to copy.');
        return;
    }

    // Fallback for insecure origin
    const textarea = document.createElement("textarea");
    textarea.value = link;
    textarea.style.position = "fixed";  // avoid scrolling to bottom
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();

    try {
        const successful = document.execCommand('copy');
        alert(successful ? 'Group link copied!' : 'Failed to copy link.');
    } catch (err) {
        alert('Failed to copy link.');
    }

    document.body.removeChild(textarea);
}



</script>


@if(session('group_created') && session('group_url'))
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            showGroupSuccessModal("{{ session('group_url') }}");
        });
    </script>
@endif


</x-dashboard-layout>






