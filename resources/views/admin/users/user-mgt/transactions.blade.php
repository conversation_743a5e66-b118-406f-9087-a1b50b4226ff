@section('title', 'Transactions')

<x-dashboard-layout>
    <div class="flex flex-col md:flex-row gap-6 px-4 py-8">
        {{-- Left Panel - User Info --}}
        <div class="w-full md:w-1/3 bg-white dark:bg-gray-800 shadow rounded-2xl p-6">
        <div class="text-center">
           <img class="w-24 h-24 rounded-full mx-auto mb-4" src="{{ Auth::user()->profile_photo_url ?? 'https://ui-avatars.com/api/?name=User&background=0E7D34&color=fff' }}" alt="User Avatar">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
                {{ Auth::user()->first_name . ' ' . Auth::user()->last_name }}
            </h2>                
            <p class="text-gray-500 dark:text-gray-400">{{ Auth::user()->email }}</p>
            {{-- <p class="text-gray-500 dark:text-gray-400">{{ Auth::user()->status }}</p> --}}

       <span class="badge {{ Auth::user()->status->badgeClass() }}">
                                                        {{ Auth::user()->status->label() }}
                                                    </span>
        <p class="text-gray-500 dark:text-gray-400">{{ Auth::user()->last_login->diffForHumans() }}</p>

            </div>

            {{-- Quick Stats --}}

             <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Quick Stats
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Contributions: <span class="text-gray-500 font-medium">{{ Auth::user()->contributions->count() }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Payouts collected: <span class="text-gray-500 font-medium">{{ Auth::user()->contributions->count() }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Groups Joined: <span class="text-gray-500 font-medium">{{ Auth::user()->groupMembers->count() }}</span></p></strong>


            </div>

             {{-- Bank --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Payout
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Payment method: <span class="text-gray-500 font-medium">Bank Transfer</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Account Number: <span class="text-gray-500 font-medium">{{ Auth::user()->bankAccounts->account_number }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Bank Name: <span class="text-gray-500 font-medium">{{ Auth::user()->bankAccounts?->bank?->name }}</span></p></strong>


            </div>

                {{-- Other info --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Other Info.
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Gender: <span class="text-gray-500 font-medium">{{ Auth::user()->gender}}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Account Number: <span class="text-gray-500 font-medium">{{ Auth::user()->bankAccounts->account_number }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Birthday: <span class="text-gray-500 font-medium"> {{ \Carbon\Carbon::parse(Auth::user()->data_of_birth)->format('j F, Y') }}</span></p></strong>


            </div>
                   {{-- Role --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Role
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Gender: <span class="text-gray-500 font-medium">{{ Auth::user()->gender}}</span></p></strong>


            </div>

        </div>

  

        <div class="w-full md:w-2/3 bg-white dark:bg-gray-800 shadow rounded-2xl ">
        <div class="mb-4">

            <div class="sm:hidden">
            <label for="tabs" class="sr-only">Select your country</label>
            <select id="tabs" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option>Profile</option>
                <option>Dashboard</option>
                <option>setting</option>
                <option>Invoioce</option>
            </select>
        </div>
        <ul class="hidden text-sm font-medium text-center text-gray-500 rounded-lg shadow-sm sm:flex dark:divide-gray-700 dark:text-gray-400">
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.show', $user->id)}}" class="inline-block w-full p-4 bg-white border-r border-gray-200 dark:border-gray-700 hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700" aria-current="page">Profile</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.groups', $user->id)}}" class="inline-block w-full p-4 bg-white border-s-0 border-gray-200 dark:border-gray-700 rounded-e-lg hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700">Groups</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.transactions', $user->id)}}" class="inline-block w-full p-4 text-gray-900 bg-gray-100 border-r border-gray-200 dark:border-gray-700 rounded-s-lg focus:ring-4 focus:ring-blue-300 active focus:outline-none dark:bg-gray-700 dark:text-white">Transactions</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.transactions', $user->id)}}" class="inline-block w-full p-4 bg-white border-s-0 border-gray-200 dark:border-gray-700 rounded-e-lg hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700">Documents</a>
            </li>
        </ul>

        </div>

        {{-- Transactions Content --}}
        <div class="w-full py-8">
            <div class="max-w-full max-auto sm:px-6 lg:px-6">
                <x-message-status></x-message-status>

                <!-- Tab Navigation -->
                <div class="border-b border-gray-200 mb-6">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        <button onclick="switchTransactionTab('transactions')" id="transactions-tab" class="transaction-tab-button active border-transparent text-[#0E7D34] hover:text-[#39A75E] hover:border-[#39A75E] whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Transactions
                        </button>
                        <button onclick="switchTransactionTab('contributions')" id="contributions-tab" class="transaction-tab-button border-transparent text-gray-500 hover:text-[#39A75E] hover:border-[#39A75E] whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                            Contributions
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div id="transaction-tab-content">
                    <!-- Transactions Tab -->
                    <div id="transactions-content" class="transaction-tab-content">
                        <div class="rounded-2xl border border-gray-200 p-5">
                            <h1 class="text-xl font-semibold text-gray-800 dark:text-white">Transaction History</h1>
                            {{$dataTable->table()}}
                        </div>
                    </div>

                    <!-- Contributions Tab -->
                    <div id="contributions-content" class="transaction-tab-content hidden">
                        <div class="rounded-2xl border border-gray-200 p-5">
                            <h1 class="text-xl font-semibold text-gray-800 dark:text-white">Contribution History</h1>

                            @if(isset($contributions) && $contributions->count() > 0)
                                <div class="overflow-x-auto bg-white border border-gray-200 rounded-lg mt-4">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Group</th>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Amount</th>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Status</th>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Payment Proof</th>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Date</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200">
                                            @foreach($contributions as $contribution)
                                            <tr class="hover:bg-gray-50">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-gray-900">{{ $contribution->group->group_name ?? 'N/A' }}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-semibold text-gray-900">₦{{ number_format($contribution->amount, 2) }}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-block px-3 py-1 rounded-full text-xs font-semibold
                                                        @if(isset($contribution->status) && $contribution->status->value === 'completed') bg-green-100 text-green-700
                                                        @elseif(isset($contribution->status) && $contribution->status->value === 'pending') bg-yellow-100 text-yellow-700
                                                        @elseif(isset($contribution->status) && $contribution->status->value === 'rejected') bg-red-100 text-red-700
                                                        @else bg-gray-100 text-gray-700
                                                        @endif">
                                                        {{ isset($contribution->status) ? ucfirst($contribution->status->value) : 'Pending' }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    @if($contribution->payment_proof)
                                                        <button onclick="openImageModal('{{ asset('storage/' . $contribution->payment_proof) }}', '{{ $contribution->created_at->format('M d, Y h:i A') }}', 'Contribution', '{{ $contribution->group->group_name ?? 'N/A' }}')" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                            </svg>
                                                            View Proof
                                                        </button>
                                                    @else
                                                        <span class="text-gray-400 text-xs">No proof</span>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900">{{ $contribution->created_at->format('M d, Y') }}</div>
                                                    <div class="text-xs text-gray-500">{{ $contribution->created_at->format('h:i A') }}</div>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-8">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">No contributions</h3>
                                    <p class="mt-1 text-sm text-gray-500">This user hasn't made any contributions yet.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>



        </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Modal Header -->
                <div class="flex items-center justify-between pb-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">Payment Proof</h3>
                    <button onclick="closeImageModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Modal Body -->
                <div class="mt-4">
                    <!-- Image Container -->
                    <div class="text-center mb-4">
                        <img id="modalImage" src="" alt="Payment Proof" class="max-w-full max-h-96 mx-auto rounded-lg shadow-md">
                    </div>

                    <!-- Image Details -->
                    <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600">Upload Time:</span>
                            <span id="modalUploadTime" class="text-sm text-gray-900"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600">Transaction Type:</span>
                            <span id="modalTransactionType" class="text-sm text-gray-900"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600">Group Name:</span>
                            <span id="modalGroupName" class="text-sm text-gray-900"></span>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex justify-end pt-4 border-t mt-4">
                    <button onclick="closeImageModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

@push('scripts')
{{ $dataTable->scripts(attributes: ['type' => 'module']) }}

<script>
function openImageModal(imageSrc, uploadTime, transactionType, groupName) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('modalUploadTime').textContent = uploadTime;
    document.getElementById('modalTransactionType').textContent = transactionType;
    document.getElementById('modalGroupName').textContent = groupName;
    document.getElementById('imageModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
    document.body.style.overflow = 'auto'; // Restore background scrolling
}

function switchTransactionTab(tabName) {
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.transaction-tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active', 'border-[#0E7D34]', 'text-[#0E7D34]');
        button.classList.add('border-transparent', 'text-gray-500');
    });

    // Hide all tab content
    const tabContents = document.querySelectorAll('.transaction-tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });

    // Add active class to clicked tab button
    const activeButton = document.getElementById(tabName + '-tab');
    if (activeButton) {
        activeButton.classList.add('active', 'border-[#0E7D34]', 'text-[#0E7D34]');
        activeButton.classList.remove('border-transparent', 'text-gray-500');
    }

    // Show corresponding tab content
    const activeContent = document.getElementById(tabName + '-content');
    if (activeContent) {
        activeContent.classList.remove('hidden');
    }
}

// Close modal when clicking outside of it
document.getElementById('imageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImageModal();
    }
});

// Initialize the first tab as active when page loads
document.addEventListener('DOMContentLoaded', function() {
    switchTransactionTab('transactions');
});
</script>
@endpush


    </div>
</x-dashboard-layout>







