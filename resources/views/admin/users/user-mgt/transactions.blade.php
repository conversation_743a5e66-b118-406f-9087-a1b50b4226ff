@section('title', 'Transactions')

<x-dashboard-layout>
    <div class="flex flex-col md:flex-row gap-6 px-4 py-8">
        {{-- Left Panel - User Info --}}
        <div class="w-full md:w-1/3 bg-white dark:bg-gray-800 shadow rounded-2xl p-6">
        <div class="text-center">
           <img class="w-24 h-24 rounded-full mx-auto mb-4" src="{{ Auth::user()->profile_photo_url ?? 'https://ui-avatars.com/api/?name=User&background=0E7D34&color=fff' }}" alt="User Avatar">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
                {{ Auth::user()->first_name . ' ' . Auth::user()->last_name }}
            </h2>                
            <p class="text-gray-500 dark:text-gray-400">{{ Auth::user()->email }}</p>
            {{-- <p class="text-gray-500 dark:text-gray-400">{{ Auth::user()->status }}</p> --}}

       <span class="badge {{ Auth::user()->status->badgeClass() }}">
                                                        {{ Auth::user()->status->label() }}
                                                    </span>
        <p class="text-gray-500 dark:text-gray-400">{{ Auth::user()->last_login->diffForHumans() }}</p>

            </div>

            {{-- Quick Stats --}}

             <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Quick Stats
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Contributions: <span class="text-gray-500 font-medium">{{ Auth::user()->contributions->count() }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Payouts collected: <span class="text-gray-500 font-medium">{{ Auth::user()->contributions->count() }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Groups Joined: <span class="text-gray-500 font-medium">{{ Auth::user()->groupMembers->count() }}</span></p></strong>


            </div>

             {{-- Bank --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Payout
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Payment method: <span class="text-gray-500 font-medium">Bank Transfer</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Account Number: <span class="text-gray-500 font-medium">{{ Auth::user()->bankAccounts->account_number }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Bank Name: <span class="text-gray-500 font-medium">{{ Auth::user()->bankAccounts?->bank?->name }}</span></p></strong>


            </div>

                {{-- Other info --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Other Info.
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Gender: <span class="text-gray-500 font-medium">{{ Auth::user()->gender}}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Account Number: <span class="text-gray-500 font-medium">{{ Auth::user()->bankAccounts->account_number }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Birthday: <span class="text-gray-500 font-medium"> {{ \Carbon\Carbon::parse(Auth::user()->data_of_birth)->format('j F, Y') }}</span></p></strong>


            </div>
                   {{-- Role --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Role
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Gender: <span class="text-gray-500 font-medium">{{ Auth::user()->gender}}</span></p></strong>


            </div>

        </div>

  

        <div class="w-full md:w-2/3 bg-white dark:bg-gray-800 shadow rounded-2xl ">
        <div class="mb-4">

            <div class="sm:hidden">
            <label for="tabs" class="sr-only">Select your country</label>
            <select id="tabs" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option>Profile</option>
                <option>Dashboard</option>
                <option>setting</option>
                <option>Invoioce</option>
            </select>
        </div>
        <ul class="hidden text-sm font-medium text-center text-gray-500 rounded-lg shadow-sm sm:flex dark:divide-gray-700 dark:text-gray-400">
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.show', $user->id)}}" class="inline-block w-full p-4 bg-white border-r border-gray-200 dark:border-gray-700 hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700" aria-current="page">Profile</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.groups', $user->id)}}" class="inline-block w-full p-4 text-gray-900 bg-gray-100 border-r border-gray-200 dark:border-gray-700 rounded-s-lg focus:ring-4 focus:ring-blue-300 active focus:outline-none dark:bg-gray-700 dark:text-white">Groups</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.transactions', $user->id)}}" class="inline-block w-full p-4 bg-white border-r border-gray-200 dark:border-gray-700 hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700">Transactions</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.transactions', $user->id)}}" class="inline-block w-full p-4 bg-white border-s-0 border-gray-200 dark:border-gray-700 rounded-e-lg hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700">Documents</a>
            </li>
        </ul>

        </div>

        {{-- table --}}

        <div class="w-full  py-8">
        <div class="max-w-full max-auto sm:px-6 lg:px-6">
            <x-message-status></x-message-status>

            <div class="rounded-2xl border border-gray-200 p-5">
                <h1 class="text-xl font-semibold text-gray-800 dark:text-white">Contributions/Payout</h1>

            <div class="flex justify-end mb-4">
                <!-- Modal toggle -->
                {{-- <button data-modal-target="authentication-modal" data-modal-toggle="authentication-modal"
                class="flex items-center gap-2 text-white bg-[#0E7D34] hover:bg-[#0E7D34] focus:ring-4 focus:outline-none focus:ring-[#0E7D34] font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-[#0E7D34] dark:hover:bg-[#0E7D34] dark:focus:ring-[#0E7D34]"
                type="button">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"></path>
                </svg>
                Create New User
            </button> --}}

            </div>

                {{$dataTable->table()}}

            </div>

        </div>
    </div>



        </div>
        </div>
    </div>
       
@push('scripts')
{{ $dataTable->scripts(attributes: ['type' => 'module']) }}
@endpush
        

    </div>
</x-dashboard-layout>







