@section('title', 'Transactions')

<x-dashboard-layout>
    <div class="flex flex-col md:flex-row gap-6 px-4 py-8">
        {{-- Left Panel - User Info --}}
           <div class="w-full md:w-1/3 bg-white dark:bg-gray-800 shadow rounded-2xl p-6">
            {{-- {{ dd($user) }} --}}

            <div class="text-center">
           <img class="w-24 h-24 rounded-full mx-auto mb-4" src="{{ $user->profile_photo_url ?? 'https://ui-avatars.com/api/?name=User&background=0E7D34&color=fff' }}" alt="User Avatar">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
                {{ $user->first_name . ' ' . $user->last_name }}
            </h2>                
            <p class="text-gray-500 dark:text-gray-400">{{ $user->email }}</p>
            {{-- <p class="text-gray-500 dark:text-gray-400">{{ Auth::user()->status }}</p> --}}

       <span class="badge {{ $user->status->badgeClass() }}">
                                                        {{ $user->status->label() }}
                                                    </span>
        <p class="text-gray-500 dark:text-gray-400">{{ $user?->last_login->diffForHumans() }}</p>

            </div>

            {{-- Quick Stats --}}

             <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Quick Stats
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Contributions: <span class="text-gray-500 font-medium">{{ $user->contributions->count() }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Payouts collected: <span class="text-gray-500 font-medium">{{ $user->contributions->count() }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Groups Joined: <span class="text-gray-500 font-medium">{{ $user->groupMembers->count() }}</span></p></strong>


            </div>

             {{-- Bank --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Payout
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Payment method: <span class="text-gray-500 font-medium">Bank Transfer</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Account Number: <span class="text-gray-500 font-medium">{{ $user?->bankAccounts?->account_number }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Bank Name: <span class="text-gray-500 font-medium">{{ $user?->bankAccounts?->bank?->name }}</span></p></strong>


            </div>

                {{-- Other info --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Other Info.
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Gender: <span class="text-gray-500 font-medium">{{ $user->gender}}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Account Number: <span class="text-gray-500 font-medium">{{ $user?->bankAccounts?->account_number }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Birthday: <span class="text-gray-500 font-medium"> {{ \Carbon\Carbon::parse($user->data_of_birth)->format('j F, Y') }}</span></p></strong>


            </div>
                   {{-- Role --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Role
            </h2>                
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Gender: <span class="text-gray-500 font-medium">{{ $user->gender}}</span></p></strong>
            </div>
        </div>

  

        <div class="w-full md:w-2/3 bg-white dark:bg-gray-800 shadow rounded-2xl ">
        <div class="mb-4">

            <div class="sm:hidden">
            <label for="tabs" class="sr-only">Select your country</label>
            <select id="tabs" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option>Profile</option>
                <option>Dashboard</option>
                <option>setting</option>
                <option>Invoioce</option>
            </select>
        </div>
        <ul class="hidden text-sm font-medium text-center text-gray-500 rounded-lg shadow-sm sm:flex dark:divide-gray-700 dark:text-gray-400">
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.show', $user->id)}}" class="inline-block w-full p-4 bg-white border-r border-gray-200 dark:border-gray-700 hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700" aria-current="page">Profile</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.groups', $user->id)}}" class="inline-block w-full p-4 bg-white border-s-0 border-gray-200 dark:border-gray-700 rounded-e-lg hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700">Groups</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.transactions', $user->id)}}" class="inline-block w-full p-4 text-gray-900 bg-gray-100 border-r border-gray-200 dark:border-gray-700 rounded-s-lg focus:ring-4 focus:ring-blue-300 active focus:outline-none dark:bg-gray-700 dark:text-white">Transactions</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.documents', $user->id)}}" class="inline-block w-full p-4 bg-white border-s-0 border-gray-200 dark:border-gray-700 rounded-e-lg hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700">Documents</a>
            </li>
        </ul>

        </div>

        {{-- table --}}

        <div class="w-full  py-8">
        <div class="max-w-full max-auto sm:px-6 lg:px-6">
            <x-message-status></x-message-status>

            <div class="rounded-2xl border border-gray-200 p-5">
                <h1 class="text-xl font-semibold text-gray-800 dark:text-white">Transactions</h1>

            <div class="flex justify-end mb-4">
                <!-- Modal toggle -->
                {{-- <button data-modal-target="authentication-modal" data-modal-toggle="authentication-modal"
                class="flex items-center gap-2 text-white bg-[#0E7D34] hover:bg-[#0E7D34] focus:ring-4 focus:outline-none focus:ring-[#0E7D34] font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-[#0E7D34] dark:hover:bg-[#0E7D34] dark:focus:ring-[#0E7D34]"
                type="button">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"></path>
                </svg>
                Create New User
            </button> --}}

            </div>

                {{$dataTable->table()}}

            </div>

        </div>
    </div>



        </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Modal Header -->
                <div class="flex items-center justify-between pb-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">Payment Proof</h3>
                    <button onclick="closeImageModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Modal Body -->
                <div class="mt-4">
                    <!-- Image Container -->
                    <div class="text-center mb-4">
                        <img id="modalImage" src="" alt="Payment Proof" class="max-w-full max-h-96 mx-auto rounded-lg shadow-md">
                    </div>

                    <!-- Image Details -->
                    <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600">Upload Time:</span>
                            <span id="modalUploadTime" class="text-sm text-gray-900"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600">Transaction Type:</span>
                            <span id="modalTransactionType" class="text-sm text-gray-900"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600">Group Name:</span>
                            <span id="modalGroupName" class="text-sm text-gray-900"></span>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex justify-end pt-4 border-t mt-4">
                    <button onclick="closeImageModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

@push('scripts')
{{ $dataTable->scripts(attributes: ['type' => 'module']) }}

<script>
function openImageModal(imageSrc, uploadTime, transactionType, groupName) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('modalUploadTime').textContent = uploadTime;
    document.getElementById('modalTransactionType').textContent = transactionType;
    document.getElementById('modalGroupName').textContent = groupName;
    document.getElementById('imageModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
    document.body.style.overflow = 'auto'; // Restore background scrolling
}



// Close modal when clicking outside of it
document.getElementById('imageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImageModal();
    }
});


</script>
@endpush


    </div>
</x-dashboard-layout>







