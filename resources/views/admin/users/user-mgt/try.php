@section('title', 'Users Details')

<x-dashboard-layout>
    <div class="flex flex-col md:flex-row gap-6 px-4 py-8">
        {{-- Left Panel - User Info --}}
        <div class="w-full md:w-1/3 bg-white dark:bg-gray-800 shadow rounded-2xl p-6">
            <div class="text-center">
           <img class="w-24 h-24 rounded-full mx-auto mb-4" src="{{ asset('storage/uploads/' . $user->profileimages) ?? 'https://ui-avatars.com/api/?name=User&background=0E7D34&color=fff' }}" alt="User Avatar">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
                {{ $user->first_name . ' ' . $user->last_name }}
            </h2>
            <p class="text-gray-500 dark:text-gray-400">{{ $user->email }}</p>
            {{-- <p class="text-gray-500 dark:text-gray-400">{{ $user->status }}</p> --}}

       <span class="badge {{ $user->status->badgeClass() }}">
                                                        {{ $user->status->label() }}
                                                    </span>
        <p class="text-gray-500 dark:text-gray-400">{{ $user->last_login ? $user->last_login->diffForHumans() : 'Never' }}</p>

            </div>

            {{-- Quick Stats --}}

             <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Quick Stats
            </h2>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Contributions: <span class="text-gray-500 font-medium">{{ isset($contributions) ? $contributions->count() : 0 }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Transactions: <span class="text-gray-500 font-medium">{{ isset($transactions) ? $transactions->count() : 0 }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Groups Joined: <span class="text-gray-500 font-medium">{{ $user->groups->count() }}</span></p></strong>


            </div>

             {{-- Bank --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Payout
            </h2>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Payment method: <span class="text-gray-500 font-medium">Bank Transfer</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Account Number: <span class="text-gray-500 font-medium">{{ $user->bankAccounts->account_number ?? 'N/A' }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Bank Name: <span class="text-gray-500 font-medium">{{ $user->bankAccounts?->bank?->name ?? 'N/A' }}</span></p></strong>


            </div>

                {{-- Other info --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Other Info.
            </h2>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Gender: <span class="text-gray-500 font-medium">{{ $user->gender ?? 'N/A' }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Phone: <span class="text-gray-500 font-medium">{{ $user->phone ?? 'N/A' }}</span></p></strong>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">Birthday: <span class="text-gray-500 font-medium">{{ $user->data_of_birth ? \Carbon\Carbon::parse($user->data_of_birth)->format('j F, Y') : 'N/A' }}</span></p></strong>


            </div>
                   {{-- Role --}}

       <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-4  mt-4">
            <h2 class="text-xl mb-2 font-semibold text-gray-800 dark:text-white">
                Role
            </h2>
            <strong><p class="text-gray-700 mb-2 dark:text-gray-400">User Type: <span class="text-gray-500 font-medium">{{ ucfirst($user->user_type ?? 'user') }}</span></p></strong>


            </div>

        </div>

        {{-- Right Panel - Tabs --}}
        <div class="w-full md:w-2/3 bg-white dark:bg-gray-800 shadow rounded-2xl p-6">
       <div class="mb-4">

            <div class="sm:hidden">
            <select id="tabs" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option>Profile</option>
                <option>Dashboard</option>
                <option>setting</option>
                <option>Invoioce</option>
            </select>
        </div>
        <ul class="hidden text-sm font-medium text-center text-gray-500 rounded-lg shadow-sm sm:flex dark:divide-gray-700 dark:text-gray-400">
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.show', $user->id)}}" class="inline-block w-full p-4 bg-white border-r border-gray-200 dark:border-gray-700 hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700" aria-current="page">Profile</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.groups', $user->id)}}" class="inline-block w-full p-4 bg-white border-r border-gray-200 dark:border-gray-700 hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700">Groups</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.transactions', $user->id)}}" class="inline-block w-full p-4 text-gray-900 bg-gray-100 border-r border-gray-200 dark:border-gray-700 focus:ring-4 focus:ring-blue-300 active focus:outline-none dark:bg-gray-700 dark:text-white">Transactions</a>
            </li>
            <li class="w-full focus-within:z-10">
                <a href="{{route('admin.users.transactions', $user->id)}}" class="inline-block w-full p-4 bg-white border-s-0 border-gray-200 dark:border-gray-700 rounded-e-lg hover:text-gray-700 hover:bg-gray-50 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700">Documents</a>
            </li>
        </ul>

        </div>

        {{-- Transactions Content --}}
        <div class="mt-6">
            <!-- Tab Navigation -->
            <div class="border-b border-gray-200 mb-6">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button onclick="switchTab('transactions')" id="transactions-tab" class="tab-button active border-transparent text-[#0E7D34] hover:text-[#39A75E] hover:border-[#39A75E] whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Transactions
                    </button>
                    <button onclick="switchTab('contributions')" id="contributions-tab" class="tab-button border-transparent text-[#0E7D34] hover:text-[#39A75E] hover:border-[#39A75E] whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Contributions
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div id="tab-content">
                <!-- Transactions Tab -->
                <div id="transactions-content" class="tab-content">
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900">Transaction History</h3>

                        @if(isset($transactions) && $transactions->count() > 0)
                            <div class="overflow-x-auto bg-white border border-gray-200 rounded-lg">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Transaction ID</th>
                                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Type</th>
                                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Amount</th>
                                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Date</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        @foreach($transactions as $transaction)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">{{ $transaction->txref ?? 'TXN-' . $transaction->id }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ ucfirst($transaction->tx_type) }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-semibold text-gray-900">₦{{ number_format($transaction->amount, 2) }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-block px-3 py-1 rounded-full text-xs font-semibold
                                                    @if($transaction->status->value === 'completed') bg-green-100 text-green-700
                                                    @elseif($transaction->status->value === 'pending') bg-yellow-100 text-yellow-700
                                                    @elseif($transaction->status->value === 'rejected') bg-red-100 text-red-700
                                                    @else bg-gray-100 text-gray-700
                                                    @endif">
                                                    {{ ucfirst($transaction->status->value) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ $transaction->created_at->format('M d, Y') }}</div>
                                                <div class="text-xs text-gray-500">{{ $transaction->created_at->format('h:i A') }}</div>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No transactions</h3>
                                <p class="mt-1 text-sm text-gray-500">This user hasn't made any transactions yet.</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Contributions Tab -->
                <div id="contributions-content" class="tab-content hidden">
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900">Contribution History</h3>

                        @if(isset($contributions) && $contributions->count() > 0)
                            <div class="overflow-x-auto bg-white border border-gray-200 rounded-lg">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Group</th>
                                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Amount</th>
                                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Proof</th>
                                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Date</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        @foreach($contributions as $contribution)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">{{ $contribution->group->group_name ?? 'N/A' }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-semibold text-gray-900">₦{{ number_format($contribution->amount, 2) }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-block px-3 py-1 rounded-full text-xs font-semibold
                                                    @if(isset($contribution->status) && $contribution->status->value === 'completed') bg-green-100 text-green-700
                                                    @elseif(isset($contribution->status) && $contribution->status->value === 'pending') bg-yellow-100 text-yellow-700
                                                    @elseif(isset($contribution->status) && $contribution->status->value === 'rejected') bg-red-100 text-red-700
                                                    @else bg-gray-100 text-gray-700
                                                    @endif">
                                                    {{ isset($contribution->status) ? ucfirst($contribution->status->value) : 'Pending' }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($contribution->payment_proof)
                                                    <a href="{{ asset('storage/' . $contribution->payment_proof) }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                                        View Proof
                                                    </a>
                                                @else
                                                    <span class="text-gray-400 text-sm">No proof</span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ $contribution->created_at->format('M d, Y') }}</div>
                                                <div class="text-xs text-gray-500">{{ $contribution->created_at->format('h:i A') }}</div>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No contributions</h3>
                                <p class="mt-1 text-sm text-gray-500">This user hasn't made any contributions yet.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        </div>

        {{-- try --}}



        {{-- try --}}
    </div>
</x-dashboard-layout>

<script>
function switchTab(tabName) {
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active', 'border-[#0E7D34]', 'text-[#0E7D34]');
        button.classList.add('border-transparent', 'text-gray-500');
    });

    // Hide all tab content
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });

    // Add active class to clicked tab button
    const activeButton = document.getElementById(tabName + '-tab');
    if (activeButton) {
        activeButton.classList.add('active', 'border-[#0E7D34]', 'text-[#0E7D34]');
        activeButton.classList.remove('border-transparent', 'text-gray-500');
    }

    // Show corresponding tab content
    const activeContent = document.getElementById(tabName + '-content');
    if (activeContent) {
        activeContent.classList.remove('hidden');
    }
}

// Initialize the first tab as active when page loads
document.addEventListener('DOMContentLoaded', function() {
    switchTab('transactions');
});
</script>







