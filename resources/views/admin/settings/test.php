@section('title', 'Profile Settings')

<x-dashboard-layout>
    <div class="max-w-full">
        <div class="w-full p-6 mx-auto">
            @if (session('success'))
                <div class="relative px-4 py-3 mb-5 text-teal-700 bg-teal-100 border border-teal-400 rounded" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline">{{ session('success') }}</span>
                    <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                        class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                        <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20">
                            <title>Close</title>
                            <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                        </svg>
                    </button>
                </div>
            @endif

            @if (session('error'))
                <div class="relative px-4 py-3 mb-5 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline">{{ session('error') }}</span>
                    <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                        class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                        <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20">
                            <title>Close</title>
                            <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                        </svg>
                    </button>
                </div>
            @endif

            <div class="flex">
                <!-- Sidebar -->
                <div class="w-64 bg-white shadow-lg min-h-screen">
                    <div class="p-6">
                        <!-- Navigation -->
                        <nav>
                            <ul class="space-y-2">
                                <li>
                                    <a href="#account" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group active">
                                        <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                        </svg>
                                        My Account
                                    </a>
                                </li>
                                <li>
                                    <a href="#bank" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                        <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 3L2 8v2h20V8l-10-5zM4 11v8h2v-6h12v6h2v-8H4zm3 8v-5h10v5H7z"/>
                                            <path d="M2 20h20v2H2v-2z"/>
                                        </svg>
                                        Bank
                                    </a>
                                </li>
                                <li>
                                    <a href="#security" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                        <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                                        </svg>
                                        Security
                                    </a>
                                </li>
                                <li>
                                    <a href="#notifications" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                        <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
                                        </svg>
                                        Notifications
                                    </a>
                                </li>
                                <li>
                                    <a href="#support" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                        <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                        </svg>
                                        Support
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="flex-1 p-6">
                    <!-- My Account Section -->
                    <div id="account-content" class="settings-content">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex justify-between mb-8 border-b border-gray-200 pb-4">
                                <h2 class="text-2xl font-bold text-gray-900 mb-6">My Account</h2>
                                <a href="" class="text-[#0E7D34] px-4 py-2 rounded-lg text-sm font-medium">Verify</a>
                            </div>
                            
                            <!-- Profile Picture -->
                            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm">
                                <div class="items-center flex space-x-4">
                                    <img class="mb-4 rounded-lg w-32 h-32 sm:mb-0 xl:mb-4 2xl:mb-0" 
                                         src="{{asset('/storage/uploads/'.Auth::user()->profile_image) }}" 
                                         id="preview-img" 
                                         alt="{{ Auth::user()->profile_image}}">
                                    <div>
                                        <h3 class="mb-1 text-xl font-bold text-gray-900">Profile picture</h3>
                                        <div class="mb-4 text-sm text-gray-500">JPG, GIF or PNG. Max size of 1MB</div>
                                        <form action="{{route('admin.upload.profile-image')}}" method="POST" enctype="multipart/form-data">
                                            @csrf
                                            <input type="file" id="profile_image" name="profile_image" 
                                                   accept="image/jpeg,image/png,image/jpg,image/gif,image/svg+xml"
                                                   class="block w-full p-3 mt-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                   onchange="previewImage(this)"/>
                                            <div class="flex items-center space-x-4">
                                                <button type="submit" class="mt-4 mb-12 px-4 py-2 bg-[#0E7D34] hover:bg-[#39A75E] border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest transition ease-in-out duration-150">
                                                    Save Continue
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <!-- Add your account form content here -->
                             <form id="UpdateProfile" action="{{route('admin.profile.update')}}" method="POST">
                                    @method('PUT')
                                    @csrf
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="firstname" :value="__('First Name')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="firstname"
                                            placeholder="First Name"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-[#0E7D34] focus:border-[#0E7D34] dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="text" name="first_name" :value="old('first_name')??$user->first_name"  autofocus autocomplete="First Name" />
                                            </div>
                                            <x-input-error :messages="$errors->get('first_name')" class="mt-2" />
                                        </div>
                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="lastname" :value="__('Last Name')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="lastname"
                                            placeholder="Last Name"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="text" name="last_name" :value="old('last_name')??$user->last_name"  autofocus autocomplete="Last Name" />
                                            </div>
                                            <x-input-error :messages="$errors->get('last_name')" class="mt-2" />
                                        </div>
                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="other_names"  :value="__('Other Names')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="other_names"
                                            placeholder="Other Name"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="text" name="other_names" :value="old('other_names')??$user->other_names"  autofocus autocomplete="Other Names" />
                                            </div>
                                            <x-input-error :messages="$errors->get('other_names')" class="mt-2" />
                                        </div>

                                        <div class="col-span-2 sm:col-span-2">
                                            <x-input-label for="address" :value="__('Address')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="address"
                                            placeholder="Address"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="text" name="address" :value="old('address')??$user->address"  autofocus autocomplete="Address" />
                                            </div>
                                            <x-input-error :messages="$errors->get('address')" class="mt-2" />
                                        </div>
                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="email" :value="__('Email Address')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="email"
                                            placeholder="Email Address"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="text" name="email" :value="old('email')??$user->email"  disabled="true" readonly="readonly"  autofocus autocomplete="Email Address" />
                                            </div>
                                            <x-input-error :messages="$errors->get('email')" class="mt-2" />
                                        </div>
                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="phone" :value="__('Phone Number')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="phone"
                                            placeholder="Phone Number"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="number" name="phone" :value="old('phone')??$user->phone"   autofocus autocomplete="Phone Number" />
                                            </div>
                                            <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                                        </div>


                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="gender" :value="__('Select Gender')" class="block  text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2 ">
                                                <x-input-icon>
                                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16 19h4a1 1 0 0 0 1-1v-1a3 3 0 0 0-3-3h-2m-2.236-4a3 3 0 1 0 0-4M3 18v-1a3 3 0 0 1 3-3h4a3 3 0 0 1 3 3v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                                    </svg>

                                                </x-input-icon>
                                            <x-select-input id="gender"
                                            class="block mt-1 w-full p-4 font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            name="gender"   autofocus >
                                              @foreach (App\Enums\Gender::cases() as $gender)
                                                <option value="{{$gender->value}}" {{(@old('gender')==$gender->value || $user->gender==$gender->value)?'selected':''}}>{{$gender->name}}</option>
                                            @endforeach


                                        </x-select-input>
                                            </div>
                                            <x-input-error :messages="$errors->get('gender')" class="mt-2" />
                                        </div>
                                        <div class="col-span-1 sm:col-span-1">
                                           
                                            <label for="date" class="block text-sm font-medium text-gray-700 mb-2">
                                            Date of Birth
                                        </label>
                                         <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                               <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 10h16m-8-3V4M7 7V4m10 3V4M5 20h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Zm3-7h.01v.01H8V13Zm4 0h.01v.01H12V13Zm4 0h.01v.01H16V13Zm-8 4h.01v.01H8V17Zm4 0h.01v.01H12V17Zm4 0h.01v.01H16V17Z"/>
                                                </svg>
                                                </div>
                                                <input type="date" id="date" value="{{old('data_of_birth')??$user->data_of_birth}}"  placeholder="{{old('data_of_birth')??$user->data_of_birth}}"  name="data_of_birth" class="bg-gray-50 font-bold block p-4 border border-gray-300 text-gray-900 text-sm sm:text-md rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required>
                                        </div>
                                            <x-input-error :messages="$errors->get('data_of_birth')" class="mt-2" />
                                        </div>

                                         <!-- locality -->
          
                        <div class="grid col-span-3 gap-4 mt-4 mb-8" 
                        x-data="{
                            loading: false,
                            stateLoading: false,
                            cityLoading: false,
                            states: [],
                            cities: [],
                            selectedCountry: '{{ old('country_id', $user->country_id ?? '') }}',
                            selectedState: '{{ old('state_id', $user->state_id ?? '') }}',
                            selectedCity: '{{ old('city_id', $user->city_id ?? '') }}',
                            
                            async getStates(countryId) {
                                if (!countryId) {
                                    this.states = [];
                                    this.cities = [];
                                    this.selectedState = '';
                                    this.selectedCity = '';
                                    return;
                                }
                                
                                this.stateLoading = true;
                                this.cities = [];
                                // Don't reset selectedState and selectedCity here if we're initializing
                                
                                try {
                                    const response = await fetch('/location/state/' + countryId + '/json', {
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                                        }
                                    });
                                    
                                    const result = await response.json();
                                    
                                    if (result.status === 200) {
                                        this.states = result.data;
                                    } else if (result.status === 204) {
                                        this.states = [];
                                        console.log('No states found for country:', countryId);
                                    } else {
                                        this.states = [];
                                        console.error('Error fetching states:', result.message);
                                    }
                                } catch (error) {
                                    console.error('Error fetching states:', error);
                                    this.states = [];
                                } finally {
                                    this.stateLoading = false;
                                }
                            },
                            
                            async getCities(stateId) {
                                if (!stateId) {
                                    this.cities = [];
                                    this.selectedCity = '';
                                    return;
                                }
                                
                                this.cityLoading = true;
                                // Don't reset selectedCity here if we're initializing
                                
                                try {
                                    const response = await fetch('/location/cities/' + stateId + '/json', {
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                                        }
                                    });
                                    
                                    const result = await response.json();
                                    
                                    if (result.status === 200) {
                                        this.cities = result.data;
                                    } else if (result.status === 204) {
                                        this.cities = [];
                                        console.log('No cities found for state:', stateId);
                                    } else {
                                        this.cities = [];
                                        console.error('Error fetching cities:', result.message);
                                    }
                                } catch (error) {
                                    console.error('Error fetching cities:', error);
                                    this.cities = [];
                                } finally {
                                    this.cityLoading = false;
                                }
                            },

                            async init() {
                                // Load states if country is pre-selected
                                if (this.selectedCountry) {
                                    await this.getStates(this.selectedCountry);
                                    // Load cities if state is pre-selected
                                    if (this.selectedState) {
                                        await this.getCities(this.selectedState);
                                    }
                                }
                            }
                        }"
                        x-init="init()">

                                            
                                            <!-- Country Selection -->
                                            <div class="col-md-4">
                                                <x-input-label for="country" :value="__('Select Country')" 
                                                            class="block mb-2 text-md font-medium text-gray-900 dark:text-white" />
                                                <div class="relative">
                                                    <x-select-input id="country"
                                                                x-model="selectedCountry"
                                                                @change="getStates($event.target.value)"
                                                                class="block w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                                name="country_id" 
                                                                autofocus>
                                                        <option value="">Select Country</option>
                                                        @foreach($countries as $country)
                                                            <option value="{{ $country->id }}" 
                                                                    {{ old('country_id', $user->country_id ?? '') == $country->id ? 'selected' : '' }}>
                                                                {{ $country->name }}
                                                            </option>
                                                        @endforeach
                                                    </x-select-input>
                                                </div>
                                                <x-input-error :messages="$errors->get('country_id')" class="mt-2" />
                                            </div>

                                            <!-- State and City Selection -->
                                            <div class="col-md-8 mb-1">
                                                <div class="grid grid-cols-2 gap-4">
                                                    
                                                    <!-- State Selection -->
                                                    <div class="col">
                                                        <div class="flex items-center">
                                                            <x-input-label for="state" 
                                                                        class="mb-2 text-md font-medium text-gray-900 dark:text-white flex">
                                                                {{ __('Select State') }}
                                                            </x-input-label>
                                                            <div role="status" x-show="stateLoading" class="ml-3">
                                                                <svg aria-hidden="true" width="16px" height="16px" 
                                                                    class="w-5 h-5 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" 
                                                                    viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C0 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                                                    <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                                                </svg>
                                                                <span class="sr-only">Loading...</span>
                                                            </div>
                                                        </div>
                                                        
                    <div class="relative mb-2">
                        <x-select-input id="state"
                                    x-model="selectedState"
                                    @change="getCities($event.target.value)"
                                    class="block mt-1 w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    name="state_id">
                            <option value="">Select State</option>
                            
                            <!-- Show existing state from database -->
                            @if($user->state_id)
                                @php
                                    $userState = $states->where('id', $user->state_id)->first();
                                @endphp
                                @if($userState)
                                    <option value="{{ $userState->id }}" selected>
                                        {{ $userState->name }}
                                    </option>
                                @endif
                            @endif
                            
                            <!-- Dynamic states from Alpine.js -->
                            <template x-for="state in states" :key="state.id">
                                <option :value="state.id" 
                                        x-text="state.name"
                                        :selected="state.id == selectedState"></option>
                            </template>
                        </x-select-input>
                    </div>



                                                        
                                                        <x-input-error :messages="$errors->get('state_id')" class="mt-2" />
                                                    </div>

                                                    <!-- City Selection -->
                                                    <div class="col">
                                                        <div class="flex items-center">
                                                            <x-input-label for="city" 
                                                                        class="mb-2 text-md font-medium text-gray-900 dark:text-white flex">
                                                                {{ __('Select City') }}
                                                            </x-input-label>
                                                            <div role="status" x-show="cityLoading" class="ml-3">
                                                                <svg aria-hidden="true" width="16px" height="16px" 
                                                                    class="w-5 h-5 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" 
                                                                    viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                                                    <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                                                </svg>
                                                                <span class="sr-only">Loading...</span>
                                                            </div>
                                                        </div>
                                                        
                                                        {{-- <div class="relative mb-2">
                                                            <x-select-input id="city"
                                                                        x-model="selectedCity"
                                                                        class="block mt-1 w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                                        name="city_id">
                                                                <option value="">Select City</option>
                                                                
                                                                <!-- Dynamic cities from Alpine.js -->
                                                                <template x-for="city in cities" :key="city.id">
                                                                    <option :value="city.id" x-text="city.name"></option>
                                                                </template>
                                                            </x-select-input>
                                                        </div> --}}
                    <div class="relative mb-2">
                        <x-select-input id="city"
                                    x-model="selectedCity"
                                    class="block mt-1 w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    name="city_id">
                            <option value="">Select City</option>
                            
                            <!-- Show existing city from database -->
                            @if($user->city_id)
                                @php
                                    $userCity = $cities->where('id', $user->city_id)->first();
                                @endphp
                                @if($userCity)
                                    <option value="{{ $userCity->id }}" selected>
                                        {{ $userCity->name }}
                                    </option>
                                @endif
                            @endif
                            
                            <!-- Dynamic cities from Alpine.js -->
                            <template x-for="city in cities" :key="city.id">
                                <option :value="city.id" 
                                        x-text="city.name"
                                        :selected="city.id == selectedCity"></option>
                            </template>
                        </x-select-input>
                    </div>

                                <x-input-error :messages="$errors->get('city_id')" class="mt-2" />
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Locality --}}
    <div class="grid col-span-3 gap-4 mt-4 mb-8" 
        x-data="{
            loading: false,
            stateLoading: false,
            cityLoading: false,
            states: [],
            cities: [],
            selectedCountry: '{{ old('country_id', $user->country_id ?? '') }}',
            selectedState: '{{ old('state_id', $user->state_id ?? '') }}',
            selectedCity: '{{ old('city_id', $user->city_id ?? '') }}',
            
            async getStates(countryId, preserveSelection = false) {
                if (!countryId) {
                    this.states = [];
                    this.cities = [];
                    this.selectedState = '';
                    this.selectedCity = '';
                    return;
                }
                
                this.stateLoading = true;
                this.cities = [];
                
                // Only clear selections if not preserving (i.e., when user manually changes country)
                if (!preserveSelection) {
                    this.selectedState = '';
                    this.selectedCity = '';
                }
                
                try {
                    const response = await fetch('/location/state/' + countryId + '/json', {
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                        }
                    });
                    
                    const result = await response.json();
                    
                    if (result.status === 200) {
                        this.states = result.data;
                    } else if (result.status === 204) {
                        this.states = [];
                        console.log('No states found for country:', countryId);
                    } else {
                        this.states = [];
                        console.error('Error fetching states:', result.message);
                    }
                } catch (error) {
                    console.error('Error fetching states:', error);
                    this.states = [];
                } finally {
                    this.stateLoading = false;
                }
            },
            
            async getCities(stateId, preserveSelection = false) {
                if (!stateId) {
                    this.cities = [];
                    this.selectedCity = '';
                    return;
                }
                
                this.cityLoading = true;
                
                // Only clear selection if not preserving (i.e., when user manually changes state)
                if (!preserveSelection) {
                    this.selectedCity = '';
                }
                
                try {
                    const response = await fetch('/location/cities/' + stateId + '/json', {
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                        }
                    });
                    
                    const result = await response.json();
                    
                    if (result.status === 200) {
                        this.cities = result.data;
                    } else if (result.status === 204) {
                        this.cities = [];
                        console.log('No cities found for state:', stateId);
                    } else {
                        this.cities = [];
                        console.error('Error fetching cities:', result.message);
                    }
                } catch (error) {
                    console.error('Error fetching cities:', error);
                    this.cities = [];
                } finally {
                    this.cityLoading = false;
                }
            },

            async init() {
                // Load states if country is pre-selected
                if (this.selectedCountry) {
                    await this.getStates(this.selectedCountry, true); // preserve selection on init
                    // Load cities if state is pre-selected
                    if (this.selectedState) {
                        await this.getCities(this.selectedState, true); // preserve selection on init
                    }
                }
            }
        }"
        x-init="init()">
    
    <!-- Country Selection -->
    <div class="col-md-4">
        <x-input-label for="country" :value="__('Select Country')" 
                    class="block mb-2 text-md font-medium text-gray-900 dark:text-white" />
        <div class="relative">
            <x-select-input id="country"
                        x-model="selectedCountry"
                        @change="getStates($event.target.value)"
                        class="block w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        name="country_id" 
                        autofocus>
                <option value="">Select Country</option>
                @foreach($countries as $country)
                    <option value="{{ $country->id }}" 
                            {{ old('country_id', $user->country_id ?? '') == $country->id ? 'selected' : '' }}>
                        {{ $country->name }}
                    </option>
                @endforeach
            </x-select-input>
        </div>
        <x-input-error :messages="$errors->get('country_id')" class="mt-2" />
    </div>

    <!-- State and City Selection -->
    <div class="col-md-8 mb-1">
        <div class="grid grid-cols-2 gap-4">
            
            <!-- State Selection -->
            <div class="col">
                <div class="flex items-center">
                    <x-input-label for="state" 
                                class="mb-2 text-md font-medium text-gray-900 dark:text-white flex">
                        {{ __('Select State') }}
                    </x-input-label>
                    <div role="status" x-show="stateLoading" class="ml-3">
                        <svg aria-hidden="true" width="16px" height="16px" 
                            class="w-5 h-5 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" 
                            viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                            <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                        </svg>
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
                
                <div class="relative mb-2">
                    <x-select-input id="state"
                                x-model="selectedState"
                                @change="getCities($event.target.value)"
                                class="block mt-1 w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                name="state_id">
                        <option value="">Select State</option>
                        
                        <!-- Dynamic states from Alpine.js -->
                        <template x-for="state in states" :key="state.id">
                            <option :value="state.id" 
                                    x-text="state.name"
                                    :selected="state.id == selectedState"></option>
                        </template>
                    </x-select-input>
                </div>
                <x-input-error :messages="$errors->get('state_id')" class="mt-2" />
            </div>

            <!-- City Selection -->
            <div class="col">
                <div class="flex items-center">
                    <x-input-label for="city" 
                                class="mb-2 text-md font-medium text-gray-900 dark:text-white flex">
                        {{ __('Select City') }}
                    </x-input-label>
                    <div role="status" x-show="cityLoading" class="ml-3">
                        <svg aria-hidden="true" width="16px" height="16px" 
                            class="w-5 h-5 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" 
                            viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                            <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                        </svg>
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
                
                <div class="relative mb-2">
                    <x-select-input id="city"
                                x-model="selectedCity"
                                class="block mt-1 w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                name="city_id">
                        <option value="">Select City</option>
                        
                        <!-- Dynamic cities from Alpine.js -->
                        <template x-for="city in cities" :key="city.id">
                            <option :value="city.id" 
                                    x-text="city.name"
                                    :selected="city.id == selectedCity"></option>
                        </template>
                    </x-select-input>
                </div>
                <x-input-error :messages="$errors->get('city_id')" class="mt-2" />
            </div>
        </div>
    </div>
</div>


{{-- Locality End --}}





                                <button type="submit"
                                        class=" mb-12 px-4 py-2 bg-[#0E7D34] hover:bg-[#39A75E] dark:bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-white dark:text-gray-800 uppercase tracking-widest dark:hover:bg-white focus:bg-gray-700 dark:focus:bg-white active:bg-gray-900 dark:active:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                    Save
                                </button>
                            </div>
                        </form>


                            {{-- form end --}}
                        </div>
                    </div>

                    <!-- Bank Section -->
                    <div id="bank-content" class="settings-content hidden">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Bank Information</h2>
                            <p>Bank settings content goes here...</p>
                        </div>
                    </div>

                    <!-- Security Section -->
                    <div id="security-content" class="settings-content hidden">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Security Settings</h2>
                            <p>Security settings content goes here...</p>
                        </div>
                    </div>

                    <!-- Notifications Section -->
                    <div id="notifications-content" class="settings-content hidden">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Notification Settings</h2>
                            <p>Notification settings content goes here...</p>
                        </div>
                    </div>

                    <!-- Support Section -->
                    <div id="support-content" class="settings-content hidden">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Support</h2>
                            <p>Support content goes here...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get all tab links and content sections
        const tabLinks = document.querySelectorAll('.settings-tab');
        const contentSections = document.querySelectorAll('.settings-content');
        
        // Function to show a specific tab
        function showTab(targetId) {
            // Hide all content sections
            contentSections.forEach(section => {
                section.classList.add('hidden');
            });
            
            // Remove active class from all tab links
            tabLinks.forEach(link => {
                link.classList.remove('active', 'bg-[#0E7D34]', 'text-white');
                link.classList.add('text-gray-700');
            });
            
            // Show the target content section
            const targetSection = document.getElementById(targetId + '-content');
            if (targetSection) {
                targetSection.classList.remove('hidden');
            }
            
            // Add active class to the clicked tab
            const activeTab = document.querySelector(`[href="#${targetId}"]`);
            if (activeTab) {
                activeTab.classList.add('active', 'bg-[#0E7D34]', 'text-white');
                activeTab.classList.remove('text-gray-700');
            }
        }
        
        // Add click event listeners to all tab links
        tabLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1); // Remove the # from href
                showTab(targetId);
            });
        });
        
        // Show the first tab by default (account)
        showTab('account');
    });
    </script>
</x-dashboard-layout>
