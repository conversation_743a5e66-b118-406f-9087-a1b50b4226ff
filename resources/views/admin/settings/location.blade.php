@section('title', 'Profile Settings')

<x-dashboard-layout>
    <div class="max-w-full ">

        <div class="w-full p-6 mx-auto">
            @if (session('success'))
                <div class="relative  px-4 py-3 mb-5 text-teal-700 bg-teal-100 border border-teal-400 rounded" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline">{{ session('success') }}</span>
                    <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                        class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                        <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20">
                            <title>Close</title>
                            <path
                                d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                        </svg>
                    </button>
                </div>
                @endif

                    @if (session('error'))
                    <div class="relative px-4 py-3 mb-5 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
                        <strong class="font-bold">Error!</strong>
                        <span class="block sm:inline">{{ session('error') }}</span>
                        <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                            class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                            <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20">
                                <title>Close</title>
                                <path
                                    d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                            </svg>
                        </button>
                    </div>
                    @endif


    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-6">

                <!-- Navigation -->
                <nav>
                    <ul class="space-y-2">
                        <li>
                            <a href="#account" id="account-content" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group active">
                                <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                </svg>
                                My Account
                            </a>
                        </li>
                         <li>
                            <a href="#bank" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 3L2 8v2h20V8l-10-5zM4 11v8h2v-6h12v6h2v-8H4zm3 8v-5h10v5H7z"/>
                                <path d="M2 20h20v2H2v-2z"/>
                                </svg>
                                Bank
                            </a>
                        </li>
                         <li>
                            <a href="{{route('admin.location.index')}}" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                </svg>
                                Locations
                            </a>
                        </li>
                        <li>
                            <a href="#security" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                                </svg>
                                Security
                            </a>
                        </li>
                        <li>
                            <a href="#notifications" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
                                </svg>
                                Notifications
                            </a>
                        </li>
                        <li>
                            <a href="#support" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                </svg>
                                Support
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <!-- My Account Section -->

            <div id="account-content" class="settings-content">

                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex justify-between mb-8 border-b border-gray-200 pb-4">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">My Account</h2>
                        <a href="" class=" text-[#0E7D34] px-4 py-2 rounded-lg text-sm font-medium">Verify</a>
                    </div>
                    {{$errors}}

            <div class="rounded-2xl border border-gray-200 p-5 mt-6">

                    {{$dataTable->table()}}

            </div>
                   
                   

                  

                   
                </div>
                 
            </div>

             <!-- Bank Section -->
            <div id="bank-content" class="settings-content hidden">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Bank </h2>

                        <div class="w-full">
                            <!-- Bank  -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-4"> View Banks</h3>

    <div class="w-full relative">
        <div class="flex items-center justify-between">
            <!-- Bank Information Section -->
            <div class="w-full bg-white rounded-lg shadow-sm p-6 mb-6">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 mr-3 text-[#0E7D34]" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 2L3 7v1h14V7l-7-5zM4 9v8h2v-6h8v6h2V9H4zm3 8v-5h6v5H7z"/>
                            <path d="M2 18h16v1H2v-1z"/>
                        </svg>
                        <h3 class="text-xl font-bold text-gray-900">Bank Information</h3>
                    </div>


                </div>

                <!-- Bank Details Display -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Bank Name</label>
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <svg class="w-5 h-5 mr-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 2L3 7v1h14V7l-7-5zM4 9v8h2v-6h8v6h2V9H4zm3 8v-5h6v5H7z"/>
                                    <path d="M2 18h16v1H2v-1z"/>
                                </svg>
                                <span class="text-gray-900 font-medium">
                                    {{ $userBank->bank->name ?? 'Not provided' }}
                                </span>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Account Number</label>
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <svg class="w-5 h-5 mr-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-gray-900 font-medium font-mono">
                                    {{ $userBank->account_number ?? 'Not provided' }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Account Name</label>
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <svg class="w-5 h-5 mr-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-gray-900 font-medium">
                                    {{ $userBank->account_name ?? 'Not provided' }}
                                </span>
                            </div>
                        </div>

                        {{-- <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">BVN</label>
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <svg class="w-5 h-5 mr-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-gray-900 font-medium font-mono">
                                    {{ $userBank->bvn ?? 'Not provided' }}
                                </span>
                            </div>
                        </div> --}}
                    </div>
                </div>
            </div>
        </div>


          {{-- Modal --}}



                        <!-- Modal toggle -->


                        <!-- Main modal -->
                        <div id="crud-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                        <div class="relative p-4 w-full max-w-2xl max-h-full">
                            <!-- Modal content -->
                            <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
                                <!-- Modal header -->
                                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                       Edit Bank
                                    </h3>
                                    <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-toggle="crud-modal">
                                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                                        </svg>
                                        <span class="sr-only">Close modal</span>
                                    </button>
                                </div>
                                <div class="p-6 space-y-6">
<!-- Modal body -->
                                     <form class="space-y-6" action="#" method="POST">
                                            @method('PUT')
                                            @csrf
                                            <!--header -->

                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Account Holder Name</label>
                                                    <input type="text" name="account_name" value=""  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" placeholder="Enter Account Name">
                                                    <x-input-error :messages="$errors->get('account_name')" class="mt-2" />
                                                </div>

                                                     <div class="">
                                                        <label for="bank_id" class="block  text-sm font-medium text-gray-700">Status</label>
                                                        <select name="bank_id" value="{{ old('bank_id') }}"  class="block w-full px-3 py-2  border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34]">
                                                            <option value="">Select Status</option>
                                                            {{-- @foreach ($user as $user) --}}
                                                                {{-- <option value="{{$bank->id}}" {{(old('bank_id') == $bank->id || ($userBanks->first()?->bank_id == $bank->id)) ? 'selected' : ''}}>{{$bank->name}}</option>                                                            @endforeach --}}
                                                        </select>
                                                        <x-input-error :messages="$errors->get('bank_id')" class="mt-2" />
                                                    </div>

                                            </div>

                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Account Number</label>
                                                    <input type="number" name="account_number" value=""  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" placeholder="Enter Account Number">
                                                    <x-input-error :messages="$errors->get('account_number')" class="mt-2" />
                                                </div>
                                                {{-- <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Routing Number</label>
                                                    <input type="number" name="bvn" value="{{ old('bvn') ?? ($userBanks->first()->bvn ?? '') }}"  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" placeholder="Bank Code">
                                                    <x-input-error :messages="$errors->get('bvn')" class="mt-2" />
                                                </div> --}}
                                            </div>
                                            <!-- Submit Button -->
                                                <div class="flex items-center justify-between">
                                                    <button
                                                    type="submit"
                                                    class="bg-[#0E7D34] hover:bg-[#39A75E] text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-400"
                                                    >
                                                    Save Information
                                                    </button>
                                                </div>
                                 </form>
                                </div>
                            </div>
                        </div>
                        </div>
                    <div class="absolute right-0 top-1 justify-end px-2">
                        <button id="dropdownButton-"
                         {{-- data-dropdown-toggle="dropdown-{{ $user->id }}" --}}
                          class="inline-block text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-1" type="button">
                            <span class="sr-only">Open dropdown</span>
                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M12 6h.01M12 12h.01M12 18h.01"/>
                            </svg>
                        </button>
                        <!-- Dropdown menu -->
                        <div id="dropdown-{{ $user->id }}" class="z-10 hidden text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-sm w-44 dark:bg-gray-700">
                            <ul class="py-2" aria-labelledby="dropdownButton-{{ $user->id }}">
                            <li class="text-black block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">
                                <button  data-modal-target="crud-modal" data-modal-toggle="crud-modal" >
                                            {{ __('Edit') }}
                                </button>
                            </li>


                            <li>
                                        <form action="" method="POST" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">
                                            @csrf
                                             @method('DELETE')
                                            <button type="submit" onclick="return confirm('Are you sure you want to delete this plan?')" class="text-red-500">
                                                    Delete
                                            </button>
                                        </form>
                            </li>
                            </ul>
                        </div>
                    </div>
                        </div>
                    {{-- @empty --}}
                        <div class="w-full bg-white rounded-lg shadow-sm p-6 mb-6">
                            <div class="text-center py-8">
                                <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No Bank Accounts</h3>
                                <p class="text-gray-500 mb-4">You haven't added any bank accounts yet.</p>

                            </div>
                        </div>
                    {{-- @endforelse --}}

{{-- @if($userBanks->isEmpty()) --}}

                            <!-- Add New Bank -->
                            <div class="border-t pt-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4"> Add Bank</h3>
                                        <form class="space-y-6" action="" method="POST">
                                            @csrf
                                            <!--header -->
                                            <div class="mt-4 text-gray-700 text-xl font-bold">


@push('scripts')
{{ $dataTable->scripts(attributes: ['type' => 'module']) }}
@endpush

</x-dashboard-layout>





