@section('title', 'Profile Settings')

<x-dashboard-layout>
    <div class="max-w-full ">

        <div class="w-full p-6 mx-auto">
            @if (session('success'))
                <div class="relative  px-4 py-3 mb-5 text-teal-700 bg-teal-100 border border-teal-400 rounded" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline">{{ session('success') }}</span>
                    <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                        class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                        <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20">
                            <title>Close</title>
                            <path
                                d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                        </svg>
                    </button>
                </div>
            @endif

            @if (session('error'))
                <div class="relative px-4 py-3 mb-5 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline">{{ session('error') }}</span>
                    <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                        class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                        <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20">
                            <title>Close</title>
                            <path
                                d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                        </svg>
                    </button>
                </div>
            @endif

            <div class="flex">
                <!-- Sidebar -->
                <div class="w-64 bg-white shadow-lg min-h-screen">
                    <div class="p-6">
                        <!-- Navigation -->
                        <nav>
                            <ul class="space-y-2">
                                <li>
                                    <a href="#account" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group active">
                                        <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                        </svg>
                                        My Account
                                    </a>
                                </li>
                                <li>
                                    <a href="#bank" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                        <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 3L2 8v2h20V8l-10-5zM4 11v8h2v-6h12v6h2v-8H4zm3 8v-5h10v5H7z"/>
                                            <path d="M2 20h20v2H2v-2z"/>
                                        </svg>
                                        Bank
                                    </a>
                                </li>
                                <li>
                                    <a href="#security" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                        <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                                        </svg>
                                        Security
                                    </a>
                                </li>
                                <li>
                                    <a href="#notifications" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                        <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
                                        </svg>
                                        Notifications
                                    </a>
                                </li>
                                <li>
                                    <a href="#support" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                        <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                        </svg>
                                        Support
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="flex-1 p-6">
                    <!-- My Account Section -->
                    <div id="account-content" class="settings-content">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex justify-between mb-8 border-b border-gray-200 pb-4">
                                <h2 class="text-2xl font-bold text-gray-900 mb-6">My Account</h2>
                                <a href="" class=" text-[#0E7D34] px-4 py-2 rounded-lg text-sm font-medium">Verify</a>
                            </div>

                            <!-- Profile Picture -->
                            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                                <div class="items-center flex space-x-4">
                                    <img class="mb-4 rounded-lg w-32 h-32 sm:mb-0 xl:mb-4 2xl:mb-0" src="{{asset('/storage/uploads/'.Auth::user()->profile_image) }}" id="preview-img" alt="{{ Auth::user()->profile_image}}">
                                    <div>
                                        <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">Profile picture</h3>
                                        <div class="mb-4 text-sm text-gray-500 dark:text-gray-400">
                                            JPG, GIF or PNG. Max size of 1MB
                                        </div>
                                        <form action="{{route('admin.upload.profile-image')}}" method="POST" enctype="multipart/form-data">
                                            @csrf
                                            <input type="file" id="profile_image" name="profile_image" accept="image/jpeg,image/png,image/jpg,image/gif,image/svg+xml"
                                                class="block w-full p-3 mt-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('profileimages') border-red @enderror"
                                                onchange="previewImage(this)"/>
                                            <div class="flex items-center space-x-4">
                                                <button type="submit" class="mt-4 mb-12 px-4 py-2 bg-[#0E7D34] hover:bg-[#39A75E] dark:bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-white dark:text-gray-800 uppercase tracking-widest dark:hover:bg-white focus:bg-gray-700 dark:focus:bg-white active:bg-gray-900 dark:active:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                                    Save Continue
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Profile Form -->
                            <form id="UpdateProfile" action="{{route('admin.profile.update')}}" method="POST">
                                @method('PUT')
                                @csrf
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="col-span-1 sm:col-span-1">
                                        <x-input-label for="firstname" :value="__('First Name')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                        <div class="relative mb-2">
                                            <x-text-input id="firstname"
                                                placeholder="First Name"
                                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-[#0E7D34] focus:border-[#0E7D34] dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                type="text" name="first_name" :value="old('first_name')??$user->first_name" autofocus autocomplete="First Name" />
                                        </div>
                                        <x-input-error :messages="$errors->get('first_name')" class="mt-2" />
                                    </div>
                                    
                                    <div class="col-span-1 sm:col-span-1">
                                        <x-input-label for="lastname" :value="__('Last Name')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                        <div class="relative mb-2">
                                            <x-text-input id="lastname"
                                                placeholder="Last Name"
                                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                type="text" name="last_name" :value="old('last_name')??$user->last_name" autofocus autocomplete="Last Name" />
                                        </div>
                                        <x-input-error :messages="$errors->get('last_name')" class="mt-2" />
                                    </div>
                                    
                                    <div class="col-span-1 sm:col-span-1">
                                        <x-input-label for="other_names" :value="__('Other Names')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                        <div class="relative mb-2">
                                            <x-text-input id="other_names"
                                                placeholder="Other Name"
                                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                type="text" name="other_names" :value="old('other_names')??$user->other_names" autofocus autocomplete="Other Names" />
                                        </div>
                                        <x-input-error :messages="$errors->get('other_names')" class="mt-2" />
                                    </div>

                                    <div class="col-span-2 sm:col-span-2">
                                        <x-input-label for="address" :value="__('Address')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                        <div class="relative mb-2">
                                            <x-text-input id="address"
                                                placeholder="Address"
                                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                type="text" name="address" :value="old('address')??$user->address" autofocus autocomplete="Address" />
                                        </div>
                                        <x-input-error :messages="$errors->get('address')" class="mt-2" />
                                    </div>
                                    
                                    <div class="col-span-1 sm:col-span-1">
                                        <x-input-label for="email" :value="__('Email Address')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                        <div class="relative mb-2">
                                            <x-text-input id="email"
                                                placeholder="Email Address"
                                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                type="text" name="email" :value="old('email')??$user->email" disabled="true" readonly="readonly" autofocus autocomplete="Email Address" />
                                        </div>
                                        <x-input-error :messages="$errors->get('email')" class="mt-2" />
                                    </div>
                                    
                                    <div class="col-span-1 sm:col-span-1">
                                        <x-input-label for="phone" :value="__('Phone Number')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                        <div class="relative mb-2">
                                            <x-text-input id="phone"
                                                placeholder="Phone Number"
                                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                type="number" name="phone" :value="old('phone')??$user->phone" autofocus autocomplete="Phone Number" />
                                        </div>
                                        <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                                    </div>

                                    <div class="col-span-1 sm:col-span-1">
                                        <x-input-label for="gender" :value="__('Select Gender')" class="block text-md font-medium text-gray-900 dark:text-white " />
                                        <div class="relative mb-2 ">
                                            <x-input-icon>
                                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16 19h4a1 1 0 0 0 1-1v-1a3 3 0 0 0-3-3h-2m-2.236-4a3 3 0 1 0 0-4M3 18v-1a3 3 0 0 1 3-3h4a3 3 0 0 1 3 3v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                                </svg>
                                            </x-input-icon>
                                            <x-select-input id="gender"
                                                class="block mt-1 w-full p-4 font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                name="gender" autofocus>
                                                @foreach (App\Enums\Gender::cases() as $gender)
                                                    <option value="{{$gender->value}}" {{(@old('gender')==$gender->value || $user->gender==$gender->value)?'selected':''}}>{{$gender->name}}</option>
                                                @endforeach
                                            </x-select-input>
                                        </div>
                                        <x-input-error :messages="$errors->get('gender')" class="mt-2" />
                                    </div>
                                    
                                    <div class="col-span-1 sm:col-span-1">
                                        <label for="date" class="block text-sm font-medium text-gray-700 mb-2">
                                            Date of Birth
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 10h16m-8-3V4M7 7V4m10 3V4M5 20h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Zm3-7h.01v.01H8V13Zm4 0h.01v.01H12V13Zm4 0h.01v.01H16V13Zm-8 4h.01v.01H8V17Zm4 0h.01v.01H12V17Zm4 0h.01v.01H16V17Z"/>
                                                </svg>
                                            </div>
                                            <input type="date" id="date" value="{{old('data_of_birth')??$user->data_of_birth}}" placeholder="{{old('data_of_birth')??$user->data_of_birth}}" name="data_of_birth" class="bg-gray-50 font-bold block p-4 border border-gray-300 text-gray-900 text-sm sm:text-md rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required>
                                        </div>
                                        <x-input-error :messages="$errors->get('data_of_birth')" class="mt-2" />
                                    </div>

                                    <!-- Location Selection Component -->
                                    <div class="col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 mb-8" 
                                         x-data="{
                                            loading: false,
                                            stateLoading: false,
                                            cityLoading: false,
                                            states: [],
                                            cities: [],
                                            selectedCountry: '{{ old('country_id', $user->country_id ?? '') }}',
                                            selectedState: '{{ old('state_id', $user->state_id ?? '') }}',
                                            selectedCity: '{{ old('city_id', $user->city_id ?? '') }}',
                                            
                                            async getStates(countryId, preserveSelection = false) {
                                                if (!countryId) {
                                                    this.states = [];
                                                    this.cities = [];
                                                    this.selectedState = '';
                                                    this.selectedCity = '';
                                                    return;
                                                }
                                                
                                                this.stateLoading = true;
                                                this.cities = [];
                                                
                                                if (!preserveSelection) {
                                                    this.selectedState = '';
                                                    this.selectedCity = '';
                                                }
                                                
                                                try {
                                                    const response = await fetch('/location/state/' + countryId + '/json', {
                                                        headers: {
                                                            'Content-Type': 'application/json',
                                                            'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                                                        }
                                                    });
                                                    
                                                    const result = await response.json();
                                                    console.log('States response:', result);
                                                    
                                                    if (result.status === 200 && result.data) {
                                                        this.states = result.data;
                                                    } else if (result.status === 204) {
                                                        this.states = [];
                                                        console.log('No states found for country:', countryId);
                                                    } else {
                                                        this.states = [];
                                                        console.error('Error fetching states:', result.message || 'Unknown error');
                                                    }
                                                } catch (error) {
                                                    console.error('Error fetching states:', error);
                                                    this.states = [];
                                                } finally {
                                                    this.stateLoading = false;
                                                }
                                            },
                                            
                                            async getCities(stateId, preserveSelection = false) {
                                                if (!stateId) {
                                                    this.cities = [];
                                                    this.selectedCity = '';
                                                    return;
                                                }
                                                
                                                this.cityLoading = true;
                                                
                                                if (!preserveSelection) {
                                                    this.selectedCity = '';
                                                }
                                                
                                                try {
                                                    const response = await fetch('/location/cities/' + stateId + '/json', {
                                                        headers: {
                                                            'Content-Type': 'application/json',
                                                            'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                                                        }
                                                    });
                                                    
                                                    const result = await response.json();
                                                    console.log('Cities response:', result);
                                                    
                                                    if (result.status === 200 && result.data) {
                                                        this.cities = result.data;
                                                    } else if (result.status === 204) {
                                                        this.cities = [];
                                                        console.log('No cities found for state:', stateId);
                                                    } else {
                                                        this.cities = [];
                                                        console.error('Error fetching cities:', result.message || 'Unknown error');
                                                    }
                                                } catch (error) {
                                                    console.error('Error fetching cities:', error);
                                                    this.cities = [];
                                                } finally {
                                                    this.cityLoading = false;
                                                }
                                            },

                                            async init() {
                                                console.log('Initializing with:', {
                                                    country: this.selectedCountry,
                                                    state: this.selectedState,
                                                    city: this.selectedCity
                                                });
                                                
                                                if (this.selectedCountry) {
                                                    await this.getStates(this.selectedCountry, true);
                                                    if (this.selectedState) {
                                                        await this.getCities(this.selectedState, true);
                                                    }
                                                }
                                            }
                                         }"
                                         x-init="init()">
                                        
                                        <!-- Country Selection -->
                                        <div class="col-span-1">
                                            <x-input-label for="country" :value="__('Select Country')" 
                                                          class="block mb-2 text-md font-medium text-gray-900 dark:text-white" />
                                            <div class="relative">
                                                <x-select-input id="country"
                                                               x-model="selectedCountry"
                                                               @change="getStates($event.target.value)"
                                                               class="block w-full p-3 font-bold bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#0E7D34] focus:border-[#0E7D34]"
                                                               name="country_id">
                                                    <option value="">Select Country</option>
                                                    @foreach($countries as $country)
                                                        <option value="{{ $country->id }}" 
                                                                {{ old('country_id', $user->country_id ?? '') == $country->id ? 'selected' : '' }}>
                                                            {{ $country->name }}
                                                        </option>
                                                    @endforeach
                                                </x-select-input>
                                            </div>
                                            <x-input-error :messages
                                            ="$errors->get('country_id')" class="mt-2" />
                                        </div>

                                        <!-- State Selection -->
                                        <div class="col-span-1">
                                            <div class="flex items-center mb-2">
                                                <x-input-label for="state" class="text-md font-medium text-gray-900 dark:text-white">
                                                    {{ __('Select State') }}
                                                </x-input-label>
                                                <div role="status" x-show="stateLoading" class="ml-3">
                                                    <svg class="w-4 h-4 text-gray-200 animate-spin fill-blue-600" viewBox="0 0 100 101">
                                                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            
                                            <x-select-input id="state"
                                                           x-model="selectedState"
                                                           @change="getCities($event.target.value)"
                                                           class="block w-full p-3 font-bold bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#0E7D34] focus:border-[#0E7D34]"
                                                           name="state_id">
                                                <option value="">Select State</option>
                                                <template x-for="state in states" :key="state.id">
                                                    <option :value="state.id" 
                                                            x-text="state.name"
                                                            :selected="state.id == selectedState"></option>
                                                </template>
                                            </x-select-input>
                                            <x-input-error :messages="$errors->get('state_id')" class="mt-2" />
                                        </div>

                                        <!-- City Selection -->
                                        <div class="col-span-1">
                                            <div class="flex items-center mb-2">
                                                <x-input-label for="city" class="text-md font-medium text-gray-900 dark:text-white">
                                                    {{ __('Select City') }}
                                                </x-input-label>
                                                <div role="status" x-show="cityLoading" class="ml-3">
                                                    <svg class="w-4 h-4 text-gray-200 animate-spin fill-blue-600" viewBox="0 0 100 101">
                                                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            
                                            <x-select-input id="city"
                                                           x-model="selectedCity"
                                                           class="block w-full p-3 font-bold bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#0E7D34] focus:border-[#0E7D34]"
                                                           name="city_id">
                                                <option value="">Select City</option>
                                                <template x-for="city in cities" :key="city.id">
                                                    <option :value="city.id" 
                                                            x-text="city.name"
                                                            :selected="city.id == selectedCity"></option>
                                                </template>
                                            </x-select-input>
                                            <x-input-error :messages="$errors->get('city_id')" class="mt-2" />
                                        </div>
                                    </div>
                                    <!-- End Location Selection -->

                                    <div class="col-span-2">
                                        <button type="submit"
                                                class="mb-12 px-4 py-2 bg-[#0E7D34] hover:bg-[#39A75E] dark:bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-white dark:text-gray-800 uppercase tracking-widest dark:hover:bg-white focus:bg-gray-700 dark:focus:bg-white active:bg-gray-900 dark:active:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                            Save
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Bank Section -->
                    <div id="bank-content" class="settings-content hidden">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Bank Information</h2>

                            <div class="w-full">
                                <!-- Bank Information Display -->
                                <div class="w-full relative">
                                    <div class="flex items-center justify-between">
                                        <!-- Bank Information Section -->
                                        <div class="w-full bg-white rounded-lg shadow-sm p-6 mb-6">
                                            <div class="flex items-center justify-between mb-6">
                                                <div class="flex items-center">
                                                    <svg class="w-6 h-6 mr-3 text-[#0E7D34]" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 2L3 7v1h14V7l-7-5zM4 9v8h2v-6h8v6h2V9H4zm3 8v-5h6v5H7z"/>
                                                        <path d="M2 18h16v1H2v-1z"/>
                                                    </svg>
                                                    <h3 class="text-xl font-bold text-gray-900">Bank Information</h3>
                                                </div>
                                            </div>

                                            <!-- Bank Details Display -->
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div class="space-y-4">
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-500 mb-1">Bank Name</label>
                                                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                                            <svg class="w-5 h-5 mr-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                                <path d="M10 2L3 7v1h14V7l-7-5zM4 9v8h2v-6h8v6h2V9H4zm3 8v-5h6v5H7z"/>
                                                                <path d="M2 18h16v1H2v-1z"/>
                                                            </svg>
                                                            <span class="text-gray-900 font-medium">
                                                                {{ $userBank->bank->name ?? 'Not provided' }}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-500 mb-1">Account Number</label>
                                                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                                            <svg class="w-5 h-5 mr-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"/>
                                                            </svg>
                                                            <span class="text-gray-900 font-medium font-mono">
                                                                {{ $userBank->account_number ?? 'Not provided' }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="space-y-4">
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-500 mb-1">Account Name</label>
                                                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                                            <svg class="w-5 h-5 mr-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                                            </svg>
                                                            <span class="text-gray-900 font-medium">
                                                                {{ $userBank->account_name ?? 'Not provided' }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Edit Bank Modal -->
                                    <div id="crud-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                        <div class="relative p-4 w-full max-w-2xl max-h-full">
                                            <!-- Modal content -->
                                            <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
                                                <!-- Modal header -->
                                                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                                        Edit Bank Information
                                                    </h3>
                                                    <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-toggle="crud-modal">
                                                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                                                        </svg>
                                                        <span class="sr-only">Close modal</span>
                                                    </button>
                                                </div>
                                                
                                                <div class="p-6 space-y-6">
                                                    <!-- Modal body -->
                                                    <form class="space-y-6" action="#" method="POST">
                                                        @method('PUT')
                                                        @csrf
                                                        
                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">Account Holder Name</label>
                                                                <input type="text" name="account_name" value="" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" placeholder="Enter Account Name">
                                                                <x-input-error :messages="$errors->get('account_name')" class="mt-2" />
                                                            </div>

                                                            <div class="">
                                                                <label for="bank_id" class="block text-sm font-medium text-gray-700">Bank</label>
                                                                <select name="bank_id" value="{{ old('bank_id') }}" class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34]">
                                                                    <option value="">Select Bank</option>
                                                                    {{-- Add your banks here --}}
                                                                </select>
                                                                <x-input-error :messages="$errors->get('bank_id')" class="mt-2" />
                                                            </div>
                                                        </div>

                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">Account Number</label>
                                                                <input type="number" name="account_number" value="" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" placeholder="Enter Account Number">
                                                                <x-input-error :messages="$errors->get('account_number')" class="mt-2" />
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- Submit Button -->
                                                        <div class="flex items-center justify-between">
                                                            <button type="submit" class="bg-[#0E7D34] hover:bg-[#39A75E] text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-400">
                                                                Save Information
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Dropdown Menu -->
                                    <div class="absolute right-0 top-1 justify-end px-2">
                                        <button id="dropdownButton" class="inline-block text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-1" type="button">
                                            <span class="sr-only">Open dropdown</span>
                                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M12 6h.01M12 12h.01M12 18h.01"/>
                                            </svg>
                                        </button>
                                        
                                        <!-- Dropdown menu -->
                                        <div id="dropdown" class="z-10 hidden text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-sm w-44 dark:bg-gray-700">
                                            <ul class="py-2" aria-labelledby="dropdownButton">
                                                <li class="text-black block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">
                                                    <button data-modal-target="crud-modal" data-modal-toggle="crud-modal">
                                                        {{ __('Edit') }}
                                                    </button>
                                                </li>
                                                <li>
                                                    <form action="" method="POST" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" onclick="return confirm('Are you sure you want to delete this bank account?')" class="text-red-500">
                                                            Delete
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add New Bank Section -->
                                <div class="border-t pt-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Add New Bank Account</h3>
                                    <form class="space-y-6" action="" method="POST">
                                        @csrf
                                        
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Account Holder Name</label>
                                                <input type="text" name="account_name" value="{{ old('account_name') }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" placeholder="Enter Account Name" required>
                                                <x-input-error :messages="$errors->get('account_name')" class="mt-2" />
                                            </div>

                                            <div>
                                                <label for="bank_id" class="block text-sm font-medium text-gray-700 mb-2">Select Bank</label>
                                                <select name="bank_id" class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" required>
                                                    <option value="">Select Bank</option>
                                                    {{-- Add your banks here --}}
                                                </select>
                                                <x-input-error :messages="$errors->get('bank_id')" class="mt-2" />
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Account Number</label>
                                                <input type="text" name="account_number" value="{{ old('account_number') }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" placeholder="Enter Account Number" required>
                                                <x-input-error :messages="$errors->get('account_number')" class="mt-2" />
                                            </div>
                                        </div>

                                        <div class="flex items-center justify-start">
                                            <button type="submit" class="bg-[#0E7D34] hover:bg-[#39A75E] text-white font-bold py-2 px-6 rounded focus:outline-none focus:ring-2 focus:ring-[#0E7D34]">
                                                Add Bank Account
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Section -->
                    <div id="security-content" class="settings-content hidden">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Security Settings</h2>
                            
                            <!-- Change Password Form -->
                            <form class="space-y-6" method="POST" action="">
                                @csrf
                                @method('PUT')
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                                    <input type="password" name="current_password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" required>
                                    <x-input-error :messages="$errors->get('current_password')" class="mt-2" />
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                                    <input type="password" name="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" required>
                                    <x-input-error :messages="$errors->get('password')" class="mt-2" />
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                                    <input type="password" name="password_confirmation" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" required>
                                </div>

                                <button type="submit" class="bg-[#0E7D34] hover:bg-[#39A75E] text-white font-bold py-2 px-6 rounded focus:outline-none focus:ring-2 focus:ring-[#0E7D34]">
                                    Update Password
                                </button>
                            </form>

                            <!-- Two-factor Authentication Section -->
                            <h2 class="text-2xl font-bold text-gray-900 mt-10 mb-4">Two-factor authentication</h2>
                            <p class="mb-4">Enhance the security of your account by implementing a second authentication method alongside your password. 
                            This can be either through a text message or by utilizing an authentication app.</p>

                            <!-- Authentication Options -->
                            <div class="space-y-4">
                            <label class="flex items-start justify-between cursor-pointer">
                                <div>
                                    <span class="font-medium text-gray-900">Text Message</span>
                                    <p class="text-gray-600 text-sm">Use your mobile phone to receive a text message with an authentication code to enter when you log in to your account.</p>
                                </div>
                                <input type="checkbox" name="two_factor[]" value="sms" class="mt-1 ml-3 flex-shrink-0">
                            </label>
                            
                            <label class="flex items-start justify-between cursor-pointer">
                                <div>
                                    <span class="font-medium text-gray-900">Authentication App</span>
                                    <p class="text-gray-600 text-sm">Use a mobile authentication app to get a verification code to enter every time you log in to your account.</p>
                                </div>
                                <input type="checkbox" name="two_factor[]" value="auth_app" class="mt-1 ml-3 flex-shrink-0">
                            </label>
                        </div>

                        </div>
                    </div>


                    <!-- Notifications Section -->
              <div id="notifications-content" class="settings-content hidden">
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Notification Preferences</h2>
        
        <form class="space-y-6" method="POST" action="{{ route('admin.notifications.update') }}">
            @csrf
            @method('PUT')
            
            <div class="space-y-6">
                <!-- Email Notifications -->
                <div class="flex items-center justify-between py-4 border-b border-gray-200">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">Email Notifications</h3>
                        <p class="text-sm text-gray-500">Receive notifications via email for important updates</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input 
                            type="checkbox" 
                            name="email_notifications" 
                            id="email_notifications"
                            value="1"
                            {{ old('email_notifications', $user->email_notifications ?? false) ? 'checked' : '' }}
                            class="sr-only peer"
                        >
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#0E7D34]/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0E7D34]"></div>
                    </label>
                </div>

                <!-- SMS Notifications -->
                <div class="flex items-center justify-between py-4 border-b border-gray-200">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">SMS Notifications</h3>
                        <p class="text-sm text-gray-500">Receive text messages for critical alerts and updates</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input 
                            type="checkbox" 
                            name="sms_notifications" 
                            id="sms_notifications"
                            value="1"
                            {{ old('sms_notifications', $user->sms_notifications ?? false) ? 'checked' : '' }}
                            class="sr-only peer"
                        >
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#0E7D34]/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0E7D34]"></div>
                    </label>
                </div>

                <!-- Push Notifications -->
                <div class="flex items-center justify-between py-4 border-b border-gray-200">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">Push Notifications</h3>
                        <p class="text-sm text-gray-500">Receive browser push notifications when you're online</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input 
                            type="checkbox" 
                            name="push_notifications" 
                            id="push_notifications"
                            value="1"
                            {{ old('push_notifications', $user->push_notifications ?? false) ? 'checked' : '' }}
                            class="sr-only peer"
                        >
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#0E7D34]/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0E7D34]"></div>
                    </label>
                </div>

                <!-- Device Login Alerts -->
                <div class="flex items-center justify-between py-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">Device Login Alerts</h3>
                        <p class="text-sm text-gray-500">Get notified when your account is accessed from a new device</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input 
                            type="checkbox" 
                            name="device_login_alerts" 
                            id="device_login_alerts"
                            value="1"
                            {{ old('device_login_alerts', $user->device_login_alerts ?? true) ? 'checked' : '' }}
                            class="sr-only peer"
                        >
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#0E7D34]/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0E7D34]"></div>
                    </label>
                </div>
            </div>

            <!-- Additional Notification Settings -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Notification Frequency</h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email Digest Frequency</label>
                        <select name="email_frequency" class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34] focus:border-[#0E7D34]">
                            <option value="immediate" {{ old('email_frequency', $user->email_frequency ?? 'immediate') == 'immediate' ? 'selected' : '' }}>Immediate</option>
                            <option value="daily" {{ old('email_frequency', $user->email_frequency ?? 'immediate') == 'daily' ? 'selected' : '' }}>Daily Digest</option>
                            <option value="weekly" {{ old('email_frequency', $user->email_frequency ?? 'immediate') == 'weekly' ? 'selected' : '' }}>Weekly Digest</option>
                            <option value="never" {{ old('email_frequency', $user->email_frequency ?? 'immediate') == 'never' ? 'selected' : '' }}>Never</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Notification Categories -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Notification Categories</h3>
                
                <div class="space-y-4">
                    <!-- Account Activity -->
                    <div class="flex items-center justify-between py-3">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Account Activity</h4>
                            <p class="text-sm text-gray-500">Login attempts, password changes, profile updates</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input 
                                type="checkbox" 
                                name="account_activity_notifications" 
                                value="1"
                                {{ old('account_activity_notifications', $user->account_activity_notifications ?? true) ? 'checked' : '' }}
                                class="sr-only peer"
                            >
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#0E7D34]/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0E7D34]"></div>
                        </label>
                    </div>

                    <!-- Transaction Alerts -->
                    <div class="flex items-center justify-between py-3">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Transaction Alerts</h4>
                            <p class="text-sm text-gray-500">Payment confirmations, transaction failures, refunds</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input 
                                type="checkbox" 
                                name="transaction_notifications" 
                                value="1"
                                {{ old('transaction_notifications', $user->transaction_notifications ?? true) ? 'checked' : '' }}
                                class="sr-only peer"
                            >
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#0E7D34]/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0E7D34]"></div>
                        </label>
                    </div>

                    <!-- System Updates -->
                    <div class="flex items-center justify-between py-3">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">System Updates</h4>
                            <p class="text-sm text-gray-500">Maintenance notifications, feature updates, announcements</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input 
                                type="checkbox" 
                                name="system_notifications" 
                                value="1"
                                {{ old('system_notifications', $user->system_notifications ?? false) ? 'checked' : '' }}
                                class="sr-only peer"
                            >
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#0E7D34]/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0E7D34]"></div>
                        </label>
                    </div>

                    <!-- Marketing Communications -->
                    <div class="flex items-center justify-between py-3">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Marketing Communications</h4>
                            <p class="text-sm text-gray-500">Promotional offers, newsletters, product updates</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input 
                                type="checkbox" 
                                name="marketing_notifications" 
                                value="1"
                                {{ old('marketing_notifications', $user->marketing_notifications ?? false) ? 'checked' : '' }}
                                class="sr-only peer"
                            >
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#0E7D34]/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0E7D34]"></div>
                        </label>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between pt-6">
                <button type="submit" class="bg-[#0E7D34] hover:bg-[#39A75E] text-white font-bold py-2 px-6 rounded focus:outline-none focus:ring-2 focus:ring-[#0E7D34]">
                    Save Preferences
                </button>
                
                <button type="button" class="text-sm text-gray-500 hover:text-gray-700" onclick="resetToDefaults()">
                    Reset to Defaults
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function resetToDefaults() {
    if (confirm('Are you sure you want to reset all notification preferences to default settings?')) {
        // Reset main toggles
        document.getElementById('email_notifications').checked = true;
        document.getElementById('sms_notifications').checked = false;
        document.getElementById('push_notifications').checked = true;
        document.getElementById('device_login_alerts').checked = true;
        
        // Reset category toggles
        document.querySelector('input[name="account_activity_notifications"]').checked = true;
        document.querySelector('input[name="transaction_notifications"]').checked = true;
        document.querySelector('input[name="system_notifications"]').checked = false;
        document.querySelector('input[name="marketing_notifications"]').checked = false;
        
        // Reset frequency
        document.querySelector('select[name="email_frequency"]').value = 'immediate';
    }
}
</script>


                    <!-- Support Section -->
                    <div id="support-content" class="settings-content hidden">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Support & Help</h2>
                            
                            <div class="space-y-6">
                                <div class="border-b pb-4">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Contact Support</h3>
                                    <p class="text-gray-600 mb-4">Need help? Get in touch with our support team.</p>
                                    <button class="bg-[#0E7D34] hover:bg-[#39A75E] text-white font-bold py-2 px-6 rounded focus:outline-none focus:ring-2 focus:ring-[#0E7D34]">
                                        Contact Support
                                    </button>
                                </div>
                                
                                <div class="border-b pb-4">
                                    <h3 class="text-lg font-semibold text -gray-900 mb-2">Frequently Asked Questions</h3>
                                    <p class="text-gray-600 mb-4">Find answers to common questions.</p>
                                    <a href="#" class="text-[#0E7D34] hover:text-[#39A75E] font-medium">View FAQ</a>
                                </div>
                                
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Documentation</h3>
                                    <p class="text-gray-600 mb-4">Learn how to use all features.</p>
                                    <a href="#" class="text-[#0E7D34] hover:text-[#39A75E] font-medium">View Documentation</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Tab Switching and Image Preview -->
    <script>
        // Tab switching functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.settings-tab');
            const contents = document.querySelectorAll('.settings-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all tabs
                    tabs.forEach(t => t.classList.remove('active', 'bg-[#0E7D34]', 'text-white'));
                    tabs.forEach(t => t.classList.add('text-gray-700'));
                    
                    // Add active class to clicked tab
                    this.classList.add('active', 'bg-[#0E7D34]', 'text-white');
                    this.classList.remove('text-gray-700');
                    
                    // Hide all content sections
                    contents.forEach(content => content.classList.add('hidden'));
                    
                    // Show corresponding content
                    const targetId = this.getAttribute('href').substring(1) + '-content';
                    const targetContent = document.getElementById(targetId);
                    if (targetContent) {
                        targetContent.classList.remove('hidden');
                    }
                });
            });
            
            // Set default active tab
            const defaultTab = document.querySelector('.settings-tab.active');
            if (defaultTab) {
                defaultTab.click();
            }
        });

        // Image preview functionality
        function previewImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('preview-img').src = e.target.result;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Modal functionality (if using Flowbite or similar)
        document.addEventListener('DOMContentLoaded', function() {
            const modalToggleButtons = document.querySelectorAll('[data-modal-toggle]');
            const modal = document.getElementById('crud-modal');
            
            modalToggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (modal) {
                        modal.classList.toggle('hidden');
                    }
                });
            });
            
            // Close modal when clicking outside
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        modal.classList.add('hidden');
                    }
                });
            }
        });

        // Dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const dropdownButton = document.getElementById('dropdownButton');
            const dropdown = document.getElementById('dropdown');
            
            if (dropdownButton && dropdown) {
                dropdownButton.addEventListener('click', function() {
                    dropdown.classList.toggle('hidden');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!dropdownButton.contains(e.target) && !dropdown.contains(e.target)) {
                        dropdown.classList.add('hidden');
                    }
                });
            }
        });
    </script>

    <!-- Additional CSS for better styling -->
    <style>
        .settings-tab.active {
            background-color: #0E7D34 !important;
            color: white !important;
        }
        
        .settings-tab.active svg {
            color: white !important;
        }
        
        .settings-content {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Loading spinner animation */
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .animate-spin {
            animation: spin 1s linear infinite;
        }
        
        /* Custom scrollbar for better UX */
        .settings-content::-webkit-scrollbar {
            width: 6px;
        }
        
        .settings-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        
        .settings-content::-webkit-scrollbar-thumb {
            background: #0E7D34;
            border-radius: 10px;
        }
        
        .settings-content::-webkit-scrollbar-thumb:hover {
            background: #39A75E;
        }
    </style>
</x-dashboard-layout>




