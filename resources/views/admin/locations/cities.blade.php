@section('title', 'Groups')

<x-dashboard-layout>

    {{-- message --}}


    {{$errors}}
  

    {{-- message --}}
    

    <div class="w-full px-4 py-8">
        <div class="max-w-full max-auto sm:px-6 lg:px-6">
            <x-message-status></x-message-status>

            <div class="rounded-2xl border border-gray-200 p-5">




    <div class="mb-4 border-b border-gray-200 dark:border-gray-700 w-full">
        <ul class="flex w-full -mb-px text-sm font-medium text-center" role="tablist">
            <li class="flex-1" role="presentation">
                <a href="{{route('admin.countries.index')}}" 
                class="w-full p-4 border-b-2 rounded-t-lg block text-center {{ request()->routeIs('admin.countries.index') ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-600 hover:border-gray-300' }}">
                Countries
                </a>
            </li>
            <li class="flex-1" role="presentation">
                <a href="{{route('admin.states.index')}}" 
                class="w-full p-4 border-b-2 rounded-t-lg block text-center {{ request()->routeIs('admin.states.index') ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-600 hover:border-gray-300' }}">
                States
                </a>
            </li>
            <li class="flex-1" role="presentation">
                <a href="{{route('admin.cities.index')}}" 
                class="w-full p-4 border-b-2 rounded-t-lg block text-center {{ request()->routeIs('admin.cities.index') ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-600 hover:border-gray-300' }}">
                Cities
                </a>
            </li>
        </ul>
    </div>

    <div class="p-4 rounded-lg bg-gray-50 dark:bg-gray-800" id="contributions-tab" role="tabpanel" aria-labelledby="profile-tab">
                    {{$dataTable->table()}}
    </div>




            



        </div>
    </div>



   

     






@push('scripts')
{{ $dataTable->scripts(attributes: ['type' => 'module']) }}
@endpush





</x-dashboard-layout>






