@section('title', 'Active Group')
<x-dashboard-layout>

<div class="max-w-full ">
    <div class="w-full p-6 mx-auto bg-[#CFEAD8]">
        @if (session('success'))
            <div class="relative px-4 py-3 mb-5 text-teal-700 bg-teal-100 border border-teal-400 rounded" role="alert">
                <strong class="font-bold">Success!</strong>
                <span class="block sm:inline">{{ session('success') }}</span>
                <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                    class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                    <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20">
                        <title>Close</title>
                        <path
                            d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                    </svg>
                </button>
            </div>
        @endif

        @if (session('error'))
            <div class="relative px-4 py-3 mb-5 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline">{{ session('error') }}</span>
                <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                    class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                    <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20">
                        <title>Close</title>
                        <path
                            d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                    </svg>
                </button>
            </div>
        @endif

            <div class="w-full mx-auto ">
                <div class="flex gap-4">
                    <div class="bg-white w-1/2 p-6 rounded-lg shadow-sm">
                        <!-- Group Information Section -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h2 class="text-2xl text-[#0E7D34] font-bold">{{ $group->group_name }}</h2>
                                </div>
                             

                            @if (auth()->user()?->isGroupAdmin() || auth()->user()?->user_type === 'admin')
                                <button onclick="toggleEditMode()" id="editButton"
                                    class="bg-[#0E7D34] hover:bg-[#0c6b2d] text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                    Edit Group
                                </button>
                            @endif

                            </div>

                            <!-- Group Stats -->
                            <div class="mb-6">
                                <div class="bg-white rounded-lg border border-gray-200 p-2">
                                    <div class="space-y-4">
                                        <div class="flex justify-between items-center  border-b border-gray-100">
                                            <span class="text-sm font-medium text-gray-600">Contribution Amount:</span>
                                            <span class="text-lg font-bold text-[#0E7D34]">₦{{ number_format($group->amount ?? 0, 2) }}</span>
                                        </div>

                                        <div class="flex justify-between items-center  border-b border-gray-100">
                                            <span class="text-sm font-medium text-gray-600">Frequency:</span>
                                            <span class="text-sm font-semibold text-gray-900">{{ $group->frequency ?? 'N/A' }}</span>
                                        </div>

                                        <div class="flex justify-between items-center  border-b border-gray-100">
                                            <span class="text-sm font-medium text-gray-600">Members:</span>
                                            <span class="text-sm font-semibold text-gray-900">
                                                {{ ($group->number_of_numbers ?? 0) }} / {{ ($group->max_number_of_member ?? 0) }}
                                                <span class="text-xs text-gray-500 ml-1">
                                                    ({{ $group?->max_number_of_member - $group?->number_of_numbers }} slots left)
                                                </span>
                                            </span>
                                        </div>

                                        <div class="flex justify-between items-center  border-b border-gray-100">
                                            <span class="text-sm font-medium text-gray-600">Order of Payout:</span>
                                            <span class="text-sm font-semibold text-gray-900">{{ $group->orderOfPayment ?? 'N/A' }}</span>
                                        </div>

                                        <div class="flex justify-between items-center border-b border-gray-100">
                                            <span class="text-sm font-medium text-gray-600">Payout Size:</span>
                                            <span class="text-lg font-bold text-[#0E7D34]">₦{{ number_format($group->payout_size ?? 0, 2) }}</span>
                                        </div>

                                        <div class="flex justify-between items-start ">
                                            <span class="text-sm font-medium text-gray-600">Group Link:</span>
                                            <div class="text-right max-w-xs">
                                                <div class="flex items-center space-x-2">
                                                    <input type="text"
                                                        id="groupLink" value="{{ $group->group_link }}"
                                                        class="text-xs text-blue-600 bg-gray-50 border border-gray-200 rounded px-2 py-1 w-full"
                                                        readonly>
                                                    <button onclick="copyGroupLink()"
                                                            class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-colors">
                                                        Copy
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Edit Group Form (Hidden by default) -->
                        <div id="editGroupForm" class="mb-6 hidden">
                            <div class="bg-gray-50 rounded-lg border border-gray-200 p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Edit Group Details</h3>
                                <form id="groupEditForm" action="{{ route('admin.groups.update', $group->id) }}" method="POST">
                                    @csrf
                                    @method('PUT')

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <!-- Group Name -->
                                        <div>
                                            <label for="group_name" class="block text-sm font-medium text-gray-700 mb-1">Group Name</label>
                                            <input type="text" id="group_name" name="group_name" value="{{ $group->group_name }}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34] focus:border-transparent">
                                        </div>

                                        <!-- Contribution Amount -->
                                        <div>
                                            <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">Contribution Amount</label>
                                            <input type="number" id="amount" name="amount" value="{{ $group->amount }}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34] focus:border-transparent">
                                        </div>

                                        <!-- Frequency -->
                                        <div>
                                            <label for="frequency" class="block text-sm font-medium text-gray-700 mb-1">Frequency</label>
                                            <select id="frequency" name="frequency"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34] focus:border-transparent">
                                                <option value="Weekly" {{ $group->frequency === 'Weekly' ? 'selected' : '' }}>Weekly</option>
                                                <option value="Monthly" {{ $group->frequency === 'Monthly' ? 'selected' : '' }}>Monthly</option>
                                                <option value="Biweekly" {{ $group->frequency === 'Biweekly' ? 'selected' : '' }}>Biweekly</option>
                                                <option value="Quarterly" {{ $group->frequency === 'Quarterly' ? 'selected' : '' }}>Quarterly</option>
                                                <option value="Yearly" {{ $group->frequency === 'Yearly' ? 'selected' : '' }}>Yearly</option>
                                            </select>
                                        </div>

                                        <!-- Max Members -->
                                        <div>
                                            <label for="max_number_of_member" class="block text-sm font-medium text-gray-700 mb-1">Max Members</label>
                                            <input type="number" id="max_number_of_member" name="max_number_of_member" value="{{ $group->max_number_of_member }}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34] focus:border-transparent">
                                        </div>

                                        <!-- Order of Payment -->
                                        <div>
                                            <label for="orderOfPayment" class="block text-sm font-medium text-gray-700 mb-1">Order of Payment</label>
                                            <select id="orderOfPayment" name="orderOfPayment"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34] focus:border-transparent">
                                                <option value="random" {{ $group->orderOfPayment === 'random' ? 'selected' : '' }}>Random</option>
                                                <option value="sequential" {{ $group->orderOfPayment === 'sequential' ? 'selected' : '' }}>Sequential</option>
                                                <option value="lottery" {{ $group->orderOfPayment === 'lottery' ? 'selected' : '' }}>Lottery</option>
                                            </select>
                                        </div>

                                        <!-- Status -->
                                        <div>
                                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                            <select id="status" name="status"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34] focus:border-transparent">
                                                <option value="pending" {{ $group->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                                <option value="active" {{ $group->status === 'active' ? 'selected' : '' }}>Active</option>
                                                <option value="banned" {{ $group->status === 'banned' ? 'selected' : '' }}>Banned</option>
                                                <option value="Closed" {{ $group->status === 'Closed' ? 'selected' : '' }}>Closed</option>
                                                <option value="suspended" {{ $group->status === 'suspended' ? 'selected' : '' }}>Suspended</option>
                                                <option value="deactivated" {{ $group->status === 'deactivated' ? 'selected' : '' }}>Deactivated</option>
                                            </select>
                                        </div>

                                        <!-- Currency -->
                                        <div>
                                            <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                                            <select id="currency" name="currency"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34] focus:border-transparent">
                                                <option value="Naira" {{ $group->currency === 'Naira' ? 'selected' : '' }}>Nigerian Naira (₦)</option>
                                                <option value="Yuan" {{ $group->currency === 'Yuan' ? 'selected' : '' }}>Chinese Yuan (¥)</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Group Rules -->
                                    <div class="mt-4">
                                        <label for="group_rules" class="block text-sm font-medium text-gray-700 mb-1">Group Rules</label>
                                        <textarea id="group_rules" name="group_rules" rows="4"
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34] focus:border-transparent"
                                                  placeholder="Enter group rules and guidelines...">{{ $group->group_rules }}</textarea>
                                    </div>

                                    <!-- Form Actions -->
                                    <div class="flex justify-end space-x-3 mt-6">
                                        <button type="button" onclick="cancelEdit()"
                                                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                                            Cancel
                                        </button>
                                        <button type="submit"
                                                class="px-4 py-2 bg-[#0E7D34] text-white rounded-md hover:bg-[#0c6b2d] transition-colors">
                                            Save Changes
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Tabbed Navigation -->
                        <div class="border-b border-gray-200 mb-6">
                            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                                <button onclick="switchTab('members')" id="members-tab" class="tab-button active border-transparent text-[#0E7D34] hover:text-[#39A75E] hover:border-[#39A75E] whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                    Members
                                </button>
                                 <button onclick="switchTab('payouts')" id="payouts-tab" class="tab-button border-transparent text-[#0E7D34] hover:text-[#39A75E] hover:border-[#39A75E] whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                    Payouts
                                </button>
                                <button onclick="switchTab('contributions')" id="contributions-tab" class="tab-button border-transparent text-[#0E7D34] hover:text-[#39A75E] hover:border-[#39A75E] whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                    Contributions
                                </button>
                                @if (auth()->user() && auth()->user()->user_type === 'admin')
                                 <button onclick="switchTab('group-admins')" id="group-admins-tab" class="tab-button border-transparent text-[#0E7D34] hover:text-[#39A75E] hover:border-[#39A75E] whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                    Group Admins
                                </button>
                                 <button onclick="switchTab('users')" id="users-tab" class="tab-button border-transparent text-[#0E7D34] hover:text-[#39A75E] hover:border-[#39A75E] whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                    Users
                                </button>
                                @endif
                                <button onclick="switchTab('rules')" id="rules-tab" class="tab-button border-transparent text-[#0E7D34] hover:text-[#39A75E] hover:border-[#39A75E] whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                    Group Rules
                                </button>
                            </nav>
                        </div>

                        <!-- Tab Content -->
                        <div id="tab-content">
                            <!-- Members Tab -->
                        <div id="members-content" class="tab-content">
                                <div class="space-y-6">
                                {{-- Admin Section --}}
                               

                                @if($admin)
                                <div class="flex items-center p-4 bg-white  rounded-lg ">
                                    <div class="relative mr-4">
                                        <div class="absolute left-9 top-0 w-3 h-3 rounded-full border-2 border-white {{ $admin->user->status === \App\Enums\UserStatus::Active ? 'bg-green-500' : 'bg-gray-400' }}"></div>
                                        <img class="w-14 h-14 rounded-full object-cover" src="{{ asset('storage/uploads/' . $admin->user->profileimages) }}" alt="{{ $admin->user->first_name }}">
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold text-gray-900">
                                            {{ $admin->user->first_name }} {{ $admin->user->last_name }}
                                        </h4>
                                        <p class="text-sm text-gray-500 font-medium">Admin</p>
                                    </div>
                                </div>
                                @endif

                                {{-- Other Users Section --}}
                                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                                    @forelse($groupMembers as $member)
                                    <div class="items-center p-2 rounded-lg">
                                            <div class="flex items-center">
                                                <div class="relative">
                                                        <!-- Active Status Dot -->
                                                    {{-- <div class="absolute left-8   w-3 h-3 rounded-full border-2 border-white {{ $member->is_active ? 'bg-green-500' : 'bg-gray-400' }}"></div> --}}
                                                    <div class="absolute left-8 w-3.5 h-3.5 rounded-full border-2 border-white
                                                        {{ $member->contributions && $member->contributions->where('status', 'approved')->count() > 0 ? 'bg-green-500' : 'bg-gray-400' }}">
                                                    </div>
                                                    <img class="w-12 h-12 rounded-full object-cover" src="{{ asset('storage/uploads/' . $member->user->profileimages) }}" alt="{{ $member->user->first_name }}">
                                                </div>
                                            </div>
                                            <div class="">
                                                    <h4 class="text-sm text-gray-900">{{ $member->user->first_name . '' . $member->user->last_name }}</h4>
                                            </div>
                                        </div>
                                    @empty
                                    <div class="text-center py-8 col-span-full">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">No members</h3>
                                        <p class="mt-1 text-sm text-gray-500">Get started by adding members to this group.</p>
                                    </div>
                                    @endforelse
                                </div>
                            </div>

                            {{-- Request --}}

                                    <h3 class="text-lg font-semibold mb-4">Join Requests</h3>

                                <div class="space-y-2">
                                @foreach ($group->members->where('status', 'pending') as $member)
                                    <div class="flex items-center justify-between p-4  rounded-md">
                                        <div class="flex items-center gap-4">
                                            <img src="{{ asset('storage/uploads/' . $member->user->profileimages) }}" class="w-12 h-12 rounded-full object-cover">
                                            <div>
                                                <div class="font-medium text-gray-900">{{ $member->user->first_name }} {{ $member->user->last_name }}</div>
                                                <div class="text-sm text-gray-500">{{ $member->user->email }}</div>
                                            </div>
                                        </div>

                                        <div class="flex">
                                        <!-- Approve Button -->
                                        <form action="{{ route('groups.members.approve', $member->id) }}" method="POST" class="inline-block">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="bg-green-600 text-white px-4 py-1 rounded hover:bg-green-700">
                                                Approve
                                            </button>
                                        </form>

                                        <!-- Decline Button -->
                                        <form action="{{ route('groups.members.decline', $member->id) }}" method="POST" class="inline-block ml-2">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="bg-red-600 text-white px-4 py-1 rounded hover:bg-red-700">
                                                Decline
                                            </button>
                                        </form>


                                        {{-- <form action="{{ route('groups.members.approve', $member->id) }}" method="POST">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="text-green-600 font-medium px-4 py-1 inline-block hover:text-green-700">
                                                Approve
                                                <div class="h-0.5 bg-green-500 mt-1"></div>
                                            </button>
                                        </form>

                                        <form action="{{ route('groups.members.decline', $member->id) }}" method="POST">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="text-red-600 font-medium px-4 py-1 inline-block hover:text-green-700">
                                                Decline
                                                <div class="h-0.5 bg-red-500 mt-1"></div>
                                            </button>
                                        </form> --}}


                                        

                                        

                                            
                                        </div>
                                    </div>
                                @endforeach
                                </div>




                            {{-- request end --}}

                            </div>

                            <!-- Contributions Tab -->
                            <div id="contributions-content" class="tab-content hidden">
                                <div class="space-y-2">
                                   <ol class="list-decimal">

                                      <!-- Contributions Tab -->
                                <div class="overflow-x-auto bg-white border border-gray-200 rounded-lg">
                                    @if($contributions->count())
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Member</th>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Status</th>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Proof of payment</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200">
                                            @foreach($contributions as $contribution)
                                            <tr class="hover:bg-gray-50">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        {{-- <img class="h-10 w-10 rounded-full object-cover mr-4" src="{{ asset('storage/uploads/' . $contribution->user->profileimages) }}" alt="User"> --}}
                                                        <div>
                                                            <div class="font-semibold text-gray-800">{{ $contribution->user->first_name }} {{ $contribution->user->last_name }}</div>
                                                            {{-- <div class="text-sm text-gray-500">{{ $contribution->user->email }}</div> --}}
                                                        </div>
                                                    </div>
                                                </td>
                                               
                                               
                                                <td class="px-6 py-4">
                                                    <span class="inline-block px-3 py-1 rounded-full text-xs font-semibold 
                                                        @if($contribution->status === 'completed') bg-green-100 text-green-700
                                                        @elseif($contribution->status === 'pending') bg-yellow-100 text-yellow-700
                                                        @elseif($contribution->status === 'rejected') bg-red-100 text-red-700
                                                        @else bg-gray-100 text-gray-700
                                                        @endif">
                                            {{ ucfirst($contribution->status->value) }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 text-sm">
                                                    @if($contribution->payment_proof)
                                                    <div class="flex items-center p-1">
                                                   <svg xmlns="http://www.w3.org/2000/svg" width="11" height="12" viewBox="0 0 11 12" fill="none">
                                                    <path d="M6.55401 3.63305L4.00135 6.13571C3.75868 6.37571 3.62535 6.70371 3.63068 7.04838C3.63795 7.39705 3.78155 7.729 4.03068 7.97305C4.28068 8.22038 4.61735 8.36238 4.96601 8.36838C5.1367 8.37197 5.30638 8.34138 5.46506 8.27841C5.62375 8.21544 5.76823 8.12136 5.89001 8.00171L8.44201 5.49905C8.68277 5.26087 8.87263 4.97626 9.00007 4.66249C9.1275 4.34871 9.18985 4.01232 9.18334 3.67371C9.16932 2.97633 8.88261 2.31219 8.38468 1.82371C7.88537 1.32962 7.21501 1.04672 6.51268 1.03371C6.17167 1.02691 5.83273 1.08814 5.51566 1.21382C5.19859 1.3395 4.90974 1.52712 4.66601 1.76571L2.11201 4.26905C1.75115 4.62649 1.46659 5.05345 1.27556 5.52408C1.08454 5.99471 0.991021 6.49922 1.00068 7.00705C1.02215 8.05288 1.45244 9.04868 2.19935 9.78105C2.94792 10.522 3.95293 10.9464 5.00601 10.9664C5.51772 10.977 6.02641 10.8855 6.50228 10.6971C6.97816 10.5086 7.41164 10.2271 7.77735 9.86905L10.33 7.36505" stroke="#131313" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                        <a href="{{ asset('storage/' . $contribution->payment_proof) }}" target="_blank" class="text-green-600 underline hover:underline">
                                                            View Screenshot
                                                        </a>
                                                    </div>
                                                   
                                                    @else
                                                        <span class="text-gray-400">No proof</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>

                                    {{-- Optional pagination --}}
                                    @if(method_exists($contributions, 'links'))
                                    <div class="p-4">
                                        {{ $contributions->links() }}
                                    </div>
                                    @endif

                                    @else
                                    <div class="p-6 text-center text-gray-500">No contributions found.</div>
                                    @endif
                                </div>
    


                                  
                                    </ol>
                                </div>
                            </div>

                            {{--  --}}
                          

                            <!-- Payouts Tab -->
                            <div id="payouts-content" class="tab-content hidden">
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                        <div class="flex items-center">
                                            <div>
                                                <h4 class="font-medium text-gray-900">Wallet Balance</h4>
                                               @if(isset($userPayout) && $userPayout)
                                                <p class="text-sm text-gray-600">
                                                    #{{ number_format($userPayout->amount, 2) }}
                                                </p>
                                                <p>
                                                    <span class="
                                                        @if($userPayout->status === 'completed') text-green-600
                                                        @elseif($userPayout->status === 'pending') text-yellow-600
                                                        @elseif($userPayout->status === 'failed') text-red-600
                                                        @else text-gray-600
                                                        @endif
                                                    ">
                                                        Status: {{ ucfirst($userPayout->status) }}
                                                    </span>
                                                </p>
                                            @else
                                                <p class="text-sm text-gray-600">#0.00</p>
                                                <p><span class="text-gray-500">Status: No payout yet</span></p>
                                            @endif

                                            </div>
                                        </div>
                                        {{-- @if($userPayout->status === 'pending')
                                        <div class="text-right">
                                            <button data-modal-target="popup-modal" data-modal-toggle="popup-modal" class="block text-white bg-gray-500 focus:ring-4 focus:outline-none  font-medium rounded-lg text-sm px-5 py-2.5 text-center " type="button" disabled>
                                            Request Payout
                                            </button>
                                        </div>
                                        @else
                                            <div class="text-right">
                                            <button data-modal-target="popup-modal" data-modal-toggle="popup-modal" class="block text-white bg-[#0E7D34] hover:bg-[#39A75E] focus:ring-4 focus:outline-none  font-medium rounded-lg text-sm px-5 py-2.5 text-center " type="button">
                                            Request Payout
                                            </button>
                                        </div>
                                        @endif --}}
                                        <div id="popup-modal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                            <div class="relative p-4 w-full max-w-md max-h-full">
                                                <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
                                                    <button type="button" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="popup-modal">
                                                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                                                        </svg>
                                                        <span class="sr-only">Close modal</span>
                                                    </button>
                                                    <div class="p-4 md:p-5 text-center">
                                                        <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                                                        </svg>
                                                        <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Are you sure you want to delete this product?</h3>
                                                        <button data-modal-hide="popup-modal" type="button" class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center">
                                                            Yes, I'm sure
                                                        </button>
                                                        <button data-modal-hide="popup-modal" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">No, cancel</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <h4 class="font-medium text-gray-900">Payout Option</h4>
                                    </div>
                                          <!-- Payout Tab -->
                                <div class="overflow-x-auto bg-white border border-gray-200 rounded-lg">
                                @if($payoutLists->count())
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Name</th>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Trust Score</th>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Status</th>
                                                <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase">Date</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200">
                                            @foreach($payoutLists as $payout)
                                                <tr class="hover:bg-gray-50">
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                     
                                                       <div class="flex items-center space-x-2 font-semibold text-gray-800">
                                                        <span class="w-3.5 h-3.5 rounded-full border-2 border-white 
                                                            {{ $member->contributions && $member->contributions->where('status', 'approved')->count() > 0 ? 'bg-green-500' : 'bg-gray-400' }}">
                                                        </span>
                                                        <span>
                                                            {{ $payout->user->first_name }} {{ $payout->user->last_name }}
                                                        </span>
                                                    </div>

                                                        
                                                    </td>

                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {{ $payout->trust_score ?? 'N/A' }}
                                                    </td>

                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span class="inline-block px-3 py-1 rounded-full text-xs font-semibold 
                                                            @if($payout->status === 'completed') bg-green-100 text-green-700
                                                            @elseif($payout->status === 'pending') bg-yellow-100 text-yellow-700
                                                            @elseif($payout->status === 'rejected') bg-red-100 text-red-700
                                                            @else bg-gray-100 text-gray-700
                                                            @endif">
                                                            {{ ucfirst($payout->status->value) }}
                                                        </span>
                                                    </td>

                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {{ $payout->created_at->format('M d, Y h:i A') }}
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>

                                    @if(method_exists($payoutLists, 'links'))
                                        <div class="p-4">
                                            {{ $payoutLists->links() }}
                                        </div>
                                    @endif
                                @else
                                    <div class="p-6 text-center text-gray-500">No payouts found.</div>
                                @endif
                            </div>

                                {{--  --}}

                                </div>
                            </div>

                            {{-- Group Admin --}}
                             @if (auth()->user() && auth()->user()->user_type === 'admin')
    

                              <div id="group-admins-content" class="tab-content hidden">
                            <div class="space-y-4">
                                <h1 class="text-lg font-medium text-gray-900 mb-2">Group Management</h1>

                                @forelse($groupMembers as $member)
                                    <div class="flex items-center justify-between p-2 bg-white rounded-lg shadow-sm">
                                        <!-- Left: Avatar + Name -->
                                        <div class="flex items-center space-x-4">
                                            <div class="relative">
                                                <!-- Active Status Dot -->
                                                <div class="absolute left-8 w-3.5 h-3.5 rounded-full border-2 border-white
                                                    {{ $member->contributions && $member->contributions->where('status', 'approved')->count() > 0 ? 'bg-green-500' : 'bg-gray-400' }}">
                                                </div>
                                                <img class="w-12 h-12 rounded-full object-cover" src="{{ asset('storage/uploads/' . $member->user->profileimages) }}" alt="{{ $member->user->first_name }}">
                                            </div>
                                            <h4 class="text-sm text-gray-900">
                                                {{ $member->user->first_name . ' ' . $member->user->last_name }}
                                            </h4>
                                        </div>

                                        <!-- Right: Toggle Admin Button -->
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="checkbox"
                                                class="sr-only peer admin-toggle"
                                                data-user="{{ $member->user->id }}"
                                                data-group="{{ $member->group_id }}"
                                                {{ \App\Models\GroupAdmin::where('group_id', $member->group_id)->where('user_id', $member->user->id)->exists() ? 'checked' : '' }}>
                                            <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 dark:bg-gray-700 
                                                peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full 
                                                peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] 
                                                after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all 
                                                dark:border-gray-600 peer-checked:bg-blue-600 dark:peer-checked:bg-blue-600">
                                            </div>
                                        </label>
                                    </div>
                                @empty
                                    <div class="text-center py-8 col-span-full">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">No members</h3>
                                        <p class="mt-1 text-sm text-gray-500">Get started by adding members to this group.</p>
                                    </div>
                                @endforelse
                            </div>


                        </div>

                        {{-- users start --}}


                              <div id="users-content" class="tab-content hidden">
                            <div class="space-y-4">
                                <h1 class="text-lg font-medium text-gray-900 mb-2">Users Managment</h1>

                                @forelse($groupMembers as $member)
                                    <div class="flex items-center justify-between p-2 bg-white rounded-lg shadow-sm">
                                        <!-- Left: Avatar + Name -->
                                        <div class="flex items-center space-x-4">
                                            <div class="relative">
                                                <!-- Active Status Dot -->
                                                <div class="absolute left-8 w-3.5 h-3.5 rounded-full border-2 border-white
                                                    {{ $member->contributions && $member->contributions->where('status', 'approved')->count() > 0 ? 'bg-green-500' : 'bg-gray-400' }}">
                                                </div>
                                                <img class="w-12 h-12 rounded-full object-cover" src="{{ asset('storage/uploads/' . $member->user->profileimages) }}" alt="{{ $member->user->first_name }}">
                                            </div>
                                            <h4 class="text-sm text-gray-900">
                                                {{ $member->user->first_name . ' ' . $member->user->last_name }}
                                            </h4>
                                        </div>

                                        <!-- Right: Toggle Admin Button -->
                                       <div class="flex space-x-2">
                <!-- Suspend/Reactivate Button -->
                @if($member->status === 'suspended' || $member->status === \App\Enums\GroupStatus::Suspended)
                    <!-- Reactivate Button -->
                    <form action="{{ route('admin.groups.members.reactivate', [$group->id, $member->id]) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit"
                                class="px-4 py-2 text-sm font-medium text-green-700 bg-green-100 border border-green-300 rounded-lg hover:bg-green-200 dark:bg-green-800 dark:text-green-100 dark:border-green-600 dark:hover:bg-green-700"
                                onclick="return confirm('Are you sure you want to reactivate this member in the group?')">
                            Reactivate
                        </button>
                    </form>
                @else
                    <!-- Suspend Button -->
                    <form action="{{ route('admin.groups.members.suspend', [$group->id, $member->id]) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit"
                                class="px-4 py-2 text-sm font-medium text-yellow-700 bg-yellow-100 border border-yellow-300 rounded-lg hover:bg-yellow-200 dark:bg-yellow-800 dark:text-yellow-100 dark:border-yellow-600 dark:hover:bg-yellow-700"
                                onclick="return confirm('Are you sure you want to suspend this member from the group?')">
                            Suspend
                        </button>
                    </form>
                @endif

                <!-- Delete Button -->
                <form action="{{ route('admin.users.destroy', $user->id) }}" method="POST" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-red-700 bg-red-100 border border-red-300 rounded-lg hover:bg-red-200 dark:bg-red-800 dark:text-red-100 dark:border-red-600 dark:hover:bg-red-700"
                            onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                        Delete
                    </button>
                </form>
            </div>
                                    </div>
                                @empty
                                    <div class="text-center py-8 col-span-full">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">No members</h3>
                                        <p class="mt-1 text-sm text-gray-500">Get started by adding members to this group.</p>
                                    </div>
                                @endforelse
                            </div>


                        </div>


                        {{-- users end --}}
                            @endif

                            {{-- Group aDMIN END --}}

                            <!-- Settings Tab -->
                            <div id="rules-content" class="tab-content hidden">
                                <div class="space-y-6">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 mb-2">Group Rules</h3>
                                        <div class="space-y-4">
                                            {{($group->group_rules ?? '')}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



      {{-- chat --}}
                    <div class="bg-white w-1/2 rounded-lg shadow-lg flex flex-col ">
    <!-- Chat Header -->
    <div class="text-[#117D34] p-4 rounded-t-lg">
        <h3 class="font-semibold">Chat</h3>
        {{-- <h3 class="font-semibold">Group name: {{ $group->group_name }}</h3> --}}
        {{-- <p class="text-sm opacity-90">Members: {{ $groupMembers->count() }} member</p> --}}
    </div>

    <!-- Messages Container -->
    <div id="messagesContainer" class="flex-1 overflow-y-auto p-4 space-y-3 bg-gray-50">
        @foreach($messages as $message)
            <div class="flex {{ $message->user_id == auth()->id() ? 'justify-end' : 'justify-start' }}">
                <div class="max-w-xs lg:max-w-md">
                    <!-- Message bubble -->
                    <img src="{{ $message->user->profile_picture }}" alt="User Avatar" class="w-10 h-10 rounded-full mr-2">
                    <div class="px-4 py-2 rounded-lg {{ $message->user_id == auth()->id() ? 'bg-[#CFEAD8] text-gray-600' : 'bg-white border' }}">
                        @if($message->type == 'payment')
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                                </svg>
                                <span class="font-medium">Payment Made</span>
                            </div>
                        @endif
                        
                        <p class="text-sm">{{ $message->message }}</p>
                        
                        @if($message->metadata)
                            <div class="mt-2 text-xs opacity-75">
                                @if($message->type == 'payment' && isset($message->metadata['amount']))
                                    Amount: ${{ number_format($message->metadata['amount'], 2) }}
                                @endif
                            </div>
                        @endif
                    </div>
                    
                    <!-- Message info -->
                    <div class="flex items-center mt-1 space-x-2 text-xs text-gray-500">
                        <span>{{ $message->user->first_name }}</span>
                        <span>•</span>
                        <span>{{ $message->created_at->format('H:i') }}</span>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Message Input -->
    <div class=" p-4 bg-white rounded-b-lg">
        <form id="messageForm" class="flex space-x-2">
            @csrf
            <input type="hidden" name="group_id" value="{{ $group->id }}">
            
           
            
            <!-- Message Input -->
            <input type="text" 
                   name="message" 
                   id="messageInput"
                   placeholder="Start Typing Here.." 
                   class="flex-1 px-3 py-2 bg-gray-100  rounded-lg focus:outline-none focus:ring-2 focus:ring-[#117D34]"
                   required>
            
            <!-- Send Button -->
            <button type="submit" 
                    class="px-4 py-2 bg-[#117D34] text-white rounded-lg hover:bg-[#0f6b2d] transition-colors">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                </svg>
            </button>
        </form>
    </div>
</div>

     {{-- chat --}}
                </div>
            </div>
    </div>
</div>


<script>
function switchTab(tabName) {
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active', 'border-[#0E7D34]', 'text-[#0E7D34]');
        button.classList.add('border-transparent', 'text-gray-500');
    });

    // Hide all tab content
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });

    // Add active class to clicked tab button
    const activeButton = document.getElementById(tabName + '-tab');
    if (activeButton) {
        activeButton.classList.add('active', 'border-[#0E7D34]', 'text-[#0E7D34]');
        activeButton.classList.remove('border-transparent', 'text-gray-500');
    }

    // Show corresponding tab content
    const activeContent = document.getElementById(tabName + '-content');
    if (activeContent) {
        activeContent.classList.remove('hidden');
    }
}

// Initialize the first tab as active when page loads
document.addEventListener('DOMContentLoaded', function() {
    switchTab('members');
});
</script>




<script>
document.addEventListener('DOMContentLoaded', function() {
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');
    const uploadContent = document.getElementById('uploadContent');
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const removeFile = document.getElementById('removeFile');
    const progressContainer = document.getElementById('progressContainer');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    const successMessage = document.getElementById('successMessage');
    const submitBtn = document.getElementById('submitBtn');
    const submitSpinner = document.getElementById('submitSpinner');
    const submitText = document.getElementById('submitText');
    const form = document.getElementById('proofUploadForm');

    // Click to upload
    dropZone.addEventListener('click', () => fileInput.click());

    // Drag and drop functionality
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('border-[#0E7D34]', 'bg-green-50');
    });

    dropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropZone.classList.remove('border-[#0E7D34]', 'bg-green-50');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('border-[#0E7D34]', 'bg-green-50');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    });

    // File input change
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFile(e.target.files[0]);
        }
    });

    // Remove file
    removeFile.addEventListener('click', (e) => {
        e.stopPropagation();
        resetUpload();
    });

    // Handle file selection
    function handleFile(file) {
        // Validate file
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf'];

        if (!allowedTypes.includes(file.type)) {
            showError('Please select a PNG, JPG, or PDF file.');
            return;
        }

        if (file.size > maxSize) {
            showError('File size must be less than 10MB.');
            return;
        }

        // Hide error and show file preview
        hideError();
        uploadContent.classList.add('hidden');
        filePreview.classList.remove('hidden');
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);

        // Enable submit button
        enableSubmitButton();
    }

    // Form submission with progress
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!fileInput.files[0]) {
            showError('Please select a file to upload.');
            return;
        }

        const formData = new FormData(form);

        // Show progress and disable button
        showProgress();
        disableSubmitButton();

        // Simulate upload progress (replace with actual XMLHttpRequest for real progress)
        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = Math.round((e.loaded / e.total) * 100);
                updateProgress(percentComplete);
            }
        });

        xhr.addEventListener('load', function() {
            if (xhr.status === 200) {
                hideProgress();
                showSuccess();
                resetUpload();
            } else {
                hideProgress();
                showError('Upload failed. Please try again.');
                enableSubmitButton();
            }
        });

        xhr.addEventListener('error', function() {
            hideProgress();
            showError('Upload failed. Please try again.');
            enableSubmitButton();
        });

        xhr.open('POST', form.action);
        xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
        xhr.send(formData);
    });

    // Helper functions
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function showError(message) {
        errorText.textContent = message;
        errorMessage.classList.remove('hidden');
        successMessage.classList.add('hidden');
    }

    function hideError() {
        errorMessage.classList.add('hidden');
    }

    function showSuccess() {
        successMessage.classList.remove('hidden');
        errorMessage.classList.add('hidden');
    }

    function showProgress() {
        progressContainer.classList.remove('hidden');
    }

    function hideProgress() {
        progressContainer.classList.add('hidden');
        updateProgress(0);
    }

    function updateProgress(percent) {
        progressBar.style.width = percent + '%';
        progressText.textContent = percent + '%';
    }

    function enableSubmitButton() {
        submitBtn.disabled = false;
        submitBtn.classList.remove('bg-gray-400', 'cursor-not-allowed');
        submitBtn.classList.add('bg-[#0E7D34]', 'hover:bg-[#39A75E]');
    }

    function disableSubmitButton() {
        submitBtn.disabled = true;
        submitBtn.classList.add('bg-gray-400', 'cursor-not-allowed');
        submitBtn.classList.remove('bg-[#0E7D34]', 'hover:bg-[#39A75E]');
        submitSpinner.classList.remove('hidden');
        submitText.textContent = 'Uploading...';
    }

    function resetUpload() {
        fileInput.value = '';
        uploadContent.classList.remove('hidden');
        filePreview.classList.add('hidden');
        hideProgress();
        hideError();
        submitBtn.disabled = true;
        submitBtn.classList.add('bg-gray-400', 'cursor-not-allowed');
        submitBtn.classList.remove('bg-[#0E7D34]', 'hover:bg-[#39A75E]');
        submitSpinner.classList.add('hidden');
        submitText.textContent = 'Submit Proof';
    }

    // Initialize button state
    resetUpload();
});
</script>


<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        toast.textContent = 'Account number copied to clipboard!';
        document.body.appendChild(toast);

        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
    });
}
</script>


<script>
function copyGroupLink() {
    const linkInput = document.getElementById('groupLink');
    linkInput.select();
    linkInput.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        // Show success message
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.classList.add('bg-green-500');
        button.classList.remove('bg-blue-500');

        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('bg-green-500');
            button.classList.add('bg-blue-500');
        }, 2000);
    } catch (err) {
        console.error('Failed to copy: ', err);
    }
}
</script>


{{-- group message --}}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageForm = document.getElementById('messageForm');
    const messageInput = document.getElementById('messageInput');
    const messagesContainer = document.getElementById('messagesContainer');
    const groupId = {{ $group->id }};
    const userId = {{ $user->id }}; // ✅ Add this missing variable

    // Auto-scroll to bottom
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // Add message to chat
    function addMessage(message) {
        const isOwn = message.user_id == {{ auth()->id() }};
        const messageHtml = `
            <div class="flex ${isOwn ? 'justify-end' : 'justify-start'}">
                <div class="max-w-xs lg:max-w-md">
                    <div class="px-4 py-2 rounded-lg ${isOwn ? 'bg-[#CFEAD8] text-gray-600' : 'bg-white border'}">
                        ${message.type === 'payment' ? `
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                                </svg>
                                <span class="font-medium">Payment Made</span>
                            </div>
                        ` : ''}
                        <p class="text-sm">${message.message}</p>
                        ${message.metadata && message.type === 'payment' && message.metadata.amount ? `
                            <div class="mt-2 text-xs opacity-75">
                                Amount: $${parseFloat(message.metadata.amount).toFixed(2)}
                            </div>
                        ` : ''}
                    </div>
                    <div class="flex items-center mt-1 space-x-2 text-xs text-gray-500">
                        <span>${message.user.first_name}</span>
                        <span>•</span>
                        <span>Just now</span>
                    </div>
                </div>
            </div>
        `;
        
        messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
        scrollToBottom();
    }

    // Send message
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const messageText = messageInput.value.trim();
        if (!messageText) return;

        messageInput.disabled = true;
        
        const formData = new FormData();
        formData.append('message', messageText);
        formData.append('_token', document.querySelector('input[name="_token"]').value);
        
        fetch(`/admin/users/${userId}/groups/${groupId}/messages`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addMessage(data.message);
                messageInput.value = '';
            } else {
                alert('Failed to send message');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to send message');
        })
        .finally(() => {
            messageInput.disabled = false;
            messageInput.focus();
        });
    });

    // Allow sending with Enter key
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            messageForm.dispatchEvent(new Event('submit'));
        }
    });

    // Poll for new messages every 5 seconds
    let lastMessageCount = {{ $messages->count() }};
    
    setInterval(function() {
        fetch(`/admin/users/${userId}/groups/${groupId}/messages`)
            .then(response => response.json())
            .then(messages => {
                if (messages.length > lastMessageCount) {
                    location.reload();
                }
            })
            .catch(error => console.error('Error fetching messages:', error));
    }, 5000);

    // Initial scroll to bottom
    scrollToBottom();
    
    // Focus on input
    messageInput.focus();
});


// make admin

    document.querySelectorAll('.admin-toggle').forEach(toggle => {
        toggle.addEventListener('change', function () {
            const userId = this.dataset.user;
            const groupId = this.dataset.group;

            fetch(`/admin/groups/${groupId}/users/${userId}/toggle-admin`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId,
                    group_id: groupId,
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Toggle response:', data.message);
                    // Show success message
                    showNotification(data.message, 'success');
                } else {
                    console.error('Toggle failed:', data.message);
                    // Revert toggle state
                    this.checked = !this.checked;
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Toggle error:', error);
                // Revert toggle state
                this.checked = !this.checked;
                showNotification('An error occurred while toggling admin rights', 'error');
            });
        });
    });

    $(document).ready(function () {
        $('.admin-toggle').change(function () {
            const userId = $(this).data('user');
            const groupId = $(this).data('group');
            const isChecked = $(this).is(':checked');

            $.ajax({
                url: `/admin/groups/${groupId}/users/${userId}/toggle-admin`,
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function (response) {
                    console.log(response.message);
                },
                error: function (xhr) {
                    alert('Something went wrong');
                    console.log(xhr.responseText);
                }
            });
        });
    });

    // Edit Group Functions
    function toggleEditMode() {
        const editForm = document.getElementById('editGroupForm');
        const editButton = document.getElementById('editButton');

        if (editForm.classList.contains('hidden')) {
            editForm.classList.remove('hidden');
            editButton.innerHTML = `
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Cancel Edit
            `;
        } else {
            editForm.classList.add('hidden');
            editButton.innerHTML = `
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Group
            `;
        }
    }

    function cancelEdit() {
        toggleEditMode();
    }

    // Handle form submission with AJAX
    document.getElementById('groupEditForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;

        // Show loading state
        submitButton.disabled = true;
        submitButton.textContent = 'Saving...';

        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json().then(data => ({
                status: response.status,
                data: data
            }));
        })
        .then(({status, data}) => {
            console.log('Response data:', data);

            if (status === 200 && data.success) {
                showNotification(data.message, 'success');
                // Hide edit form
                toggleEditMode();
                // Reload page to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else if (status === 422) {
                // Validation errors
                let errorMessage = 'Validation failed: ';
                if (data.errors) {
                    errorMessage += Object.values(data.errors).flat().join(', ');
                } else {
                    errorMessage += data.message || 'Please check your input';
                }
                showNotification(errorMessage, 'error');
            } else {
                showNotification(data.message || 'Failed to update group', 'error');
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            showNotification('Network error occurred while updating the group', 'error');
        })
        .finally(() => {
            // Reset button state
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        });
    });

    // Notification function
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-md shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

</script>



        

</x-dashboard-layout>







