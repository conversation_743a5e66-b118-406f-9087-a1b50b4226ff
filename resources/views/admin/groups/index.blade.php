@section('title', 'Groups')

<x-dashboard-layout>

    {{-- message --}}


    {{$errors}}
    {{-- @if (session('success'))
            <div class="relative px-4 py-3 mb-5 text-teal-700 bg-teal-100 border border-teal-400 rounded" role="alert">
                <strong class="font-bold">Success!</strong>
                <span class="block sm:inline">{{ session('success') }}</span>
                <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                    class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                    <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20">
                        <title>Close</title>
                        <path
                            d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                    </svg>
                </button>
            </div>
        @endif

        @if (session('error'))
            <div class="relative px-4 py-3 mb-5 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline">{{ session('error') }}</span>
                <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                    class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                    <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20">
                        <title>Close</title>
                        <path
                            d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                    </svg>
                </button>
            </div>
        @endif --}}

    {{-- message --}}
    

    <div class="w-full px-4 py-8">
        <div class="max-w-full max-auto sm:px-6 lg:px-6">
            <x-message-status></x-message-status>

            <div class="rounded-2xl border border-gray-200 p-5">

            <div class="flex justify-end mb-4">
                <!-- Modal toggle -->
                <button data-modal-target="authentication-modal" data-modal-toggle="authentication-modal"
                class="flex items-center gap-2 text-white bg-[#0E7D34] hover:bg-[#0E7D34] focus:ring-4 focus:outline-none focus:ring-[#0E7D34] font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-[#0E7D34] dark:hover:bg-[#0E7D34] dark:focus:ring-[#0E7D34]"
                type="button">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"></path>
                </svg>
                Create Groups
            </button>

            </div>

                {{$dataTable->table()}}

            </div>

        </div>
    </div>



    {{-- modal --}}

    <!-- create group modal -->
<div id="authentication-modal" tabindex="-1" aria-hidden="true" class="hidden mt-6 overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-lg max-h-full">
        
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Create a Group
                </h3>
                <button type="button" class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="authentication-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="p-4 md:p-5">
                <form  class="space-y-4" id="create-group-form" action="{{ route('admin.groups.store') }}" method="POST">
                    @csrf
                    <div>
                        <label for="group_name"  class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Group Name</label>
                        <input type="group_name" value="{{old('group_name')}}" name="group_name" id="group_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Type the name of your group here"  />
                    </div>
                    <x-input-error :messages="$errors->get('group_name')" class="mt-2" />

                    <div>
                        <label for="amount" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Contribution Amount</label>
                        <input type="number" name="amount" value="{{old('amount')}}" id="amount" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Type the contribution amount here eg. #10,000"  />
                    </div>
                    <x-input-error :messages="$errors->get('amount')" class="mt-2" />

                    <div class="grid grid-cols-2 gap-4 md:grid-cols-2 lg:grid-cols-2">
                        <div>
                            <label for="frequency" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Frequency</label>
                            <select id="frequency" name="frequency" value="{{old('frequency')}}" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white">
                                <option>Select</option>
                               @foreach (\App\Enums\Frequency::cases() as $frequency)
                                <option value="{{ $frequency->value }}" {{ old('frequency') == $frequency->value ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('-', ' ', $frequency->value)) }}
                                </option>
                            @endforeach
                            </select>
                        <x-input-error :messages="$errors->get('frequency')" class="mt-2" />


                        </div>

                        <div>
                            <label for="currency" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Currency</label>
                            <select id="currency" name="currency" value="{{old('currency')}}" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white">
                                <option>Select</option>
                                @foreach (\App\Enums\Currency::cases() as $currency) 
                                <option value="{{ $currency->value }}" {{ old('currency') == $currency->value ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('-', ' ', $currency->value)) }}
                                </option>
                                @endforeach
                            </select>
                                <x-input-error :messages="$errors->get('currency')" class="mt-2" />

                        </div>

                    </div>
                    {{--  --}}
                    <div class="grid grid-cols-2 gap-4 md:grid-cols-2 lg:grid-cols-2">
                    <div>
                        <label for="max_number_of_member" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Number of members</label>
                        <input type="max_number_of_member" value="{{old('max_number_of_member')}}" name="max_number_of_member" id="max_number_of_member" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Type the contribution amount here eg. #10,000"  />
                        <x-input-error :messages="$errors->get('max_number_of_member')" class="mt-2" />

                    </div>

                      <div>
                            <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Staus</label>
                            <select id="status" name="status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white">
                            <option value="">Select</option>
                            @foreach (\App\Enums\GroupStatus::cases() as $status) 
                                <option value="{{ $status->value }}" {{ old('status') == $status->value ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('-', ' ', $status->value)) }}
                                </option>
                            @endforeach
                        </select>
                            <x-input-error :messages="$errors->get('currency')" class="mt-2" />

                        </div>

                    </div>
                   
                    <div>
                        <label for="group_rules" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Group rules</label>
                        <input type="group_rules" name="group_rules" value="{{old('group_rules')}}" id="group_rules" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Type the contribution amount here eg. #10,000"  />
                    </div>
                    <x-input-error :messages="$errors->get('group_rules')" class="mt-2" />
                     <div>
                        <label for="orderOfPayment" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Order of payment</label>
                        <input type="orderOfPayment" value="{{old('orderOfPayment')}}" name="orderOfPayment" id="orderOfPayment" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Type the contribution amount here eg. #10,000"  />
                    </div>
                    <x-input-error :messages="$errors->get('orderOfPayment')" class="mt-2" />

                       
                   
                    <div class="flex justify-end">
                    <button type="submit" class="flex justify-content-end text-white bg-[#0E7D34] hover:bg-[#0E7D34] focus:ring-4 focus:outline-none focus:ring-[#0E7D34] font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-[#0E7D34] dark:hover:bg-[#0E7D34] dark:focus:ring-[#0E7D34]">Create Group</button>

                    </div>
                    
                </form>
            </div>
        </div>
    </div>
</div> 
    {{-- close create group modal --}}



<!-- Trigger (invisible) -->
<button id="triggerEditModal" data-modal-target="editModal" data-modal-toggle="editModal" class="hidden"></button>

<!-- Flowbite Modal -->
<div id="editModal" tabindex="-1" aria-hidden="true"
     class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-auto overflow-y-auto h-full">
    <div class="relative w-full max-w-md mx-auto mt-10">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Edit Group</h3>
                <button type="button" class="text-gray-400 hover:bg-gray-200 hover:text-gray-900 rounded-lg w-8 h-8"
                        data-modal-hide="editModal">
                    &times;
                </button>
            </div>
            <div class="p-6 space-y-4">
                <div id="modal-content">
                    {{-- form --}}


             


                    {{-- form --}}
                </div>
            </div>
        </div>
    </div>
</div>



    {{-- edit --}}


     
{{-- success group modal --}}

<div id="group-success-modal" tabindex="-1" class="hidden fixed inset-0 z-50 flex items-center justify-center backdrop-blur-xs bg-white/10">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6 dark:bg-gray-800">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">🎉 Group Created Successfully</h3>
        
        <p class="text-sm text-gray-600 dark:text-gray-300 mb-6">
            Your group has been created. You can now manage contributions and invite members.
        </p>

        <div class="flex justify-between items-center gap-3">
            <a  href="{{route('admin.groups.index')}}" class="w-full text-center bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm">
                Go to Group
            </a>
           <button onclick="copyGroupLink(this)" type="button"
                data-link="{{ session('group_url') }}" id="goToGroupBtn"
                class="w-full cursor-pointer text-center bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 text-sm">
                Copy Group Link
            </button>
        </div>
        
        
    </div>
</div>


    {{--  --}}


@push('scripts')
{{ $dataTable->scripts(attributes: ['type' => 'module']) }}
@endpush


<script>
   function showGroupSuccessModal(groupUrl) {
    document.getElementById('group-success-modal').classList.remove('hidden');
    window.groupUrl = groupUrl;
}

//    function copyGroupLink(btn) {
//     const link = btn.dataset.link || window.groupUrl;
//     console.log('Link to copy:', link); // Debug line
//     if (!link) {
//         alert('No group link available to copy.');
//         return;
//     }
//     navigator.clipboard.writeText(link)
//         .then(() => alert('Group link copied to clipboard!'))
//         .catch(() => alert('Failed to copy link.'));
// }


function copyGroupLink(btn) {
    const link = btn.dataset.link || window.groupUrl;
    if (!link) {
        alert('No group link available to copy.');
        return;
    }

    // Fallback for insecure origin
    const textarea = document.createElement("textarea");
    textarea.value = link;
    textarea.style.position = "fixed";  // avoid scrolling to bottom
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();

    try {
        const successful = document.execCommand('copy');
        alert(successful ? 'Group link copied!' : 'Failed to copy link.');
    } catch (err) {
        alert('Failed to copy link.');
    }

    document.body.removeChild(textarea);
}

let currentGroupId = null;

// Open Edit Modal
// function openEditModal(groupId) {
//     currentGroupId = groupId;
    
//     // Show modal
//     document.getElementById('editGroupModal').classList.remove('hidden');
    
//     // Show loading state
//     document.getElementById('modalLoading').classList.remove('hidden');
//     document.getElementById('editGroupForm').style.display = 'none';
    
//     // Fetch group data
//     fetch(`/admin/groups/${groupId}/edit`, {
//         method: 'GET',
//         headers: {
//             'X-Requested-With': 'XMLHttpRequest',
//             'Content-Type': 'application/json',
//             'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
//         }
//     })
//     .then(response => response.json())
//     .then(data => {
//         if (data.success) {
//             // Populate form fields
//             document.getElementById('edit_group_name').value = data.group.group_name || '';
//             document.getElementById('edit_amount').value = data.group.amount || '';
//             document.getElementById('edit_currency').value = data.group.currency || '';
//             document.getElementById('edit_frequency').value = data.group.frequency || '';
//             document.getElementById('edit_max_members').value = data.group.max_number_of_member || '';
//             document.getElementById('edit_order_payment').value = data.group.orderOfPayment || '';
//             document.getElementById('edit_status').value = data.group.status || '';
//             document.getElementById('edit_group_rules').value = data.group.group_rules || '';
            
//             // Set form action
//             document.getElementById('editGroupForm').action = `/admin/groups/${groupId}/update`;
            
//             // Hide loading, show form
//             document.getElementById('modalLoading').classList.add('hidden');
//             document.getElementById('editGroupForm').style.display = 'block';
//         } else {
//             alert('Failed to load group data');
//             closeEditModal();
//         }
//     })
//     .catch(error => {
//         console.error('Error:', error);
//         alert('An error occurred while loading group data');
//         closeEditModal();
//     });
// }

// // Close Edit Modal
// function closeEditModal() {
//     document.getElementById('editGroupModal').classList.add('hidden');
//     currentGroupId = null;
    
//     // Reset form
//     document.getElementById('editGroupForm').reset();
    
//     // Hide any error messages
//     const errorElements = document.querySelectorAll('[id$="_error"]');
//     errorElements.forEach(element => {
//         element.classList.add('hidden');
//         element.textContent = '';
//     });
// }

// // Handle form submission
// document.getElementById('editGroupForm').addEventListener('submit', function(e) {
//     e.preventDefault();
    
//     const form = this;
//     const formData = new FormData(form);
    
//     // Show loading state
//     const updateButton = document.querySelector('#editGroupForm button[type="submit"]');
//     const buttonText = document.getElementById('updateButtonText');
//     const buttonSpinner = document.getElementById('updateButtonSpinner');
    
//     updateButton.disabled = true;
//     buttonText.textContent = 'Updating...';
//     buttonSpinner.classList.remove('hidden');
    
//     fetch(form.action, {
//         method: 'POST',
//         body: formData,
//         headers: {
//             'X-Requested-With': 'XMLHttpRequest',
//             'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
//         }
//     })
//     .then(response => response.json())
//     .then(data => {
//         if (data.success) {
//             alert('Group updated successfully!');
//             closeEditModal();
            
//             // Reload DataTable if available
//             if (window.LaravelDataTables && window.LaravelDataTables['dataTableBuilder']) {
//                 window.LaravelDataTables['dataTableBuilder'].ajax.reload();
//             } else {
//                 location.reload();
//             }
//         } else {
//             alert(data.message || 'Failed to update group');
//         }
//     })
//     .catch(error => {
//         console.error('Error:', error);
//         alert('An error occurred while updating the group');
//     })
//     .finally(() => {
//         // Reset button state
//         updateButton.disabled = false;
//         buttonText.textContent = 'Update Group';
//         buttonSpinner.classList.add('hidden');
//     });
// });

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('editGroupModal');
    if (e.target === modal) {
        closeEditModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeEditModal();
    }
});

@push('scripts')
    document.addEventListener('DOMContentLoaded', () => {
        const triggerModal = document.getElementById('triggerEditModal');
        const modalContent = document.getElementById('modal-content');

        document.querySelectorAll('.open-edit-modal').forEach(button => {
            button.addEventListener('click', function () {
                const url = this.dataset.url;

                // Load the edit form via AJAX
                fetch(url)
                    .then(response => response.text())
                    .then(html => {
                        modalContent.innerHTML = html;
                        triggerModal.click(); // Opens the Flowbite modal
                    })
                    .catch(() => {
                        modalContent.innerHTML = "<p class='text-red-500'>Failed to load form.</p>";
                    });
            });
        });
    });
@endpush



</script>




@if(session('group_created') && session('group_url'))
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            showGroupSuccessModal("{{ session('group_url') }}");
        });
    </script>
@endif


</x-dashboard-layout>






