@section('title', 'Groups')

<x-dashboard-layout>

    {{-- message --}}


    {{$errors}}
  

    {{-- message --}}
    

    <div class="w-full px-4 py-8">
        <div class="max-w-full max-auto sm:px-6 lg:px-6">
            <x-message-status></x-message-status>

            <div class="rounded-2xl border border-gray-200 p-5">

<div class="text-sm font-medium text-center text-gray-500 border-b border-gray-200 dark:text-gray-400 dark:border-gray-700 w-full">
    <ul class="flex flex-wrap -mb-px w-full">
        <li class="flex-1">
            <a href="#" class="inline-block w-full p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300">Contributions</a>
        </li>
        <li class="flex-1">
            <a href="#" class="inline-block w-full p-4 text-blue-600 border-b-2 border-blue-600 rounded-t-lg active dark:text-blue-500 dark:border-blue-500" aria-current="page">Payout Requests</a>
        </li>
        <li class="flex-1">
            <a href="#" class="inline-block w-full p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300">Failed Payout</a>
        </li>


    </ul>
</div>







<div class="mb-4 border-b border-gray-200 dark:border-gray-700">
    <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="default-tab" data-tabs-toggle="#default-tab-content" role="tablist">
        <li class="me-2" role="presentation">
            <button class="inline-block p-4 border-b-2 rounded-t-lg" id="profile-tab" data-tabs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="false">Profile</button>
        </li>
        <li class="me-2" role="presentation">
            <button class="inline-block p-4 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300" id="dashboard-tab" data-tabs-target="#dashboard" type="button" role="tab" aria-controls="dashboard" aria-selected="false">Dashboard</button>
        </li>
        <li class="me-2" role="presentation">
            <button class="inline-block p-4 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300" id="settings-tab" data-tabs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">Settings</button>
        </li>
        <li role="presentation">
            <button class="inline-block p-4 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300" id="contacts-tab" data-tabs-target="#contacts" type="button" role="tab" aria-controls="contacts" aria-selected="false">Contacts</button>
        </li>
    </ul>
</div>
<div id="default-tab-content">
    <div class="hidden p-4 rounded-lg bg-gray-50 dark:bg-gray-800" id="profile" role="tabpanel" aria-labelledby="profile-tab">
        <p class="text-sm text-gray-500 dark:text-gray-400">This is some placeholder content the <strong class="font-medium text-gray-800 dark:text-white">Profile tab's associated content</strong>. Clicking another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the content visibility and styling.</p>
    </div>
    <div class="hidden p-4 rounded-lg bg-gray-50 dark:bg-gray-800" id="dashboard" role="tabpanel" aria-labelledby="dashboard-tab">
        <p class="text-sm text-gray-500 dark:text-gray-400">This is some placeholder content the <strong class="font-medium text-gray-800 dark:text-white">Dashboard tab's associated content</strong>. Clicking another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the content visibility and styling.</p>
    </div>
    <div class="hidden p-4 rounded-lg bg-gray-50 dark:bg-gray-800" id="settings" role="tabpanel" aria-labelledby="settings-tab">
        <p class="text-sm text-gray-500 dark:text-gray-400">This is some placeholder content the <strong class="font-medium text-gray-800 dark:text-white">Settings tab's associated content</strong>. Clicking another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the content visibility and styling.</p>
    </div>
    <div class="hidden p-4 rounded-lg bg-gray-50 dark:bg-gray-800" id="contacts" role="tabpanel" aria-labelledby="contacts-tab">
        <p class="text-sm text-gray-500 dark:text-gray-400">This is some placeholder content the <strong class="font-medium text-gray-800 dark:text-white">Contacts tab's associated content</strong>. Clicking another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the content visibility and styling.</p>
    </div>
</div>




            



        </div>
    </div>



    {{-- modal --}}

    <!-- Main modal -->
<div id="authentication-modal" tabindex="-1" aria-hidden="true" class="hidden mt-6 overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-lg max-h-full">
        
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Create a Group
                </h3>
                <button type="button" class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="authentication-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="p-4 md:p-5">
                <form  class="space-y-4" id="create-group-form" action="{{ route('admin.groups.store') }}" method="POST">
                    @csrf
                    <div>
                        <label for="group_name"  class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Group Name</label>
                        <input type="group_name" value="{{old('group_name')}}" name="group_name" id="group_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Type the name of your group here"  />
                    </div>
                    <x-input-error :messages="$errors->get('group_name')" class="mt-2" />

                    <div>
                        <label for="amount" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Contribution Amount</label>
                        <input type="number" name="amount" value="{{old('amount')}}" id="amount" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Type the contribution amount here eg. #10,000"  />
                    </div>
                    <x-input-error :messages="$errors->get('amount')" class="mt-2" />

                    <div class="grid grid-cols-2 gap-4 md:grid-cols-2 lg:grid-cols-2">
                        <div>
                            <label for="frequency" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Frequency</label>
                            <select id="frequency" name="frequency" value="{{old('frequency')}}" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white">
                                <option>Select</option>
                               @foreach (\App\Enums\Frequency::cases() as $frequency)
                                <option value="{{ $frequency->value }}" {{ old('frequency') == $frequency->value ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('-', ' ', $frequency->value)) }}
                                </option>
                            @endforeach
                            </select>
                        <x-input-error :messages="$errors->get('frequency')" class="mt-2" />


                        </div>

                        <div>
                            <label for="currency" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Currency</label>
                            <select id="currency" name="currency" value="{{old('currency')}}" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white">
                                <option>Select</option>
                                @foreach (\App\Enums\Currency::cases() as $currency) 
                                <option value="{{ $currency->value }}" {{ old('currency') == $currency->value ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('-', ' ', $currency->value)) }}
                                </option>
                                @endforeach
                            </select>
                                <x-input-error :messages="$errors->get('currency')" class="mt-2" />

                        </div>

                    </div>
                    {{--  --}}
                    <div class="grid grid-cols-2 gap-4 md:grid-cols-2 lg:grid-cols-2">
                    <div>
                        <label for="max_number_of_member" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Number of members</label>
                        <input type="max_number_of_member" value="{{old('max_number_of_member')}}" name="max_number_of_member" id="max_number_of_member" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Type the contribution amount here eg. #10,000"  />
                        <x-input-error :messages="$errors->get('max_number_of_member')" class="mt-2" />

                    </div>

                      <div>
                            <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Staus</label>
                            <select id="status" name="status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white">
                            <option value="">Select</option>
                            @foreach (\App\Enums\GroupStatus::cases() as $status) 
                                <option value="{{ $status->value }}" {{ old('status') == $status->value ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('-', ' ', $status->value)) }}
                                </option>
                            @endforeach
                        </select>
                            <x-input-error :messages="$errors->get('currency')" class="mt-2" />

                        </div>

                    </div>
                   
                    <div>
                        <label for="group_rules" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Group rules</label>
                        <input type="group_rules" name="group_rules" value="{{old('group_rules')}}" id="group_rules" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Type the contribution amount here eg. #10,000"  />
                    </div>
                    <x-input-error :messages="$errors->get('group_rules')" class="mt-2" />
                     <div>
                        <label for="orderOfPayment" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Order of payment</label>
                        <input type="orderOfPayment" value="{{old('orderOfPayment')}}" name="orderOfPayment" id="orderOfPayment" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Type the contribution amount here eg. #10,000"  />
                    </div>
                    <x-input-error :messages="$errors->get('orderOfPayment')" class="mt-2" />

                       
                   
                    <div class="flex justify-end">
                    <button type="submit" class="flex justify-content-end text-white bg-[#0E7D34] hover:bg-[#0E7D34] focus:ring-4 focus:outline-none focus:ring-[#0E7D34] font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-[#0E7D34] dark:hover:bg-[#0E7D34] dark:focus:ring-[#0E7D34]">Create Group</button>

                    </div>
                    
                </form>
            </div>
        </div>
    </div>
</div> 
    {{-- modal --}}

    {{-- Group success --}}

     


<div id="group-success-modal" tabindex="-1" class="hidden fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6 dark:bg-gray-800">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">🎉 Group Created Successfully</h3>
        
        <p class="text-sm text-gray-600 dark:text-gray-300 mb-6">
            Your group has been created. You can now manage contributions and invite members.
        </p>

        <div class="flex justify-between items-center gap-3">
            <a  href="{{route('admin.groups.index')}}" class="w-full text-center bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm">
                Go to Group
            </a>
           <button onclick="copyGroupLink(this)" type="button"
                data-link="{{ session('group_url') }}" id="goToGroupBtn"
                class="w-full cursor-pointer text-center bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 text-sm">
                Copy Group Link
            </button>
        </div>
    </div>
</div>



    {{--  --}}


@push('scripts')
{{ $dataTable->scripts(attributes: ['type' => 'module']) }}
@endpush


<script>
   function showGroupSuccessModal(groupUrl) {
    document.getElementById('group-success-modal').classList.remove('hidden');
    window.groupUrl = groupUrl;
}

//    function copyGroupLink(btn) {
//     const link = btn.dataset.link || window.groupUrl;
//     console.log('Link to copy:', link); // Debug line
//     if (!link) {
//         alert('No group link available to copy.');
//         return;
//     }
//     navigator.clipboard.writeText(link)
//         .then(() => alert('Group link copied to clipboard!'))
//         .catch(() => alert('Failed to copy link.'));
// }



function copyGroupLink(btn) {
    const link = btn.dataset.link || window.groupUrl;
    if (!link) {
        alert('No group link available to copy.');
        return;
    }

    // Fallback for insecure origin
    const textarea = document.createElement("textarea");
    textarea.value = link;
    textarea.style.position = "fixed";  // avoid scrolling to bottom
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();

    try {
        const successful = document.execCommand('copy');
        alert(successful ? 'Group link copied!' : 'Failed to copy link.');
    } catch (err) {
        alert('Failed to copy link.');
    }

    document.body.removeChild(textarea);
}



</script>


@if(session('group_created') && session('group_url'))
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            showGroupSuccessModal("{{ session('group_url') }}");
        });
    </script>
@endif


</x-dashboard-layout>






