@section('title', 'Revenue')

<x-dashboard-layout>
    <div class="p-6 py-6 bg-gray-50 min-h-screen">
        <!-- Top Revenue Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-sm text-gray-600">Total Revenue</h3>
                <p class="text-2xl font-bold text-gray-900">200.5B</p>
                <p class="text-sm text-green-600 mt-1">↑ 5% This Month</p>
            </div>

            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-sm text-gray-600">This Month</h3>
                <p class="text-2xl font-bold text-gray-900">132.5M</p>
                <p class="text-sm text-green-600 mt-1">↑ 5% This Month</p>
            </div>

            <div class="bg-green-700 text-white p-6 rounded-lg flex flex-col justify-between">
                <h3 class="text-lg font-semibold mb-4">Withdraw Revenue</h3>
                <button class="bg-green-500 hover:bg-green-600 text-white font-semibold px-4 py-2 rounded shadow">Request Withdrawal</button>
            </div>
        </div>

        <!-- Graphs and Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- Revenue Trends -->
            <div class="bg-white p-6 rounded-lg shadow col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="font-semibold text-gray-800">Revenue Trends</h4>
                    <div class="space-x-2">
                        <button class="px-3 py-1 text-sm border rounded bg-green-100 text-green-700">This Week</button>
                        <button class="px-3 py-1 text-sm border rounded">This Month</button>
                        <button class="px-3 py-1 text-sm border rounded">This Year</button>
                    </div>
                </div>
                <div>
                    <!-- Chart Placeholder -->
                    <canvas id="revenueTrendsChart" height="200"></canvas>
                </div>
                <div class="flex justify-center space-x-4 mt-4 text-sm">
                    <div class="flex items-center space-x-1 text-green-700">
                        <span class="w-3 h-3 rounded-full bg-green-700"></span>
                        <span>Subscriptions</span>
                    </div>
                    <div class="flex items-center space-x-1 text-green-300">
                        <span class="w-3 h-3 rounded-full bg-green-300"></span>
                        <span>Ad Revenue</span>
                    </div>
                    <div class="flex items-center space-x-1 text-yellow-500">
                        <span class="w-3 h-3 rounded-full bg-yellow-500"></span>
                        <span>Referral Commission</span>
                    </div>
                </div>
            </div>

            <!-- Revenue Source Distribution -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h4 class="font-semibold text-gray-800 mb-4">Revenue Source Distribution</h4>
                <canvas id="revenueSourceChart" height="200"></canvas>
            </div>
        </div>

        <!-- Withdrawal History Table -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h4 class="font-semibold text-gray-800 mb-4">Withdrawal History</h4>
            <div class="overflow-x-auto">
                <table class="min-w-full text-left text-sm">
                    <thead class="text-gray-600 border-b">
                        <tr>
                            <th class="py-2 px-4">S/N</th>
                            <th class="py-2 px-4">Date</th>
                            <th class="py-2 px-4">Transaction I.D</th>
                            <th class="py-2 px-4">Transaction Type</th>
                            <th class="py-2 px-4">Amount</th>
                            <th class="py-2 px-4">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="border-b">
                            <td class="py-2 px-4">01</td>
                            <td class="py-2 px-4">01/04/2025</td>
                            <td class="py-2 px-4">132egu2dwdnke0</td>
                            <td class="py-2 px-4">Withdrawal</td>
                            <td class="py-2 px-4">₦100,000,000</td>
                            <td class="py-2 px-4 text-green-600 font-semibold">Success</td>
                        </tr>
                        <tr class="border-b">
                            <td class="py-2 px-4">02</td>
                            <td class="py-2 px-4">31/03/2025</td>
                            <td class="py-2 px-4">12bnxcdnlbck123</td>
                            <td class="py-2 px-4">Withdrawal</td>
                            <td class="py-2 px-4">₦120,000,000</td>
                            <td class="py-2 px-4 text-red-600 font-semibold">Failed</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        const revenueCtx = document.getElementById('revenueTrendsChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['01-Apr', '02-Apr', '03-Apr', '04-Apr', '05-Apr', '06-Apr', '07-Apr', '08-Apr'],
                datasets: [
                    {
                        label: 'Subscriptions',
                        data: [25, 23, 22, 21, 20, 19, 20, 21],
                        borderColor: '#15803d',
                        fill: false
                    },
                    {
                        label: 'Ad Revenue',
                        data: [12, 11, 10, 11, 10, 9, 11, 12],
                        borderColor: '#86efac',
                        fill: false
                    },
                    {
                        label: 'Referral Commission',
                        data: [3, 4, 5, 6, 6, 7, 8, 9],
                        borderColor: '#facc15',
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'Amount (₦) in Thousands' }
                    }
                }
            }
        });

        const sourceCtx = document.getElementById('revenueSourceChart').getContext('2d');
        new Chart(sourceCtx, {
            type: 'doughnut',
            data: {
                labels: ['Subscriptions', 'Ad Revenue', 'Referral Commission'],
                datasets: [{
                    label: 'Revenue Source',
                    data: [60, 25, 15],
                    backgroundColor: ['#15803d', '#bbf7d0', '#facc15']
                }]
            },
            options: {
                responsive: true,
                cutout: '70%'
            }
        });
    </script>
    @endpush

</x-dashboard-layout>
