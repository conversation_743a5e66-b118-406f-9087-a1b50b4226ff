@section('title', 'All Plans')

<x-dashboard-layout>


    <div class="w-full px-4 py-8">
        <div class="max-w-full max-auto sm:px-6 lg:px-6">
            <!-- Breadcrumb -->
            <div class="p-4 mb-5 border-stroke border-gray-200 lg:mt-1.5 dark:bg-gray-800 dark:border-gray-700">
                <nav class="flex mb-5" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center ml-auto mr-20 space-x-1 text-sm font-medium md:space-x-2">
                        <li class="inline-flex items-center">
                            <a href="" class="inline-flex items-center text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-white">
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
                                <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500">View Plans</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
            <x-message-status></x-message-status> {{-- message status --}}



            <!-- Add Plan Button -->
            <div class="flex justify-end mb-4">
                <a href="{{ route('admin.faculty.create')}}" class="inline-flex px-5 py-2 text-base font-medium text-center text-white bg-blue-800 rounded-lg hover:bg-blue-700 focus:ring-4 focus:bg-blue-900">
                    <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                    Add Faculty
                </a>
            </div>

            <div class="rounded-2xl border border-gray-200 p-5">

                {{$dataTable->table()}}




            </div>

        </div>
    </div>

@push('scripts')
{{ $dataTable->scripts(attributes: ['type' => 'module']) }}
@endpush

</x-dashboard-layout>
