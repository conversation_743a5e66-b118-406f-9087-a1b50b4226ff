

<x-dashboard-layout>




    <!-- Add Loans Form -->

    <div class="py-6 bg-gray-100">
        <div class="max-w-full mx-auto sm:px-6 lg:px-6">

    {{-- Breadcrumb --}}



    <div class=" p-4 mb-5 border-stroke border-gray-200 lg:mt-1.5 dark:bg-gray-800 dark:border-gray-700">

        <div class="w-full mb-1">
            <div class="mb-4">
                <nav class="flex mb-5" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center ml-auto mr-20 space-x-1 text-sm font-medium md:space-x-2">
                      <li class="inline-flex items-center">
                        <a href="" class="inline-flex items-center text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-white">
                          {{-- <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path></svg> --}}
                          Dashboard
                        </a>
                      </li>
                      <li>
                        <div class="flex items-center">
                          <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
                          <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500" aria-current="page">Plan</span>
                        </div>
                      </li>

                    </ol>
                </nav>
            </div>

        </div>

    </div>

    {{-- Breadcrumb end --}}


    <div class="w-[1200px] rounded-2xl p-6 mx-auto bg-white shadow-md">
        <h2 class="pb-4 mb-4 text-xl font-semibold text-gray-900 border-b border-stroke sm:text-2xl dark:text-white"> Add Faculty</h2>

        @if(session('success'))
        <div   class="relative px-4 py-3 mb-5 text-teal-700 bg-teal-100 border border-teal-400 rounded" role="alert">
            <strong class="font-bold">Success!</strong>
            <span class="block sm:inline">{{ session('success') }}</span>
            <button  type="button"  onclick="return this.parentNode.remove()" aria-label="Close" class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
            <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
            </button>
        </div>

        @endif

        @if(session('error'))
        <div  class="relative px-4 py-3 mb-5 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ session('error') }}</span>
            <button  type="button"  onclick="return this.parentNode.remove()" aria-label="Close" class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
            <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
            </button>
        </div>
        @endif

        <form action="{{ route('admin.faculty.store') }}" method="POST">
            @csrf
            <div class="mb-4">
                <label for="name" class="block py-2 text-sm font-medium text-gray-700">Name</label>
                <input type="text" id="name" name="name" value="{{ old('name') }}"  class="block w-full p-3 mt-1 border border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500  @error('name') border-red @enderror" placeholder="Enter plan name" />
                <x-input-error :messages="$errors->get('name')" class="mt-2" />

            </div>

            {{-- <div class="mb-4">
                <label for="description" class="block py-2 text-sm font-medium text-gray-700">Description</label>
                <textarea id="description" value="{{ old('description') }}" name="description" rows="4"  class="block w-full p-3 mt-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter plan description"></textarea>
                <x-input-error :messages="$errors->get('description')" class="mt-2" />

            </div> --}}

              <button type="submit"
                    class="w-full sm:w-auto px-6 py-3 text-white rounded-md bg-[#E01C22] hover:bg-[#BE4C4C] focus:outline-none focus:ring-2 focus:ring-[#E01C22] mt-4 transition-colors duration-200">
                    Create
                </button>
        </form>
    </div>


</div>
</div>









    </x-dashboard-layout>
