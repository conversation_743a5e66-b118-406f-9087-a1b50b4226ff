<x-dashboard-layout>


    @section('content')
    {{-- <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">Admin Dashboard</div>

                    <div class="card-body">
                        Welcome to the admin dashboard!
                    </div>
                </div>
            </div>
        </div>
    </div> --}}

    <div class="py-8 bg-gray-100">
            <div class="max-w-full mx-auto sm:px-6 lg:px-6">

                <div class="grid grid-cols-1 md:grid-cols-2">
             {{-- Right side --}}
                <div class="text-2xl font-bold text-gray-800 dark:text-white font-urbanist leading-[20.4px]">
                    Welcome, {{ Auth::user()->first_name }}!
                    <div class="mt-2 mb-4">
                    <p class="text-sm font-regular">Here’s an overview of the platform’s latest activity</p>
                    </div>
                </div>

                {{-- left side --}}
                <div class=" flex justify-end mb-4">
                    <div class="bg-[#cfead8] w-[220px] rounded-md ">
                    <button type="button" class=" w-full h-full flex justify-center  items-center">Resolve reported issues</button>
                    </div>

                </div>
               


                </div>
             



                {{-- the boxes --}}

              <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
            <!-- Box 1 -->
        <div class="p-6 border border-gray-300 rounded-lg shadow-sm bg-white dark:bg-gray-800 hover:bg-[#CFEAD852] hover:border-[#117D34] dark:hover:bg-gray-700 transition-colors duration-200">
            <div class="text-center space-y-3">
                <!-- Icon + Title Row -->
                <div class="flex items-center justify-center gap-2 mb-2">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a4 4 0 00-3-3.87M9 20H4v-2a4 4 0 013-3.87m10-5a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                    <h5 class="text-sm font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                        Total Users
                    </h5>
                </div>
                
                <!-- User Count - Centered -->
                <div class="flex items-baseline justify-center gap-2">
                    <p class="text-4xl font-bold text-gray-900 dark:text-white">
                        {{ $user->count() }}
                    </p>
                    <span class="text-sm font-medium text-[#117D34] self-end mb-1">users</span>
                </div>
                
                <!-- Growth Info - Centered -->
                <div class="flex items-center justify-center gap-1">
                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l6 6a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L4.707 10.707a1 1 0 01-1.414-1.414l6-6A1 1 0 0110 3z" clip-rule="evenodd" />
                    </svg>
                    <span class="text-sm text-green-600 dark:text-green-400 font-medium">
                        +5% This month
                    </span>
                </div>
            </div>
        </div>



            <!-- Box 2 -->
        <div class="p-6 border border-gray-300 rounded-lg shadow-sm bg-white dark:bg-gray-800 hover:bg-[#CFEAD852] hover:border-[#117D34] dark:hover:bg-gray-700 transition-colors duration-200">
            <div class="text-center space-y-3">
                <!-- Icon + Title Row -->
                <div class="flex items-center justify-center gap-2 mb-2">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                    </svg>
                    <h5 class="text-sm font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                        Total Transactions
                    </h5>
                </div>
                
                <!-- Transaction Count - Centered -->
                <div class="flex items-baseline justify-center gap-2">
                    <p class="text-4xl font-bold text-gray-900 dark:text-white">
                        {{$transaction->count()}}
                    </p>
                    <span class="text-sm font-medium text-[#117D34] self-end mb-1">transactions</span>
                </div>
                
                <!-- Growth Info - Centered -->
                <div class="flex items-center justify-center gap-1">
                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l6 6a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L4.707 10.707a1 1 0 01-1.414-1.414l6-6A1 1 0 0110 3z" clip-rule="evenodd" />
                    </svg>
                    <span class="text-sm text-green-600 dark:text-green-400 font-medium">
                        +5% This month
                    </span>
                </div>
            </div>
        </div>


            <!-- Box 3 -->
        <div class="p-6 border border-gray-300 rounded-lg shadow-sm bg-white dark:bg-gray-800 hover:bg-[#CFEAD852] hover:border-[#117D34] dark:hover:bg-gray-700 transition-colors duration-200">
            <div class="text-center space-y-3">
                <!-- Icon + Title Row -->
                <div class="flex items-center justify-center gap-2 mb-2">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/>
                    </svg>
                    <h5 class="text-sm font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                        Total Groups
                    </h5>
                </div>
                
                <!-- Groups Count - Centered -->
                <div class="flex items-baseline justify-center gap-2">
                    <p class="text-4xl font-bold text-gray-900 dark:text-white">
                        {{$group->count()}}
                    </p>
                    <span class="text-sm font-medium text-[#117D34] self-end mb-1">groups</span>
                </div>
                
                <!-- Growth Info - Centered -->
                <div class="flex items-center justify-center gap-1">
                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l6 6a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L4.707 10.707a1 1 0 01-1.414-1.414l6-6A1 1 0 0110 3z" clip-rule="evenodd" />
                    </svg>
                    <span class="text-sm text-green-600 dark:text-green-400 font-medium">
                        +5% This month
                    </span>
                </div>
            </div>
        </div>


            <!-- Box 4 -->
           <div class="p-6 border border-gray-300 rounded-lg shadow-sm bg-white dark:bg-gray-800 hover:bg-orange-50 hover:border-orange-300 dark:hover:bg-gray-700 transition-colors duration-200">
        <div class="text-center space-y-3">
            <!-- Icon + Title Row -->
            <div class="flex items-center justify-center gap-2 mb-2">
                <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
                <h5 class="text-sm font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                    Pending Verification
                </h5>
            </div>
            
            <!-- Pending Count - Centered -->
            <div class="flex items-baseline justify-center gap-2">
                <p class="text-4xl font-bold text-gray-900 dark:text-white">
                    12,450
                </p>
                <span class="text-sm font-medium text-yellow-600 self-end mb-1">pending</span>
            </div>
            
            <!-- Growth Info - Centered -->
            <div class="flex items-center justify-center gap-1">
                <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l6 6a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L4.707 10.707a1 1 0 01-1.414-1.414l6-6A1 1 0 0110 3z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-yellow-600 dark:text-yellow-400 font-medium">
                    +2% This month
                </span>
            </div>
        </div>
          </div>


             <!-- Box 5 -->
            <div class="p-6 border border-gray-300 rounded-lg shadow-sm bg-white dark:bg-gray-800 hover:bg-red-50 hover:border-red-300 dark:hover:bg-gray-700 transition-colors duration-200">
            <div class="text-center space-y-3">
                <!-- Icon + Title Row -->
                <div class="flex items-center justify-center gap-2 mb-2">
                    <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <h5 class="text-sm font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wide">
                        Unresolved Cases
                    </h5>
                </div>
                
                <!-- Cases Count - Centered -->
                <div class="flex items-baseline justify-center gap-2">
                    <p class="text-4xl font-bold text-gray-900 dark:text-white">
                        12,450
                    </p>
                    <span class="text-sm font-medium text-red-600 self-end mb-1">cases</span>
                </div>
                
                <!-- Growth Info - Centered -->
                <div class="flex items-center justify-center gap-1">
                    <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l6 6a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L4.707 10.707a1 1 0 01-1.414-1.414l6-6A1 1 0 0110 3z" clip-rule="evenodd" />
                    </svg>
                    <span class="text-sm text-red-600 dark:text-red-400 font-medium">
                        +3% This month
                    </span>
                </div>
            </div>
            </div>


            {{-- end --}}
        </div>


        {{-- the boxes --}}


        {{-- section-two --}}
        <section class="mt-8">
         <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Transaction Overview -->
            <div class="p-6 bg-white border border-gray-200 rounded-lg ">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">💰 Transaction Overview</h3>
                    <span class="text-sm text-gray-500">Last 30 Days</span>
                </div>
                <div class="relative h-64">
                    <canvas id="transactionChart" class="absolute inset-0 w-full h-full"></canvas>
                </div>
            </div>


            <!-- User Growth -->
        <div class="p-6 bg-white border border-gray-200 rounded-lg ">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">📈 User Growth</h3>
                <span class="text-sm text-gray-500">This Month</span>
            </div>
            <div class="relative h-64">
                <canvas id="userGrowthChart" class="absolute inset-0 w-full h-full"></canvas>
            </div>
        </div>

        </div>

            
        </section>

        {{-- section-three --}}

         <section class="mt-8">
         <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Transaction Overview -->
            <div class="p-6 bg-white border border-gray-200 rounded-lg ">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">💰 Transaction Overview</h3>
                    <span class="text-sm text-gray-500">Last 30 Days</span>
                </div>
                <div class="relative h-64">
                    <canvas id="transactionChart" class="absolute inset-0 w-full h-full"></canvas>
                </div>
            </div>


            <!-- User Growth -->
        <div class="p-6 bg-white border border-gray-200 rounded-lg ">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">📈 User Growth</h3>
                <span class="text-sm text-gray-500">This Month</span>
            </div>
            <div class="relative h-64">
                <canvas id="userGrowthChart" class="absolute inset-0 w-full h-full"></canvas>
            </div>
        </div>

        </div>

            
        </section>
       


       







    </div>
    </div>

    </x-dashboard-layout>



