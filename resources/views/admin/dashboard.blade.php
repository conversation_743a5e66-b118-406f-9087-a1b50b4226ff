<x-dashboard-layout>


    @section('content')
    {{-- <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">Admin Dashboard</div>

                    <div class="card-body">
                        Welcome to the admin dashboard!
                    </div>
                </div>
            </div>
        </div>
    </div> --}}

    <div class="py-8 bg-gray-100">
            <div class="max-w-full mx-auto sm:px-6 lg:px-6">

                <div class="grid grid-cols-1 md:grid-cols-2">
             {{-- Right side --}}
                <div class="text-2xl font-bold text-gray-800 dark:text-white font-urbanist leading-[20.4px]">
                    Welcome, {{ Auth::user()->first_name }}!
                    <div class="mt-2 mb-4">
                    <p class="text-sm font-regular">Here’s an overview of the platform’s latest activity</p>
                    </div>
                </div>

                {{-- left side --}}
                <div class=" flex justify-end mb-4">
                    <div class="bg-[#cfead8] w-[220px] rounded-md ">
                    <button type="button" class=" w-full h-full flex justify-center  items-center">Resolve reported issues</button>
                    </div>

                </div>
               


                </div>
             



                {{-- the boxes --}}

              <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
            <!-- Box 1 -->
            <div class="p-6 border border-gray-300 rounded-lg shadow-sm bg-white dark:bg-gray-800 hover:bg-[#CFEAD852] hover:border-[#117D34] dark:hover:bg-gray-700 transition-colors duration-200">
                <div class="flex flex-col h-full justify-between">
                    <!-- Header: Icon + Title -->
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center gap-2">
                            <!-- User Icon -->
                            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="currentColor" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a4 4 0 00-3-3.87M9 20H4v-2a4 4 0 013-3.87m10-5a4 4 0 11-8 0 4 4 0 018 0z" />
                           </svg>

                            <h5 class="text-sm font-regular text-gray-800 dark:text-white">Total Users</h5>
                        </div>
                    </div>

                    <!-- User Count -->
                    <p class="text-3xl font-semi-bold text-gray-900 dark:text-white mb-2">12,450<span class="text-sm  text-[#117D34]">users</span></p>

                    <!-- Growth Info -->
                    <div class="flex items-center gap-1">
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l6 6a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L4.707 10.707a1 1 0 01-1.414-1.414l6-6A1 1 0 0110 3z" clip-rule="evenodd" />
                        </svg>

                        <span class="text-sm text-green-600 dark:text-green-400">5% This month</span>
                    </div>
                </div>
            </div>


            <!-- Box 2 -->
            <div class="p-6 border border-gray-300 rounded-lg hover:bg-[#CFEAD852] hover:border-[#117D34] shadow-sm bg-white dark:bg-gray-800">
              <div class="flex flex-col h-full justify-between ">
                    <!-- Header: Icon + Title -->
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center gap-2">
                            <!-- User Icon -->
                            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="currentColor" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a4 4 0 00-3-3.87M9 20H4v-2a4 4 0 013-3.87m10-5a4 4 0 11-8 0 4 4 0 018 0z" />
                           </svg>

                            <h5 class="text-sm font-regular text-gray-800 dark:text-white">Total Transactions</h5>
                        </div>
                    </div>

                    <!-- User Count -->
                    <p class="text-3xl font-semi-bold text-gray-900 dark:text-white mb-2">12,450<span class="text-sm  text-[#117D34]">users</span></p>

                    <!-- Growth Info -->
                    <div class="flex items-center gap-1">
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l6 6a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L4.707 10.707a1 1 0 01-1.414-1.414l6-6A1 1 0 0110 3z" clip-rule="evenodd" />
                        </svg>

                        <span class="text-sm text-green-600 dark:text-green-400">5% This month</span>
                    </div>
                </div>
            </div>

            <!-- Box 3 -->
            <div class="p-6 border border-gray-300 rounded-lg shadow-sm bg-white hover:bg-[#CFEAD852] hover:border-[#117D34] dark:bg-gray-800">
               <div class="flex flex-col h-full justify-between">
                    <!-- Header: Icon + Title -->
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center gap-2">
                            <!-- User Icon -->
                            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="currentColor" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a4 4 0 00-3-3.87M9 20H4v-2a4 4 0 013-3.87m10-5a4 4 0 11-8 0 4 4 0 018 0z" />
                           </svg>

                            <h5 class="text-sm font-regular text-gray-800 dark:text-white">Total Groups</h5>
                        </div>
                    </div>

                    <!-- User Count -->
                    <p class="text-3xl font-semi-bold text-gray-900 dark:text-white mb-2">12,450<span class="text-sm  text-[#117D34]">users</span></p>

                    <!-- Growth Info -->
                    <div class="flex items-center gap-1">
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l6 6a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L4.707 10.707a1 1 0 01-1.414-1.414l6-6A1 1 0 0110 3z" clip-rule="evenodd" />
                        </svg>

                        <span class="text-sm text-green-600 dark:text-green-400">5% This month</span>
                    </div>
                </div>
            </div>

            <!-- Box 4 -->
            <div class="p-6 border border-gray-300 rounded-lg shadow-sm hover:bg-[#CFEAD852] hover:border-[#117D34] bg-white dark:bg-gray-800">
               <div class="flex flex-col h-full justify-between">
                    <!-- Header: Icon + Title -->
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center gap-2">
                            <!-- User Icon -->
                            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="currentColor" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a4 4 0 00-3-3.87M9 20H4v-2a4 4 0 013-3.87m10-5a4 4 0 11-8 0 4 4 0 018 0z" />
                           </svg>

                            <h5 class="text-sm font-regular text-gray-800 dark:text-white">Pending Verification</h5>
                        </div>
                    </div>

                    <!-- User Count -->
                    <p class="text-3xl font-semi-bold text-gray-900 dark:text-white mb-2">12,450<span class="text-sm  text-[#117D34]">users</span></p>

                    <!-- Growth Info -->
                    <div class="flex items-center gap-1">
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l6 6a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L4.707 10.707a1 1 0 01-1.414-1.414l6-6A1 1 0 0110 3z" clip-rule="evenodd" />
                        </svg>

                        <span class="text-sm text-green-600 dark:text-green-400">5% This month</span>
                    </div>
                </div>
            </div>

             <!-- Box 5 -->
            <div class="p-6 border border-gray-300 rounded-lg shadow-sm hover:bg-[#CFEAD852] hover:border-[#117D34] bg-white dark:bg-gray-800">
                <div class="flex flex-col h-full justify-between">
                    <!-- Header: Icon + Title -->
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center gap-2">
                            <!-- User Icon -->
                            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="currentColor" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a4 4 0 00-3-3.87M9 20H4v-2a4 4 0 013-3.87m10-5a4 4 0 11-8 0 4 4 0 018 0z" />
                           </svg>

                            <h5 class="text-sm font-regular text-gray-800 dark:text-white">Unresolved Cases</h5>
                        </div>
                    </div>

                    <!-- User Count -->
                    <p class="text-3xl font-semi-bold text-gray-900 dark:text-white mb-2">12,450<span class="text-sm  text-[#117D34]">users</span></p>

                    <!-- Growth Info -->
                    <div class="flex items-center gap-1">
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l6 6a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L4.707 10.707a1 1 0 01-1.414-1.414l6-6A1 1 0 0110 3z" clip-rule="evenodd" />
                        </svg>

                        <span class="text-sm text-green-600 dark:text-green-400">5% This month</span>
                    </div>
                </div>
            </div>
        </div>


        {{-- the boxes --}}


        {{-- section-two --}}
        <section class="mt-8">
         <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Transaction Overview -->
            <div class="p-6 bg-white border border-gray-200 rounded-lg ">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">💰 Transaction Overview</h3>
                    <span class="text-sm text-gray-500">Last 30 Days</span>
                </div>
                <div class="relative h-64">
                    <canvas id="transactionChart" class="absolute inset-0 w-full h-full"></canvas>
                </div>
            </div>


            <!-- User Growth -->
        <div class="p-6 bg-white border border-gray-200 rounded-lg ">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">📈 User Growth</h3>
                <span class="text-sm text-gray-500">This Month</span>
            </div>
            <div class="relative h-64">
                <canvas id="userGrowthChart" class="absolute inset-0 w-full h-full"></canvas>
            </div>
        </div>

        </div>

            
        </section>

        {{-- section-three --}}

         <section class="mt-8">
         <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Transaction Overview -->
            <div class="p-6 bg-white border border-gray-200 rounded-lg ">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">💰 Transaction Overview</h3>
                    <span class="text-sm text-gray-500">Last 30 Days</span>
                </div>
                <div class="relative h-64">
                    <canvas id="transactionChart" class="absolute inset-0 w-full h-full"></canvas>
                </div>
            </div>


            <!-- User Growth -->
        <div class="p-6 bg-white border border-gray-200 rounded-lg ">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">📈 User Growth</h3>
                <span class="text-sm text-gray-500">This Month</span>
            </div>
            <div class="relative h-64">
                <canvas id="userGrowthChart" class="absolute inset-0 w-full h-full"></canvas>
            </div>
        </div>

        </div>

            
        </section>
       


       







    </div>
    </div>

    </x-dashboard-layout>



