<x-dashboard-layout>


    @section('content')
   

    <div class="py-8 bg-gray-100">
            <div class="max-w-full mx-auto sm:px-6 lg:px-6">

            
        

                {{-- the boxes --}}

              <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <!-- Box 1 -->
            <div class="p-6 border border-gray-300 rounded-lg shadow-sm bg-[#0E7D34] dark:bg-gray-800 hover:bg-[#CFEAD852] hover:border-[#117D34] dark:hover:bg-gray-700 transition-colors duration-200 relative overflow-hidden" style="background-image: url('{{ asset('images/wave-pattern.svg') }}'), url('{{ asset('images/wave-pattern.svg') }}'); background-repeat: no-repeat, no-repeat; background-position: top left, top right; background-size: 80px 25px, 80px 25px;">
                <div class="flex flex-col h-full justify-center items-center relative z-10">
                    <!-- Header: Icon + Title -->
                    <div class="flex flex-col items-center gap-2">
                        <!-- User Icon -->
                        <h5 class="text-sm font-regular text-white dark:text-white">Total Tasks</h5>
                        <p class="text-3xl font-semi-bold text-white dark:text-white mb-2">12</p>
                    </div>
                </div>
            </div>






            <!-- Box 2 -->
            <div class="p-6 border border-[#117D34] rounded-lg bg-[#CFEAD852] hover:bg-[#CFEAD852] hover:border-[#117D34] shadow-sm dark:bg-gray-800">
             <div class="flex flex-col h-full justify-center items-center relative z-10">
                <!-- Header: Icon + Title -->
                <div class="flex flex-col items-center gap-2">
                    <!-- User Icon -->
                    <h5 class="text-sm font-regular text-gray-800 dark:text-white">Active Tasks</h5>
                    <p class="text-3xl font-semi-bold text-gray-800 dark:text-white mb-2">12</p>
                </div>
          </div>
            </div>

            <!-- Box 3 -->
             <div class="p-6 border border-[#117D34] rounded-lg bg-[#CFEAD852] hover:bg-[#CFEAD852] hover:border-[#117D34] shadow-sm dark:bg-gray-800">
             <div class="flex flex-col h-full justify-center items-center relative z-10">
                <!-- Header: Icon + Title -->
                <div class="flex flex-col items-center gap-2">
                    <!-- User Icon -->
                    <h5 class="text-sm font-regular text-gray-800 dark:text-white">Avg.Point / Task</h5>
                    <p class="text-3xl font-semi-bold text-gray-800 dark:text-white mb-2">12</p>
                </div>
          </div>
            </div>

            <!-- Box 4 -->
             <div class="p-6 border border-[#117D34] rounded-lg bg-[#CFEAD852] hover:bg-[#CFEAD852] hover:border-[#117D34] shadow-sm dark:bg-gray-800">
             <div class="flex flex-col h-full justify-center items-center relative z-10">
                <!-- Header: Icon + Title -->
                <div class="flex flex-col items-center gap-2">
                    <!-- User Icon -->
                    <h5 class="text-sm font-regular text-gray-800 dark:text-white">Most Completed Tasks</h5>
                    <p class="text-3xl font-semi-bold text-gray-800 dark:text-white mb-2">Invite 3 Friends</p>
                </div>
          </div>
            </div>

           
        </div>


        {{-- the boxes --}}


        {{-- section-two --}}
        <div class="mt-6">
      <x-message-status></x-message-status>

        </div>
{{$errors}}
            <div class="rounded-2xl border border-gray-200 p-5 mt-6">

            <div class="flex justify-end mb-4">
                <!-- Modal toggle -->
                <button data-modal-target="authentication-modal" data-modal-toggle="authentication-modal"
                class="flex items-center gap-2 text-white bg-[#0E7D34] hover:bg-[#0E7D34] focus:ring-4 focus:outline-none focus:ring-[#0E7D34] font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-[#0E7D34] dark:hover:bg-[#0E7D34] dark:focus:ring-[#0E7D34]"
                type="button">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"></path>
                </svg>
                Add Task
            </button>

            </div>

                {{$dataTable->table()}}

            </div>





              <!-- Main modal -->
<div id="authentication-modal" tabindex="-1" aria-hidden="true" class="hidden mt-6 overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-lg max-h-full">
        
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Create a Task
                </h3>
                <button type="button" class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="authentication-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="p-4 md:p-5">
                <form  class="space-y-4" id="create-group-form" action="{{ route('admin.tasks.store') }}" method="POST">
                    @csrf
                    <div>
                        <label for="task_name"  class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Task Name</label>
                        <input type="task_name" value="{{old('task_name')}}" name="task_name" id="task_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Task Name"  />
                    </div>
                    <x-input-error :messages="$errors->get('task_name')" class="mt-2" />

                   <div>
                        <label for="task_description"  class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Task Description</label>
                        <input type="task_description" value="{{old('task_description')}}" name="task_description" id="task_description" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" placeholder="Task Description"  />
                    </div>
                    <x-input-error :messages="$errors->get('task_description')" class="mt-2" />

                    <div>
                        <label for="task_points" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Task Points</label>
                        <input type="number" 
                            value="{{old('task_points')}}" name="task_points" id="task_points" min="1" step="1"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white" 
                            placeholder="Task Points" 
                            required />
                        <x-input-error :messages="$errors->get('task_points')" class="mt-2" />
                    </div>

                    <div>
                        <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                        <select id="status" 
                                name="status" 
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white"
                                required>
                            <option value="">Select Status</option>
                            @foreach (\App\Enums\TxStatus::cases() as $status)
                                <option value="{{ $status->value }}" {{ old('status') == $status->value ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('-', ' ', $status->value)) }}
                                </option>
                            @endforeach
                        </select>
                        <x-input-error :messages="$errors->get('status')" class="mt-2" />
                    </div>


                    

                    {{--  --}}
                
        
                   
                   
                    <div class="flex justify-end">
                    <button type="submit" class="flex justify-content-end text-white bg-[#0E7D34] hover:bg-[#0E7D34] focus:ring-4 focus:outline-none focus:ring-[#0E7D34] font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-[#0E7D34] dark:hover:bg-[#0E7D34] dark:focus:ring-[#0E7D34]">Create Task</button>

                    </div>
                    
                </form>
            </div>
        </div>
    </div>
</div> 
    {{-- modal --}}



    {{-- edit --}}

    <!-- Edit Task Modal -->
<div id="editTaskModal" class="fixed inset-0 z-50 hidden overflow-y-auto bg-black bg-opacity-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Edit Task</h3>
                <form id="editTaskForm" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Task Name</label>
                        <input type="text" id="edit_task_name" name="task_name" class="mt-1 block w-full rounded-md border-gray-300" required>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Task Description</label>
                        <textarea id="edit_task_description" name="task_description" class="mt-1 block w-full rounded-md border-gray-300" required></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Task Points</label>
                        <input type="number" id="edit_task_points" name="task_points" class="mt-1 block w-full rounded-md border-gray-300" min="1" required>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Status</label>
                        <select id="edit_status" name="status" class="mt-1 block w-full rounded-md border-gray-300" required>
                            @foreach (\App\Enums\TxStatus::cases() as $status)
                                <option value="{{ $status->value }}">{{ ucfirst(str_replace('-', ' ', $status->value)) }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="closeEditModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">Cancel</button>
                        <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">Update Task</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit button clicks
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('edit-task-btn') || e.target.closest('.edit-task-btn')) {
            e.preventDefault();
            const btn = e.target.classList.contains('edit-task-btn') ? e.target : e.target.closest('.edit-task-btn');
            
            const taskId = btn.dataset.taskId;
            const taskName = btn.dataset.taskName;
            const taskDescription = btn.dataset.taskDescription;
            const taskPoints = btn.dataset.taskPoints;
            const taskStatus = btn.dataset.taskStatus;
            
            // Populate modal form
            document.getElementById('edit_task_name').value = taskName;
            document.getElementById('edit_task_description').value = taskDescription;
            document.getElementById('edit_task_points').value = taskPoints;
            document.getElementById('edit_status').value = taskStatus;
            
            // Set form action
            document.getElementById('editTaskForm').action = `/admin/tasks/${taskId}`;
            
            // Show modal
            document.getElementById('editTaskModal').classList.remove('hidden');
        }
    });
});

function closeEditModal() {
    document.getElementById('editTaskModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('editTaskModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEditModal();
    }
});
</script>


    {{-- edit modal --}}




       


    </div>
    </div>


@push('scripts')
{{ $dataTable->scripts(attributes: ['type' => 'module']) }}
@endpush

    </x-dashboard-layout>



