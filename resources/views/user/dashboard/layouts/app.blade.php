    <!doctype html>
    <html lang="en" class="light">
        <head>
        <meta charset="UTF-8" />
        <meta
          name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
        />
        <meta http-equiv="X-UA-Compatible" content="ie=edge" />
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />


        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        <script>
            if (localStorage.getItem('dark-mode') === 'false' || !('dark-mode' in localStorage)) {
                document.querySelector('html').classList.remove('dark');
                document.querySelector('html').style.colorScheme = 'light';
            } else {
                document.querySelector('html').classList.add('dark');
                document.querySelector('html').style.colorScheme = 'dark';
        }
        </script>
       <body
        x-data="{ page: 'profile', 'loaded': true, 'darkMode': true,'stickyMenu': false, 'sidebarToggle': false, 'scrollTop': false, 'isProfileInfoModal': false, 'isProfileAddressModal': false }"
        x-init="
            darkMode = JSON.parse(localStorage.getItem('darkMode'));
    $watch('darkMode', value => localStorage.setItem('darkMode', JSON.stringify(value)))" :class="{ 'dark': darkMode === true }" class="dark:bg-slate-900 bg-white dark:text-slate-200"
      >
      <div class="flex h-screen overflow-hidden">

            @include('user.dashboard.layouts.sidebar')
            <div
            class="relative flex flex-col flex-1 overflow-x-hidden overflow-y-auto"
          >
            @include('user.dashboard.layouts.header')
            @yield('content')
        </div>
      </div>


      {{--  --}}
      <!-- Add this somewhere in your authenticated layout -->
{{--  --}}

      {{--  --}}
   @include('user.dashboard.layouts.footer')
