<aside :class="sidebarToggle ? 'translate-x-0 lg:w-[90px]' : '-translate-x-full'" class="sidebar fixed left-0 top-0 z-9999 flex h-screen w-[250px] flex-col overflow-y-hidden border-r border-gray-200 bg-white px-5 dark:border-gray-800 dark:bg-black lg:static lg:translate-x-0"
>
  <!-- SIDEBAR HEADER -->
  <div :class="sidebarToggle ? 'justify-center' : 'justify-between'" class="flex items-center gap-2 pt-8 sidebar-header pb-7">
    <a href="index.html">
      <span class="logo" :class="sidebarToggle ? 'hidden' : ''">
        <img class="dark:invisible" src="./images/logo/logo.svg" alt="Logo" />
        <img class="hidden dark:block" src="./images/logo/logo-dark.svg" alt="Logo"/>
      </span>
      <img class="logo-icon" :class="sidebarToggle ? 'lg:block' : 'hidden'" src="./images/logo/logo-icon.svg" alt="Logo"/>
    </a>
  </div>
  <!-- SIDEBAR HEADER -->

  <div class="flex flex-col overflow-y-auto duration-300 ease-linear no-scrollbar">
    <!-- Sidebar Menu -->
    <nav x-data="{selected: $persist('Dashboard')}">
      <!-- Menu Group -->
      <div>

        <ul class="flex flex-col gap-4 mb-6">
          <!-- Menu Item Dashboard -->
          {{-- <li>
            <a
              href="#"
              @click.prevent="selected = (selected === 'Dashboard' ? '':'Dashboard')"
              class="menu-item group"
              :class=" (selected === 'Dashboard') || (page === 'ecommerce' || page === 'analytics' || page === 'marketing' || page === 'crm' || page === 'stocks') ? 'menu-item-active' : 'menu-item-inactive'"
            >
              <svg
                :class="(selected === 'Dashboard') || (page === 'ecommerce' || page === 'analytics' || page === 'marketing' || page === 'crm' || page === 'stocks') ? 'menu-item-icon-active'  :'menu-item-icon-inactive'"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M5.5 3.25C4.25736 3.25 3.25 4.25736 3.25 5.5V8.99998C3.25 10.2426 4.25736 11.25 5.5 11.25H9C10.2426 11.25 11.25 10.2426 11.25 8.99998V5.5C11.25 4.25736 10.2426 3.25 9 3.25H5.5ZM4.75 5.5C4.75 5.08579 5.08579 4.75 5.5 4.75H9C9.41421 4.75 9.75 5.08579 9.75 5.5V8.99998C9.75 9.41419 9.41421 9.74998 9 9.74998H5.5C5.08579 9.74998 4.75 9.41419 4.75 8.99998V5.5ZM5.5 12.75C4.25736 12.75 3.25 13.7574 3.25 15V18.5C3.25 19.7426 4.25736 20.75 5.5 20.75H9C10.2426 20.75 11.25 19.7427 11.25 18.5V15C11.25 13.7574 10.2426 12.75 9 12.75H5.5ZM4.75 15C4.75 14.5858 5.08579 14.25 5.5 14.25H9C9.41421 14.25 9.75 14.5858 9.75 15V18.5C9.75 18.9142 9.41421 19.25 9 19.25H5.5C5.08579 19.25 4.75 18.9142 4.75 18.5V15ZM12.75 5.5C12.75 4.25736 13.7574 3.25 15 3.25H18.5C19.7426 3.25 20.75 4.25736 20.75 5.5V8.99998C20.75 10.2426 19.7426 11.25 18.5 11.25H15C13.7574 11.25 12.75 10.2426 12.75 8.99998V5.5ZM15 4.75C14.5858 4.75 14.25 5.08579 14.25 5.5V8.99998C14.25 9.41419 14.5858 9.74998 15 9.74998H18.5C18.9142 9.74998 19.25 9.41419 19.25 8.99998V5.5C19.25 5.08579 18.9142 4.75 18.5 4.75H15ZM15 12.75C13.7574 12.75 12.75 13.7574 12.75 15V18.5C12.75 19.7426 13.7574 20.75 15 20.75H18.5C19.7426 20.75 20.75 19.7427 20.75 18.5V15C20.75 13.7574 19.7426 12.75 18.5 12.75H15ZM14.25 15C14.25 14.5858 14.5858 14.25 15 14.25H18.5C18.9142 14.25 19.25 14.5858 19.25 15V18.5C19.25 18.9142 18.9142 19.25 18.5 19.25H15C14.5858 19.25 14.25 18.9142 14.25 18.5V15Z"
                  fill=""
                />
              </svg>

              <span
                class="menu-item-text "
                :class="sidebarToggle ? 'lg:hidden' : ''"
              >
                Dashboard
              </span>

              <svg
                class="menu-item-arrow"
                :class="[(selected === 'Dashboard') ? 'menu-item-arrow-active' : 'menu-item-arrow-inactive', sidebarToggle ? 'lg:hidden' : '' ]"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M4.79175 7.39584L10.0001 12.6042L15.2084 7.39585"
                  stroke=""
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </a>

            <!-- Dropdown Menu Start -->
            <div
              class="overflow-hidden transform translate"
              :class="(selected === 'Dashboard') ? 'block' :'hidden'"
            >
              <ul
                :class="sidebarToggle ? 'lg:hidden' : 'flex'"
                class="flex flex-col gap-1 mt-2 menu-dropdown pl-9"
              >
                <li>
                  <a
                    href="index.html"
                    class="menu-dropdown-item group"
                    :class="page === 'ecommerce' ? 'menu-dropdown-item-active' : 'menu-dropdown-item-inactive'"
                  >
                    eCommerce
                  </a>
                </li>
              </ul>
            </div>
            <!-- Dropdown Menu End -->
          </li> --}}

            <li>
                    <a href="{{ route('user.dashboard') }}" class="flex text-center justify-center items-center px-4 py-2 text-gray-700 bg-[#0E7D34]  hover:bg-[#39A75E] hover:text-white rounded-lg transition-all duration-200 group"
                     :class=" (selected === 'Dashboard') || (page === 'ecommerce' || page === 'analytics' || page === 'marketing' || page === 'crm' || page === 'stocks') ? 'menu-item-active' : 'menu-item-inactive'">

                    <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16.3333 10.5C16.0028 10.5 15.7259 10.388 15.5027 10.164C15.2794 9.94 15.1674 9.66311 15.1667 9.33333V4.66667C15.1667 4.33611 15.2787 4.05922 15.5027 3.836C15.7267 3.61278 16.0036 3.50078 16.3333 3.5H23.3333C23.6639 3.5 23.9412 3.612 24.1652 3.836C24.3892 4.06 24.5008 4.33689 24.5 4.66667V9.33333C24.5 9.66389 24.388 9.94117 24.164 10.1652C23.94 10.3892 23.6631 10.5008 23.3333 10.5H16.3333ZM4.66667 15.1667C4.33611 15.1667 4.05922 15.0547 3.836 14.8307C3.61278 14.6067 3.50078 14.3298 3.5 14V4.66667C3.5 4.33611 3.612 4.05922 3.836 3.836C4.06 3.61278 4.33689 3.50078 4.66667 3.5H11.6667C11.9972 3.5 12.2745 3.612 12.4985 3.836C12.7225 4.06 12.8341 4.33689 12.8333 4.66667V14C12.8333 14.3306 12.7213 14.6078 12.4973 14.8318C12.2733 15.0558 11.9964 15.1674 11.6667 15.1667H4.66667ZM16.3333 24.5C16.0028 24.5 15.7259 24.388 15.5027 24.164C15.2794 23.94 15.1674 23.6631 15.1667 23.3333V14C15.1667 13.6694 15.2787 13.3926 15.5027 13.1693C15.7267 12.9461 16.0036 12.8341 16.3333 12.8333H23.3333C23.6639 12.8333 23.9412 12.9453 24.1652 13.1693C24.3892 13.3933 24.5008 13.6702 24.5 14V23.3333C24.5 23.6639 24.388 23.9412 24.164 24.1652C23.94 24.3892 23.6631 24.5008 23.3333 24.5H16.3333ZM4.66667 24.5C4.33611 24.5 4.05922 24.388 3.836 24.164C3.61278 23.94 3.50078 23.6631 3.5 23.3333V18.6667C3.5 18.3361 3.612 18.0592 3.836 17.836C4.06 17.6128 4.33689 17.5008 4.66667 17.5H11.6667C11.9972 17.5 12.2745 17.612 12.4985 17.836C12.7225 18.06 12.8341 18.3369 12.8333 18.6667V23.3333C12.8333 23.6639 12.7213 23.9412 12.4973 24.1652C12.2733 24.3892 11.9964 24.5008 11.6667 24.5H4.66667Z" fill="white"/>
                    </svg>
                        <span class="text-white ml-3" :class="sidebarToggle ? 'lg:hidden' : ''">Dashboard</span>
                    </a>
            </li>

          <!-- Menu Item Forms -->
            <li>
                    <a href="{{ route('user.dashboard') }}" class="flex text-center justify-center items-center px-4 py-2 text-[#0E7D34]   hover:bg-[#39A75E] hover:text-white rounded-lg transition-all duration-200 group"
                     :class=" (selected === 'Dashboard') || (page === 'ecommerce' || page === 'analytics' || page === 'marketing' || page === 'crm' || page === 'stocks') ? 'menu-item-active' : 'menu-item-inactive'">
                        <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1.16797 20.0668C1.16797 19.4057 1.3383 18.7982 1.67897 18.2444C2.01964 17.6907 2.47152 17.2676 3.03464 16.9751C4.24019 16.3723 5.46519 15.9204 6.70964 15.6194C7.95408 15.3184 9.21797 15.1676 10.5013 15.1668C11.7846 15.166 13.0485 15.3169 14.293 15.6194C15.5374 15.922 16.7624 16.3739 17.968 16.9751C18.5319 17.2668 18.9841 17.6899 19.3248 18.2444C19.6655 18.799 19.8354 19.4064 19.8346 20.0668V21.0001C19.8346 21.6418 19.6064 22.1913 19.1498 22.6486C18.6932 23.1059 18.1437 23.3342 17.5013 23.3334H3.5013C2.85964 23.3334 2.31052 23.1052 1.85397 22.6486C1.39741 22.1921 1.16875 21.6426 1.16797 21.0001V20.0668ZM21.5263 23.3334C21.7402 22.9834 21.9008 22.6093 22.0081 22.2111C22.1155 21.8129 22.1687 21.4092 22.168 21.0001V19.8334C22.168 18.9779 21.93 18.1562 21.454 17.3683C20.978 16.5804 20.3021 15.9049 19.4263 15.3418C20.418 15.4584 21.3513 15.6579 22.2263 15.9403C23.1013 16.2226 23.918 16.5676 24.6763 16.9751C25.3763 17.364 25.911 17.7964 26.2805 18.2724C26.6499 18.7484 26.8346 19.2688 26.8346 19.8334V21.0001C26.8346 21.6418 26.6064 22.1913 26.1498 22.6486C25.6932 23.1059 25.1437 23.3342 24.5013 23.3334H21.5263ZM10.5013 14.0001C9.21797 14.0001 8.11936 13.5432 7.20547 12.6293C6.29158 11.7154 5.83464 10.6168 5.83464 9.33345C5.83464 8.05011 6.29158 6.9515 7.20547 6.03761C8.11936 5.12372 9.21797 4.66678 10.5013 4.66678C11.7846 4.66678 12.8832 5.12372 13.7971 6.03761C14.711 6.9515 15.168 8.05011 15.168 9.33345C15.168 10.6168 14.711 11.7154 13.7971 12.6293C12.8832 13.5432 11.7846 14.0001 10.5013 14.0001ZM22.168 9.33345C22.168 10.6168 21.711 11.7154 20.7971 12.6293C19.8832 13.5432 18.7846 14.0001 17.5013 14.0001C17.2874 14.0001 17.0152 13.976 16.6846 13.9278C16.3541 13.8796 16.0819 13.8259 15.868 13.7668C16.393 13.1446 16.7966 12.4543 17.079 11.6959C17.3613 10.9376 17.5021 10.1501 17.5013 9.33345C17.5005 8.51678 17.3597 7.72928 17.079 6.97094C16.7982 6.21261 16.3945 5.52233 15.868 4.90011C16.1402 4.80289 16.4124 4.7395 16.6846 4.70994C16.9569 4.68039 17.2291 4.666 17.5013 4.66678C18.7846 4.66678 19.8832 5.12372 20.7971 6.03761C21.711 6.9515 22.168 8.05011 22.168 9.33345Z" fill="#0E7D34"/>
                        </svg>
                        <span class="text-[#0E7D34] ml-3  hover:text-white" :class="sidebarToggle ? 'lg:hidden' : ''">Groups</span>
                    </a>
            </li>

             <li>
                    <a href="{{ route('user.dashboard') }}" class="flex text-center justify-center items-center px-4 py-2 text-[#0E7D34]  hover:bg-[#39A75E] hover:text-white rounded-lg transition-all duration-200 group"
                     :class=" (selected === 'Dashboard') || (page === 'ecommerce' || page === 'analytics' || page === 'marketing' || page === 'crm' || page === 'stocks') ? 'menu-item-active' : 'menu-item-inactive'">
                        <svg width="27" height="25" viewBox="0 0 27 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11.6667 5.41679C11.6665 4.52318 11.8944 3.64432 12.3289 2.86342C12.7633 2.08253 13.3899 1.42544 14.1492 0.954398C14.9086 0.483352 15.7757 0.213932 16.6683 0.171652C17.5609 0.129372 18.4495 0.31563 19.25 0.712789C20.0504 0.316103 20.9388 0.130222 21.8311 0.172738C22.7235 0.215253 23.5902 0.484759 24.3492 0.95575C25.1083 1.42674 25.7347 2.08364 26.169 2.86427C26.6033 3.64491 26.8313 4.52346 26.8313 5.41679C26.8313 6.31012 26.6033 7.18867 26.169 7.96931C25.7347 8.74994 25.1083 9.40684 24.3492 9.87783C23.5902 10.3488 22.7235 10.6183 21.8311 10.6608C20.9388 10.7034 20.0504 10.5175 19.25 10.1208C18.4495 10.5179 17.5609 10.7042 16.6683 10.6619C15.7757 10.6196 14.9086 10.3502 14.1492 9.87918C13.3899 9.40813 12.7633 8.75105 12.3289 7.97015C11.8944 7.18926 11.6665 6.3104 11.6667 5.41679ZM21.2917 8.31946C21.3873 8.32879 21.4846 8.33346 21.5833 8.33346C21.9781 8.3326 22.3686 8.25161 22.7312 8.09539C23.0937 7.93917 23.4208 7.71097 23.6926 7.42462C23.9644 7.13827 24.1752 6.79973 24.3122 6.42951C24.4493 6.05928 24.5098 5.66508 24.49 5.27079C24.4703 4.8765 24.3707 4.49033 24.1973 4.13567C24.0239 3.78101 23.7803 3.46524 23.4812 3.2075C23.1822 2.94976 22.8339 2.7554 22.4576 2.63622C22.0812 2.51704 21.6846 2.4755 21.2917 2.51412C21.8624 3.37458 22.1668 4.38424 22.1667 5.41679C22.1668 6.44934 21.8624 7.459 21.2917 8.31946ZM9.84083 14.4585C9.64928 14.458 9.4595 14.4952 9.28234 14.5681C9.10518 14.6409 8.94411 14.748 8.80833 14.8831L6.41667 17.2748V21.7501H12.9815L19.7517 20.0585L23.8723 18.2991C24.0127 18.2233 24.1201 18.0983 24.174 17.9481C24.2278 17.7979 24.2243 17.6331 24.1642 17.4854C24.104 17.3376 23.9915 17.2172 23.848 17.1474C23.7046 17.0775 23.5404 17.0631 23.387 17.1068L23.3637 17.1126L15.883 18.8335H11.6667V16.5001H15.3125C15.5832 16.5001 15.8429 16.3926 16.0343 16.2011C16.2258 16.0097 16.3333 15.75 16.3333 15.4793C16.3333 15.2085 16.2258 14.9489 16.0343 14.7575C15.8429 14.566 15.5832 14.4585 15.3125 14.4585H9.84083ZM18.6515 15.8025L22.7955 14.8493C23.2369 14.7332 23.6989 14.7199 24.1463 14.8102C24.5936 14.9006 25.0143 15.0922 25.376 15.3705C25.7377 15.6487 26.0309 16.0062 26.233 16.4153C26.435 16.8245 26.5406 17.2746 26.5417 17.731C26.5412 18.2849 26.3867 18.8279 26.0953 19.2991C25.804 19.7702 25.3874 20.1511 24.892 20.3991L24.8605 20.4155L20.4972 22.2763L13.2685 24.0835H0V15.6251H4.767L7.161 13.2311C7.51373 12.8796 7.93224 12.601 8.39266 12.4112C8.85307 12.2214 9.34635 12.1242 9.84433 12.1251H15.3125C15.7803 12.1251 16.2429 12.2228 16.6706 12.4122C17.0983 12.6015 17.4817 12.8782 17.7962 13.2245C18.1106 13.5708 18.3492 13.9791 18.4965 14.423C18.6438 14.867 18.6966 15.3369 18.6515 15.8025ZM4.08333 17.9585H2.33333V21.7501H4.08333V17.9585Z" fill="#0E7D34"/>
                        </svg>
                        <span class="text-[#0E7D34] ml-3" :class="sidebarToggle ? 'lg:hidden' : ''">Transactions</span>
                    </a>
            </li>

             <li>
                    <a href="{{ route('user.dashboard') }}" class="flex text-center justify-center items-center px-4 py-2 text-[#0E7D34]   hover:bg-[#39A75E] hover:text-white rounded-lg transition-all duration-200 group"
                     :class=" (selected === 'Dashboard') || (page === 'ecommerce' || page === 'analytics' || page === 'marketing' || page === 'crm' || page === 'stocks') ? 'menu-item-active' : 'menu-item-inactive'">
                        <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19.1667 0.666748C19.7855 0.666748 20.379 0.912581 20.8166 1.35017C21.2542 1.78775 21.5 2.38124 21.5 3.00008V4.16675H14.5C12.9529 4.16675 11.4692 4.78133 10.3752 5.87529C9.28125 6.96925 8.66667 8.45298 8.66667 10.0001C8.66667 11.5472 9.28125 13.0309 10.3752 14.1249C11.4692 15.2188 12.9529 15.8334 14.5 15.8334H21.5V17.0001C21.5 17.6189 21.2542 18.2124 20.8166 18.65C20.379 19.0876 19.7855 19.3334 19.1667 19.3334H2.83333C2.21449 19.3334 1.621 19.0876 1.18342 18.65C0.745833 18.2124 0.5 17.6189 0.5 17.0001V3.00008C0.5 2.38124 0.745833 1.78775 1.18342 1.35017C1.621 0.912581 2.21449 0.666748 2.83333 0.666748H19.1667ZM20.3333 6.50008C20.9522 6.50008 21.5457 6.74591 21.9832 7.1835C22.4208 7.62108 22.6667 8.21458 22.6667 8.83341V11.1667C22.6667 11.7856 22.4208 12.3791 21.9832 12.8167C21.5457 13.2542 20.9522 13.5001 20.3333 13.5001H14.5C13.5717 13.5001 12.6815 13.1313 12.0251 12.475C11.3687 11.8186 11 10.9283 11 10.0001C11 9.07182 11.3687 8.18158 12.0251 7.52521C12.6815 6.86883 13.5717 6.50008 14.5 6.50008H20.3333ZM14.5 8.83341C14.1906 8.83341 13.8938 8.95633 13.675 9.17512C13.4562 9.39392 13.3333 9.69066 13.3333 10.0001C13.3333 10.3095 13.4562 10.6062 13.675 10.825C13.8938 11.0438 14.1906 11.1667 14.5 11.1667C14.8094 11.1667 15.1062 11.0438 15.325 10.825C15.5437 10.6062 15.6667 10.3095 15.6667 10.0001C15.6667 9.69066 15.5437 9.39392 15.325 9.17512C15.1062 8.95633 14.8094 8.83341 14.5 8.83341Z" fill="#0E7D34"/>
                        </svg>
                        <span class="text-[#0E7D34] ml-3" :class="sidebarToggle ? 'lg:hidden' : ''">Wallet</span>
                    </a>
            </li>

             <li>
                    <a href="{{ route('user.dashboard') }}" class="flex text-center justify-center items-center px-4 py-2 text-[#0E7D34]  hover:bg-[#39A75E] hover:text-white rounded-lg transition-all duration-200 group"
                     :class=" (selected === 'Dashboard') || (page === 'ecommerce' || page === 'analytics' || page === 'marketing' || page === 'crm' || page === 'stocks') ? 'menu-item-active' : 'menu-item-inactive'">
                        <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10.5713 2.25176C8.3663 2.24009 6.21964 4.36342 7.19964 7.00009H3.5013C2.88246 7.00009 2.28897 7.24592 1.85139 7.68351C1.4138 8.12109 1.16797 8.71458 1.16797 9.33342V11.6668C1.16797 11.9762 1.29089 12.2729 1.50968 12.4917C1.72847 12.7105 2.02522 12.8334 2.33464 12.8334H12.8346V9.33342H15.168V12.8334H25.668C25.9774 12.8334 26.2741 12.7105 26.4929 12.4917C26.7117 12.2729 26.8346 11.9762 26.8346 11.6668V9.33342C26.8346 8.71458 26.5888 8.12109 26.1512 7.68351C25.7136 7.24592 25.1201 7.00009 24.5013 7.00009H20.803C22.168 3.18509 17.0346 0.49009 14.6663 3.78009L14.0013 4.66676L13.3363 3.75676C12.6013 2.71842 11.5863 2.26342 10.5713 2.25176ZM10.5013 4.66676C11.5396 4.66676 12.0646 5.92676 11.3296 6.66176C10.5946 7.39676 9.33463 6.87176 9.33463 5.83342C9.33463 5.524 9.45755 5.22726 9.67634 5.00847C9.89514 4.78967 10.1919 4.66676 10.5013 4.66676ZM17.5013 4.66676C18.5396 4.66676 19.0646 5.92676 18.3296 6.66176C17.5946 7.39676 16.3346 6.87176 16.3346 5.83342C16.3346 5.524 16.4576 5.22726 16.6763 5.00847C16.8951 4.78967 17.1919 4.66676 17.5013 4.66676ZM2.33464 14.0001V23.3334C2.33464 23.9523 2.58047 24.5458 3.01805 24.9833C3.45564 25.4209 4.04913 25.6668 4.66797 25.6668H23.3346C23.9535 25.6668 24.547 25.4209 24.9846 24.9833C25.4221 24.5458 25.668 23.9523 25.668 23.3334V14.0001H15.168V23.3334H12.8346V14.0001H2.33464Z" fill="#0E7D34"/>
                        <path d="M10.7901 25.6666L10.3234 21.9333C10.0707 21.836 9.83266 21.7194 9.60944 21.5833C9.38622 21.4471 9.16727 21.3013 8.9526 21.1458L5.48177 22.6041L2.27344 17.0624L5.2776 14.7874C5.25816 14.6513 5.24844 14.5203 5.24844 14.3943V13.6068C5.24844 13.48 5.25816 13.3485 5.2776 13.2124L2.27344 10.9374L5.48177 5.39575L8.9526 6.85408C9.16649 6.69853 9.3901 6.5527 9.62344 6.41658C9.85677 6.28047 10.0901 6.16381 10.3234 6.06658L10.7901 2.33325H17.2068L17.6734 6.06658C17.9262 6.16381 18.1646 6.28047 18.3886 6.41658C18.6126 6.5527 18.8312 6.69853 19.0443 6.85408L22.5151 5.39575L25.7234 10.9374L22.7193 13.2124C22.7387 13.3485 22.7484 13.48 22.7484 13.6068V14.3931C22.7484 14.5199 22.729 14.6513 22.6901 14.7874L25.6943 17.0624L22.4859 22.6041L19.0443 21.1458C18.8304 21.3013 18.6068 21.4471 18.3734 21.5833C18.1401 21.7194 17.9068 21.836 17.6734 21.9333L17.2068 25.6666H10.7901ZM14.0568 18.0833C15.1845 18.0833 16.147 17.6846 16.9443 16.8874C17.7415 16.0902 18.1401 15.1277 18.1401 13.9999C18.1401 12.8721 17.7415 11.9096 16.9443 11.1124C16.147 10.3152 15.1845 9.91658 14.0568 9.91658C12.9095 9.91658 11.942 10.3152 11.1541 11.1124C10.3662 11.9096 9.97266 12.8721 9.97344 13.9999C9.97422 15.1277 10.3682 16.0902 11.1553 16.8874C11.9424 17.6846 12.9095 18.0833 14.0568 18.0833Z" fill="#0E7D34"/>
                        </svg>
                        <span class="text-[#0E7D34] ml-3" :class="sidebarToggle ? 'lg:hidden' : ''">Rewards</span>
                    </a>
            </li>

             <li>
                    <a href="{{ route('user.profile.settings') }}" class="flex text-center justify-center items-center px-4 py-2 text-[#0E7D34]   hover:bg-[#39A75E] hover:text-white rounded-lg transition-all duration-200 group"
                     :class=" (selected === 'Dashboard') || (page === 'ecommerce' || page === 'analytics' || page === 'marketing' || page === 'crm' || page === 'stocks') ? 'menu-item-active' : 'menu-item-inactive'">
                        <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10.7901 25.6666L10.3234 21.9333C10.0707 21.836 9.83266 21.7194 9.60944 21.5833C9.38622 21.4471 9.16727 21.3013 8.9526 21.1458L5.48177 22.6041L2.27344 17.0624L5.2776 14.7874C5.25816 14.6513 5.24844 14.5203 5.24844 14.3943V13.6068C5.24844 13.48 5.25816 13.3485 5.2776 13.2124L2.27344 10.9374L5.48177 5.39575L8.9526 6.85408C9.16649 6.69853 9.3901 6.5527 9.62344 6.41658C9.85677 6.28047 10.0901 6.16381 10.3234 6.06658L10.7901 2.33325H17.2068L17.6734 6.06658C17.9262 6.16381 18.1646 6.28047 18.3886 6.41658C18.6126 6.5527 18.8312 6.69853 19.0443 6.85408L22.5151 5.39575L25.7234 10.9374L22.7193 13.2124C22.7387 13.3485 22.7484 13.48 22.7484 13.6068V14.3931C22.7484 14.5199 22.729 14.6513 22.6901 14.7874L25.6943 17.0624L22.4859 22.6041L19.0443 21.1458C18.8304 21.3013 18.6068 21.4471 18.3734 21.5833C18.1401 21.7194 17.9068 21.836 17.6734 21.9333L17.2068 25.6666H10.7901ZM14.0568 18.0833C15.1845 18.0833 16.147 17.6846 16.9443 16.8874C17.7415 16.0902 18.1401 15.1277 18.1401 13.9999C18.1401 12.8721 17.7415 11.9096 16.9443 11.1124C16.147 10.3152 15.1845 9.91658 14.0568 9.91658C12.9095 9.91658 11.942 10.3152 11.1541 11.1124C10.3662 11.9096 9.97266 12.8721 9.97344 13.9999C9.97422 15.1277 10.3682 16.0902 11.1553 16.8874C11.9424 17.6846 12.9095 18.0833 14.0568 18.0833Z" fill="#0E7D34"/>
                        </svg>
                        <span class="text-[#0E7D34] ml-3" :class="sidebarToggle ? 'lg:hidden' : ''">Settings</span>
                    </a>
            </li>
        </ul>
      </div>


    </nav>

  </div>
</aside>

