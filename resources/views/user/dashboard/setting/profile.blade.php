@extends('user.dashboard.layouts.app')
@section('title', 'Profile')
@section('content')


    <div class="max-w-full ">

        <div class="w-full p-6 mx-auto">
            @if (session('success'))
                <div class="relative  px-4 py-3 mb-5 text-teal-700 bg-teal-100 border border-teal-400 rounded" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline">{{ session('success') }}</span>
                    <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                        class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                        <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20">
                            <title>Close</title>
                            <path
                                d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                        </svg>
                    </button>
                </div>
                @endif

                    @if (session('error'))
                    <div class="relative px-4 py-3 mb-5 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
                        <strong class="font-bold">Error!</strong>
                        <span class="block sm:inline">{{ session('error') }}</span>
                        <button type="button" onclick="return this.parentNode.remove()" aria-label="Close"
                            class="absolute top-0 bottom-0 right-0 px-4 py-3" aria-hidden="true">
                            <svg class="w-6 h-6 text-red-500 fill-current" role="button" xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20">
                                <title>Close</title>
                                <path
                                    d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                            </svg>
                        </button>
                    </div>
                    @endif


<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-6">

                <!-- Navigation -->
                <nav>
                    <ul class="space-y-2">
                        <li>
                            <a href="#account" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group active">
                                <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                </svg>
                                My Account
                            </a>
                        </li>
                        <li>
                            <a href="#security" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                                </svg>
                                Security
                            </a>
                        </li>
                        <li>
                            <a href="#notifications" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
                                </svg>
                                Notifications
                            </a>
                        </li>
                        <li>
                            <a href="#support" class="settings-tab flex items-center px-4 py-3 text-gray-700 hover:bg-[#0E7D34] hover:text-white rounded-lg transition-all duration-200 group">
                                <svg class="w-5 h-5 mr-3 text-gray-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                </svg>
                                Support
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- My Account Section -->
            <div class="flex justify-between mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">My Account</h2>
                <a href="" class=" text-[#0E7D34] px-4 py-2 rounded-lg text-sm font-medium">Verify</a>
            </div>
            <div id="account-content" class="settings-content">
                <div class="bg-white rounded-lg shadow-sm p-6">

                    <!-- Profile Picture -->
                     <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                                    <div class="items-center flex space-x-4" >
                                        <img class="mb-4 rounded-lg w-32 h-32 sm:mb-0 xl:mb-4 2xl:mb-0"  src="{{asset('/storage/uploads/'.Auth::user()->profileimages) }}" id="preview-img" alt="{{ Auth::user()->profile_image}}">
                                    <div>
                                        <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">Profile picture</h3>
                                        <div class="mb-4 text-sm text-gray-500 dark:text-gray-400">
                                            JPG, GIF or PNG. Max size of 1MB
                                        </div>
                                        <form action="{{ route('user.profile.image.update', auth()->user()->id)}}"  method="POST" enctype="multipart/form-data">
                                            @csrf

                                            <input type="file" id="profileimages" name="profileimages" accept="image/jpeg,image/png,image/jpg,image/gif,image/svg+xml"
                                                class="block w-full p-3 mt-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('profileimages') border-red @enderror"
                                                onchange="previewImage(this)"/>
                                            <div class="flex items-center space-x-4">
                                                <button type="submit" class="mt-4 mb-12 px-4 py-2 bg-[#0E7D34] hover:bg-[#39A75E] dark:bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-white dark:text-gray-800 uppercase tracking-widest  dark:hover:bg-white focus:bg-gray-700 dark:focus:bg-white active:bg-gray-900 dark:active:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                                    Save Continue
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                    {{-- <div class="mb-8">
                        <div class="flex items-center space-x-6">
                            <div class="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center">
                                <svg class="w-10 h-10 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div>
                                <button class="bg-[#0E7D34] hover:bg-[#39A75E] text-white px-4 py-2 rounded-lg text-sm font-medium">
                                    Change Photo
                                </button>
                                <p class="text-sm text-gray-500 mt-1">JPG, GIF or PNG. 1MB max.</p>
                            </div>
                        </div>
                    </div> --}}

                    <!-- Account Form -->
                    {{-- <form class="space-y-6" action="{{route('user.profile.update', auth()->user()->id)}}" method="POST">
                        @method('PUT')
                        @csrf
                        <!--header -->
                        <div class="mt-4 text-gray-700 text-xl font-bold">
                            <p>Personal Information</p>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                <input type="text" name="first_name" value="{{  auth()->user()->first_name }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                <input type="text" name="last_name" value="{{  auth()->user()->last_name }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" >
                            </div>
                        </div>

                         <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                <input type="email" name="email" value="{{  auth()->user()->email }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]" >
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                <input type="tel"name="phone" value="{{  auth()->user()->phone }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]">
                            </div>
                        </div>

                        <div class="mt-4 text-gray-700 text-xl font-bold">
                            <p> Address</p>
                        </div>

                        <!-- locality -->
                                <div class="grid col-span-3 gap-4 mt-4 mb-8" x-data="{
                                    loading:false,
                                    response:{},
                                        states: [],
                                        async getStates(event) {
                                        this.response = await (await fetch('/location/state/'+event.target.value+'/json', {
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                                        }

                                        })).json();
                                        if(this.response.status == 200){
                                            this.loading=false;
                                            this.states = this.response.data;
                                        }
                            }
                                }">
                                    <div class="col-md-4">
                                        <x-input-label for="country" :value="__('Select Country')" class="block mb-2 text-md font-medium text-gray-900 dark:text-white " />
                                        <div class="relative">

                                        <x-select-input id="country"
                                        x-on:change="loading =! loading"  @change="getStates"
                                        class="block w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                        name="country_id"   autofocus >
                                         <option value="">Select</option>
                                        @foreach($countries as $country)
                                        <option  {{ @old('country')== $country->id?'selected':'' }}  value="{{ $country->id }}">{{ $country->name }}</option>
                                        @endforeach
                                        </x-select-input>
                                        </div>
                                        <x-input-error :messages="$errors->get('country')" class="mt-2" />
                                    </div>


                                    <div class="col-md-8 mb-1" >
                                    <div class="grid grid-cols-2 gap-4" x-data="{
                                        loading:false,
                                        response:{},
                                            cities: [],
                                            async getCities(event) {
                                            this.response = await (await fetch('/location/cities/'+event.target.value+'/json', {
                                            headers: {
                                                'Content-Type': 'application/json',
                                                'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                                            }

                                            })).json();
                                            if(this.response.status == 200){
                                                this.loading=false;
                                                this.cities = this.response.data;
                                            }
                                }
                                    }">
                                    <div class="col">
                                        <div class="d-flex">
                                            <x-input-label for="state" class="d-inline mb-2 text-md font-medium text-gray-900 dark:text-white flex ">{{ __('Select State') }}

                                            </x-input-label>
                                            <div role="status" x-show="loading" class="ml-3">
                                                <svg aria-hidden="loading" width="16px" height="16px" class="w-5 h-5 text-gray-200 d-inline animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                                    <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                                </svg>
                                                <span class="sr-only">Loading...</span>
                                            </div>
                                        </div>

                                        <div class="relative mb-2">

                                        <x-select-input id="state"
                                        x-on:change="loading =! loading"  @change="getCities"
                                        class="block mt-1 w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                        name="state_id"   autofocus >
                                        <option value="">Select</option>
                                        @foreach ($states as $state)
                                                <option
                                                    {{ @old('state_id') == $state->id || $state->id == $user->state_id ? 'selected' : '' }}
                                                    value="{{ $state->id }}">
                                                    {{ $state->name }} </option>
                                            @endforeach
                                            <template x-for="state in states"
                                                :key="state.id">
                                                <option :value="state.id"
                                                    x-text="state.name"></option>
                                            </template>

                                        </x-select-input>
                                        </div>
                                        <x-input-error :messages="$errors->get('state')" class="mt-2" />


                                    </div>


                                    <div class="col">
                                        <div class="d-flex">
                                        <x-input-label for="city" class="d-inline mb-2 text-md font-medium text-gray-900 dark:text-white flex ">{{ __('Select City') }}

                                        </x-input-label>
                                        <div role="status" x-show="loading" class="ml-3">
                                            <svg aria-hidden="loading" width="16px" height="16px" class="w-5 h-5 d-inline text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                                <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                            </svg>
                                            <span class="sr-only">Loading...</span>
                                        </div>
                                        </div>
                                        <div class="relative mb-2">

                                        <x-select-input id="city"

                                        class="block mt-1 w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                        name="city_id"   autofocus >
                                         @foreach ($cities as $city)
                                            <option
                                                {{ @old('city_id') == $city->id || $city->id == $user->city_id ? 'selected' : '' }}
                                                value="{{ $city->id }}">
                                                {{ $city->name }} </option>
                                        @endforeach
                                        <template x-for="city in cities" :key="city.id">
                                            <option :value="city.id" x-text="city.name"></option>
                                        </template>
                                    </x-select-input>
                                        </div>
                                        <x-input-error :messages="$errors->get('city')" class="mt-2" />
                                    </div>
                                    </div>
                                    </div>
                                </div>


                        <div class="flex justify-end">
                            <button type="submit" class="bg-[#0E7D34] hover:bg-[#39A75E] text-white px-6 py-2 rounded-lg font-medium">
                                Save Changes
                            </button>
                        </div>
                    </form> --}}



                    <form id="UpdateProfile" action="{{ route('user.profile.update', auth()->user()->id) }}" method="POST"> {{$errors}}
                                    @method('PUT')
                                    @csrf
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="firstname" :value="__('First Name')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="firstname"
                                            placeholder="First Name"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-[#0E7D34] focus:border-[#0E7D34] dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="text" name="first_name" :value="old('first_name')??$user->first_name"  autofocus autocomplete="First Name" />
                                            </div>
                                            <x-input-error :messages="$errors->get('first_name')" class="mt-2" />
                                        </div>
                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="lastname" :value="__('Last Name')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="lastname"
                                            placeholder="Last Name"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="text" name="last_name" :value="old('last_name')??$user->last_name"  autofocus autocomplete="Last Name" />
                                            </div>
                                            <x-input-error :messages="$errors->get('last_name')" class="mt-2" />
                                        </div>
                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="other_names" :value="__('Other Names')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="other_names"
                                            placeholder="Other Name"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="text" name="other_names" :value="old('other_names')??$user->other_names"  autofocus autocomplete="Other Names" />
                                            </div>
                                            <x-input-error :messages="$errors->get('other_names')" class="mt-2" />
                                        </div>

                                        <div class="col-span-2 sm:col-span-2">
                                            <x-input-label for="address" :value="__('Address')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="address"
                                            placeholder="Address"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="text" name="address" :value="old('address')??$user->address"  autofocus autocomplete="Address" />
                                            </div>
                                            <x-input-error :messages="$errors->get('address')" class="mt-2" />
                                        </div>
                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="email" :value="__('Email Address')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="email"
                                            placeholder="Email Address"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="text" name="email" :value="old('email')??$user->email"  disabled="true" readonly="readonly"  autofocus autocomplete="Email Address" />
                                            </div>
                                            <x-input-error :messages="$errors->get('email')" class="mt-2" />
                                        </div>
                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="phone" :value="__('Phone Number')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-input id="phone"
                                            placeholder="Phone Number"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="number" name="phone" :value="old('phone')??$user->phone"   autofocus autocomplete="Phone Number" />
                                            </div>
                                            <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                                        </div>


                                        <div class="col-span-1 sm:col-span-1">
                                            <x-input-label for="gender" :value="__('Select Gender')" class="block  text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2 ">
                                                <x-input-icon>
                                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16 19h4a1 1 0 0 0 1-1v-1a3 3 0 0 0-3-3h-2m-2.236-4a3 3 0 1 0 0-4M3 18v-1a3 3 0 0 1 3-3h4a3 3 0 0 1 3 3v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                                    </svg>

                                                </x-input-icon>
                                            <x-select-input id="gender"
                                            class="block mt-1 w-full p-4 font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            name="gender"   autofocus >
                                              @foreach (App\Enums\Gender::cases() as $gender)
                                                <option value="{{$gender->value}}" {{(@old('gender')==$gender->value || $user->gender==$gender->value)?'selected':''}}>{{$gender->name}}</option>
                                            @endforeach


                                        </x-select-input>
                                            </div>
                                            <x-input-error :messages="$errors->get('gender')" class="mt-2" />
                                        </div>
                                        <div class="col-span-1 sm:col-span-1">
                                            {{-- <x-input-label for="birthday" :value="__('Birthday')" class="block  text-md font-medium text-gray-900 dark:text-white " />
                                            <div class="relative mb-2">

                                            <x-text-datepicker id="birthday"
                                            placeholder="{{old('data_of_birth')??$user->data_of_birth}}"
                                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            type="text" name="data_of_birth" :min="\Carbon\Carbon::now()->subYears(70)->format('Y-m-d')" :max="\Carbon\Carbon::now()->subYears(12)->format('Y-m-d')" :value="old('data_of_birth')??$user->data_of_birth"   autofocus autocomplete="Birthday" />
                                            </div> --}}
                                            <label for="date" class="block text-sm font-medium text-gray-700 mb-2">
                                            Date of Birth
                                        </label>
                                         <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                               <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 10h16m-8-3V4M7 7V4m10 3V4M5 20h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Zm3-7h.01v.01H8V13Zm4 0h.01v.01H12V13Zm4 0h.01v.01H16V13Zm-8 4h.01v.01H8V17Zm4 0h.01v.01H12V17Zm4 0h.01v.01H16V17Z"/>
                                                </svg>
                                                </div>
                                                <input type="date" id="date" value="{{old('data_of_birth')??$user->data_of_birth}}"  placeholder="{{old('data_of_birth')??$user->data_of_birth}}"  name="data_of_birth" class="bg-gray-50 font-bold block p-4 border border-gray-300 text-gray-900 text-sm sm:text-md rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required>
                                        </div>
                                            <x-input-error :messages="$errors->get('data_of_birth')" class="mt-2" />
                                        </div>

                                         <!-- locality -->
                                <div class="grid col-span-3 gap-4 mt-4 mb-8" x-data="{
                                    loading:false,
                                    response:{},
                                        states: [],
                                        async getStates(event) {
                                        this.response = await (await fetch('/location/state/'+event.target.value+'/json', {
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                                        }

                                        })).json();
                                        if(this.response.status == 200){
                                            this.loading=false;
                                            this.states = this.response.data;
                                        }
                            }
                                }">
                                    <div class="col-md-4">
                                        <x-input-label for="country" :value="__('Select Country')" class="block mb-2 text-md font-medium text-gray-900 dark:text-white " />
                                        <div class="relative">

                                        <x-select-input id="country"
                                        x-on:change="loading =! loading"  @change="getStates"
                                        class="block w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                        name="country_id"   autofocus >
                                         <option value="">Select</option>
                                        @foreach($countries as $country)
                                        <option  {{ @old('country')== $country->id?'selected':'' }}  value="{{ $country->id }}">{{ $country->name }}</option>
                                        @endforeach
                                        </x-select-input>
                                        </div>
                                        <x-input-error :messages="$errors->get('country')" class="mt-2" />
                                    </div>

                                    <div class="col-md-8 mb-1" >
                                    <div class="grid grid-cols-2 gap-4" x-data="{
                                        loading:false,
                                        response:{},
                                            cities: [],
                                            async getCities(event) {
                                            this.response = await (await fetch('/location/cities/'+event.target.value+'/json', {
                                            headers: {
                                                'Content-Type': 'application/json',
                                                'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                                            }

                                            })).json();
                                            if(this.response.status == 200){
                                                this.loading=false;
                                                this.cities = this.response.data;
                                            }
                                }
                                    }">
                                    <div class="col">
                                        <div class="d-flex">
                                            <x-input-label for="state" class="d-inline mb-2 text-md font-medium text-gray-900 dark:text-white flex ">{{ __('Select State') }}

                                            </x-input-label>
                                            <div role="status" x-show="loading" class="ml-3">
                                                <svg aria-hidden="loading" width="16px" height="16px" class="w-5 h-5 text-gray-200 d-inline animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                                    <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                                </svg>
                                                <span class="sr-only">Loading...</span>
                                            </div>
                                        </div>

                                        <div class="relative mb-2">

                                        <x-select-input id="state"
                                        x-on:change="loading =! loading"  @change="getCities"
                                        class="block mt-1 w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                        name="state_id"   autofocus >
                                        <option value="">Select</option>
                                        @foreach ($states as $state)
                                                <option
                                                    {{ @old('state_id') == $state->id || $state->id == $user->state_id ? 'selected' : '' }}
                                                    value="{{ $state->id }}">
                                                    {{ $state->name }} </option>
                                            @endforeach
                                            <template x-for="state in states"
                                                :key="state.id">
                                                <option :value="state.id"
                                                    x-text="state.name"></option>
                                            </template>

                                        </x-select-input>
                                        </div>
                                        <x-input-error :messages="$errors->get('state')" class="mt-2" />


                                    </div>


                                    <div class="col">
                                        <div class="d-flex">
                                        <x-input-label for="city" class="d-inline mb-2 text-md font-medium text-gray-900 dark:text-white flex ">{{ __('Select City') }}

                                        </x-input-label>
                                        <div role="status" x-show="loading" class="ml-3">
                                            <svg aria-hidden="loading" width="16px" height="16px" class="w-5 h-5 d-inline text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                                <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                            </svg>
                                            <span class="sr-only">Loading...</span>
                                        </div>
                                        </div>
                                        <div class="relative mb-2">

                                        <x-select-input id="city"

                                        class="block mt-1 w-full p-2 form-control font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                        name="city_id"   autofocus >
                                         @foreach ($cities as $city)
                                            <option
                                                {{ @old('city_id') == $city->id || $city->id == $user->city_id ? 'selected' : '' }}
                                                value="{{ $city->id }}">
                                                {{ $city->name }} </option>
                                        @endforeach
                                        <template x-for="city in cities" :key="city.id">
                                            <option :value="city.id" x-text="city.name"></option>
                                        </template>
                                    </x-select-input>
                                        </div>
                                        <x-input-error :messages="$errors->get('city')" class="mt-2" />
                                    </div>
                                    </div>
                                    </div>
                                </div>




                                        <button type="submit"
                                             class=" mb-12 px-4 py-2 bg-[#0E7D34] hover:bg-[#39A75E] dark:bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-white dark:text-gray-800 uppercase tracking-widest dark:hover:bg-white focus:bg-gray-700 dark:focus:bg-white active:bg-gray-900 dark:active:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                            Save
                                        </button>
                                    </div>
                                </form>
                </div>
            </div>

            <!-- Security Section -->
            <div id="security-content" class="settings-content hidden">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Security</h2>

                    <!-- Change Password -->
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Change Password</h3>
                        <form class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                                <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                                <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                                <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0E7D34]">
                            </div>
                            <button type="submit" class="bg-[#0E7D34] hover:bg-[#39A75E] text-white px-6 py-2 rounded-lg font-medium">
                                Update Password
                            </button>
                        </form>
                    </div>

                    <!-- Two-Factor Authentication -->
                    <div class="border-t pt-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Two-Factor Authentication</h3>
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <p class="font-medium text-gray-900">Enable 2FA</p>
                                <p class="text-sm text-gray-500">Add an extra layer of security to your account</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#0E7D34]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0E7D34]"></div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications Section -->
            <div id="notifications-content" class="settings-content hidden">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Notifications</h2>

                    <div class="space-y-6">
                        <!-- Email Notifications -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Email Notifications</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Transaction Alerts</p>
                                        <p class="text-sm text-gray-500">Get notified about your transactions</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" checked>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#0E7D34]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0E7D34]"></div>
                                    </label>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Group Updates</p>
                                        <p class="text-sm text-gray-500">Updates about your Osusu groups</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" checked>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#0E7D34]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0E7D34]"></div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Push Notifications -->
                        <div class="border-t pt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Push Notifications</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Payment Reminders</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


            </div>
        </div>


@endsection


<script>
    document.addEventListener("DOMContentLoaded", function () {
        const tabs = document.querySelectorAll('.settings-tab');
        const contents = document.querySelectorAll('.settings-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', function (e) {
                e.preventDefault();

                // Remove 'active' class from all tabs
                tabs.forEach(t => t.classList.remove('active', 'bg-[#0E7D34]', 'text-white'));
                // Add it to the clicked tab
                this.classList.add('active', 'bg-[#0E7D34]', 'text-white');

                // Hide all contents
                contents.forEach(content => content.classList.add('hidden'));

                // Get the target ID from href attribute (e.g., "#account")
                const targetId = this.getAttribute('href').substring(1) + '-content';
                const targetContent = document.getElementById(targetId);

                if (targetContent) {
                    targetContent.classList.remove('hidden');
                }
            });
        });
    });
</script>


<script>
    function previewImage(input) {
        const preview = document.getElementById('preview-img');
        const previewContainer = document.getElementById('image-preview');

        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                previewContainer.classList.remove('hidden');
            }

            reader.readAsDataURL(input.files[0]);
        } else {
            previewContainer.classList.add('hidden');
        }
    }
</script>
