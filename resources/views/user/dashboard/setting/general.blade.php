@extends('landlord.layouts.landlord.app')
@section('title', '<PERSON><PERSON> Settings')
@section('content')



    <div class="max-w-full ">

        <div class="w-full p-6 mx-auto">
            <h2 class="pb-4 mb-4 text-xl font-semibold text-gray-900 border-stroke sm:text-2xl dark:text-white"> Payment Gateway Settings</h2>

            <div class="rounded-2xl border border-gray-200 p-5">
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 2xl:gap-7.5">

                    <div class="col-span-1 mt-b">
                        <form id="General" method="POST" action="{{ route('landlord.update.general') }}">
                            @csrf
                            <!-- Application information -->


                    <div class="rounded-sm border border-stroke bg-white p-4 shadow-default  dark:border-gray-700 sm:p-6 dark:bg-gray-800 md:p-6 xl:p-7.5">
                        <h2 class="text-md font-semibold border-b border-gray-200 mb-4 pb-4 text-gray-900 sm:text-xl dark:text-white dark:border-gray-700"> General Settings</h2>



                            <div class="mb-4 w-full ">
                                <x-input-label for="sitename" :value="__('Application Name')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                <div class="relative mb-2">

                                <x-text-input id="sitename"
                                placeholder="Application Name"
                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="text" name="sitename" :value="old('sitename')??get_setting('sitename')" required autofocus autocomplete="Application Name" />
                                </div>
                                <x-input-error :messages="$errors->get('sitename')" class="mt-2" />


                            </div>
                            <div class="mb-4 w-full">
                                <x-input-label for="keywords" :value="__('Application Keywords')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                <div class="relative mb-2">

                                <x-text-input id="keywords"
                                placeholder="Application Keywords"
                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="text" name="keywords" :value="old('keywords')??get_setting('keywords')" required autofocus autocomplete="Application Keywords" />
                                </div>
                                <x-input-error :messages="$errors->get('keywords')" class="mt-2" />


                            </div>

                            <div class="mb-4 w-full">
                                <x-input-label for="description" :value="__('Meta Description')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                <div class="relative mb-2">

                                <x-textarea-input id="description"
                                    placeholder="Meta Description"
                                    class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                     name="description"  required autofocus >{{ @old('description')??get_setting('description') }}</x-textarea-input>
                                </div>
                                <x-input-error :messages="$errors->get('description')" class="mt-2" />


                            </div>
                            <div class="mb-4 w-full">
                                <x-input-label for="phone" :value="__('Contact Phone Number')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                <div class="relative mb-2">

                                <x-text-input id="phone"
                                placeholder="Contact Phone Number"
                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="tel" name="phone" :value="old('phone')??get_setting('phone')" required autofocus autocomplete="Contact Phone Number" />
                                </div>
                                <x-input-error :messages="$errors->get('phone')" class="mt-2" />


                            </div>
                            <div class="mb-4 w-full">
                                <x-input-label for="email" :value="__('Contact Email')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                <div class="relative mb-2">

                                <x-text-input id="email"
                                placeholder="Contact Email"
                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="text" name="email" :value="old('email')??get_setting('email')" required autofocus autocomplete="Contact Email" />
                                </div>
                                <x-input-error :messages="$errors->get('email')" class="mt-2" />


                            </div>

                            <div class="mb-4 w-full">
                                <x-input-label for="email" :value="__('Office Address')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                <div class="relative mb-2">

                                    <x-textarea-input id="office_address"
                                    placeholder="Office Address"
                                    class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                     name="office_address"  required autofocus >{{ @old('office_address')??get_setting('office_address') }}</x-textarea-input>
                                </div>

                                <x-input-error :messages="$errors->get('office_address')" class="mt-2" />


                            </div>


                            <x-primary-button x-on:click="loading=true; document.getElementById('General').submit();"  class="ms-3 w-full px-5 py-3 text-base font-medium text-center text-white dark:text-gray-dark-200 bg-blue-800 hover:bg-blue-700 rounded-lg hover:bg-blue-700 focus:ring-4 focus:bg-blue-800 sm:w-auto dark:bg-blue-dark-200 dark:hover:bg-blue-dark-300 dark:focus:bg-blue-dark-400">
                               {{ __('Save Changes') }}

                            </x-primary-button>

                        </div>

                     </form>


                     <form id="Social" method="POST" action="{{ route('landlord.update.social') }}">
                        @csrf

                        <!-- Social Media -->

                <div class="rounded-sm border border-stroke bg-white p-4  mt-5 shadow-default  dark:border-gray-700 sm:p-6 dark:bg-gray-800 md:p-6 xl:p-7.5">
                    <h2 class="text-md font-semibold border-b border-gray-200 mb-4 pb-4 text-gray-900 sm:text-xl dark:text-white dark:border-gray-700"> Social Media</h2>



                        <div class="mb-4 w-full ">
                            <x-input-label for="facebook" :value="__('Facebook')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                            <div class="relative mb-2">

                            <x-text-input id="facebook"
                            placeholder="Facebook"
                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            type="url" name="facebook" :value="old('facebook')??get_setting('facebook')" required autofocus autocomplete="Facebook" />
                            </div>
                            <x-input-error :messages="$errors->get('facebook')" class="mt-2" />


                        </div>
                        <div class="mb-4 w-full">
                            <x-input-label for="instagram" :value="__('Instagram')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                            <div class="relative mb-2">

                            <x-text-input id="instagram"
                            placeholder="Instagram"
                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            type="url" name="instagram" :value="old('instagram')??get_setting('instagram')" required autofocus autocomplete="Instagram" />
                            </div>
                            <x-input-error :messages="$errors->get('instagram')" class="mt-2" />


                        </div>

                        <div class="mb-4 w-full">
                            <x-input-label for="tiktok" :value="__('Tiktok')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                            <div class="relative mb-2">

                                <x-text-input id="tiktok"
                                placeholder="Tiktok"
                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="url" name="tiktok" :value="old('tiktok')??get_setting('tiktok')" required autofocus autocomplete="tiktok" />
                            </div>
                            <x-input-error :messages="$errors->get('description')" class="mt-2" />


                        </div>
                        <div class="mb-4 w-full">
                            <x-input-label for="twitter" :value="__('Twitter (x)')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                            <div class="relative mb-2">

                            <x-text-input id="twitter"
                            placeholder="Twitter (x)"
                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            type="url" name="twitter" :value="old('twitter')??get_setting('twitter')" required autofocus autocomplete="Twitter (x)" />
                            </div>
                            <x-input-error :messages="$errors->get('twitter')" class="mt-2" />


                        </div>
                        <div class="mb-4 w-full">
                            <x-input-label for="youtube" :value="__('YouTube')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                            <div class="relative mb-2">

                            <x-text-input id="youtube"
                            placeholder="YouTube"
                            class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            type="url" name="youtube" :value="old('youtube')??get_setting('youtube')" required autofocus autocomplete="YouTube" />
                            </div>
                            <x-input-error :messages="$errors->get('youtube')" class="mt-2" />


                        </div>




                        <x-primary-button x-on:click="loading=true; document.getElementById('Social').submit();"  class="ms-3 w-full px-5 py-3 text-base font-medium text-center text-white dark:text-gray-dark-200 bg-blue-800 hover:bg-blue-700 rounded-lg hover:bg-blue-700 focus:ring-4 focus:bg-blue-800 sm:w-auto dark:bg-blue-dark-200 dark:hover:bg-blue-dark-300 dark:focus:bg-blue-dark-400">
                           {{ __('Save Changes') }}

                        </x-primary-button>

                    </div>

                 </form>
                    </div>
                    <div class="col-span-1 mt-b">
                        <!-- File upload -->

                        <div class="rounded-sm border border-stroke bg-white p-4   shadow-default  dark:border-gray-700 sm:p-6 dark:bg-gray-800 md:p-6 xl:p-7.5" >
                            <h2 class="text-md font-semibold border-b border-gray-200 mb-4 pb-4 text-gray-900 sm:text-xl dark:text-white dark:border-gray-700">  Global Logo</h2>

                            <div class="grid grid-cols-12 mb-5 gap-4 md:gap-6 2xl:gap-7.5 dark:text-gray-100" x-data="{
                                loading:false,
                                FileSelected:'',
                                imageUrl: '',
                                message: '',
                                response: {},

                                async upload(event) {
                                    this.fileToDataUrl(event, src => this.imageUrl = src)
                                    event.target.disabled = true;
                                    const formData = new FormData()
                                    this.message='';
                                    var files = event.target.files[0];
                                    formData.append('file', files);
                                    formData.append('_token', '{{ csrf_token() }}');
                                    formData.append('logo', 'mainlogo_white');

                                  this.response = await (await fetch('{{ route('landlord.upload.logo') }}', {
                                    method: 'POST',
                                    body: formData,


                                  })).json();

                                  if(this.response.status == 200){
                                    this.message='<div class=&quot; space-y-2 mt-2 border border-teal-400 text-teal-700 bg-teal-100 p-4 &quot; role=&quot; alert &quot;> <strong class=&quot; font-bold &quot;>Success !</strong>'+this.response.message+'</div>';
                                    this.loading=false;
                                }else{
                                    this.message='<div class=&quot; space-y-2 mt-2 border border-red-400 text-red-700 bg-red-100 p-4 &quot; role=&quot; alert &quot;><strong class=&quot; font-bold &quot;>Error !</strong> '+this.response.message+'</div>';

                                    this.loading=false;
                                  }
                                  event.target.disabled = false;
                                },

                                fileToDataUrl(event, callback) {
                                    if (! event.target.files.length) return

                                    let file = event.target.files[0],
                                        reader = new FileReader()

                                    reader.readAsDataURL(file)
                                    reader.onload = e => callback(e.target.result)
                                  },
                            }">

                            <div class="col-span-12 xl:col-span-9">

                                <x-input-label for="MainLogoWhite" :value="__('Main Logo white')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />

                                <x-text-input id="MainLogoWhite"
                                x-on:change="loading = ! loading"
                                @change="upload"
                                placeholder="Logo white"
                                class="block mt-1 w-full p-1 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="file"
                                name="MainLogoWhite" :value="old('youtube')??get_setting('youtube')"
                                accept="image/*" />

                                     <div  :class="loading ? '' : 'hidden'"  x-transition.opacity role="status">
                                        <svg aria-hidden="true" class="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                            <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                        </svg>
                                        <span class="sr-only">Uploading Please wait...</span>Uploading Please wait...
                                    </div>

                                <div x-html="message" x-transition.opacity>

                                </div>
                                </div>
                                <div class="col-span-12 xl:col-span-3">
                                    <template x-if="imageUrl">
                                        <img :src="imageUrl"

                                             class="object-cover rounded border border-gray-200"
                                             style="width: 100px; height: 100px;"
                                        >
                                      </template>

                                      <!-- Show the gray box when image is not available -->
                                      <template x-if="!imageUrl">
                                        <div
                                             class="border rounded border-gray-200 bg-gray-100"
                                             style="width: 100px; height: 100px;"
                                        ><img src="{{ getLogo('mainlogo_white')->thumb }}"  /></div>
                                      </template>
                                    </div>



                        </div>
                        <!-- end of logo -->
                        <div class="grid grid-cols-12 gap-4 mb-5 md:gap-6 2xl:gap-7.5 dark:text-gray-100" x-data="{
                            loading:false,
                            FileSelected:'',
                            imageUrl: '',
                            message: '',
                            response: {},

                            async upload(event) {
                                this.fileToDataUrl(event, src => this.imageUrl = src)
                                event.target.disabled = true;
                                const formData = new FormData()
                                this.message='';
                                var files = event.target.files[0];
                                formData.append('file', files);
                                formData.append('_token', '{{ csrf_token() }}');
                                formData.append('logo', 'mainlogo_dark');

                              this.response = await (await fetch('{{ route('landlord.upload.logo') }}', {
                                method: 'POST',
                                body: formData,


                              })).json();

                              if(this.response.status == 200){
                                this.message='<div class=&quot; space-y-2 mt-2 border border-teal-400 text-teal-700 bg-teal-100 p-4 &quot; role=&quot; alert &quot;> <strong class=&quot; font-bold &quot;>Success !</strong>'+this.response.message+'</div>';
                                this.loading=false;
                            }else{
                                this.message='<div class=&quot; space-y-2 mt-2 border border-red-400 text-red-700 bg-red-100 p-4 &quot; role=&quot; alert &quot;><strong class=&quot; font-bold &quot;>Error !</strong> '+this.response.message+'</div>';

                                this.loading=false;
                              }
                              event.target.disabled = false;
                            },

                            fileToDataUrl(event, callback) {
                                if (! event.target.files.length) return

                                let file = event.target.files[0],
                                    reader = new FileReader()

                                reader.readAsDataURL(file)
                                reader.onload = e => callback(e.target.result)
                              },
                        }">

                            <div class="col-span-12 xl:col-span-9">

                                <x-input-label for="MainLogoWDark" :value="__('Main Logo Dark')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />

                                <x-text-input id="MainLogoWDark"
                                x-on:change="loading = ! loading"
                                @change="upload"
                                placeholder="Main Logo Dark"
                                class="block mt-1 w-full p-1 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="file"
                                name="MainLogoWDark" :value="old('MainLogoWDark')??get_setting('MainLogoWDark')"
                                accept="image/*" />


                                <div  :class="loading ? '' : 'hidden'"  x-transition.opacity role="status">
                                    <svg aria-hidden="true" class="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                    </svg>
                                    <span class="sr-only">Uploading Please wait...</span>Uploading Please wait...
                                </div>

                            <div x-html="message" x-transition.opacity>

                            </div>
                                </div>
                                <div class="col-span-12 xl:col-span-3">
                                    <template x-if="imageUrl">
                                        <img :src="imageUrl"

                                             class="object-cover rounded border border-gray-200"
                                             style="width: 100px; height: 100px;"
                                        >
                                      </template>

                                      <!-- Show the gray box when image is not available -->
                                      <template x-if="!imageUrl">
                                        <div
                                             class="border rounded border-gray-200 bg-gray-100"
                                             style="width: 100px; height: 100px;"
                                        ><img src="{{ getLogo('mainlogo_dark')->thumb }}"  /></div>
                                      </template>
                                    </div>



                        </div>

                        <!-- end of logo -->
                        <div class="grid grid-cols-12 gap-4 mb-5 md:gap-6 2xl:gap-7.5 dark:text-gray-100" x-data="{
                            loading:false,
                            FileSelected:'',
                            imageUrl: '',
                            message: '',
                            response: {},

                            async upload(event) {
                                this.fileToDataUrl(event, src => this.imageUrl = src)
                                event.target.disabled = true;
                                const formData = new FormData()
                                this.message='';
                                var files = event.target.files[0];
                                formData.append('file', files);
                                formData.append('_token', '{{ csrf_token() }}');
                                formData.append('logo', 'adminlogo_white');

                              this.response = await (await fetch('{{ route('landlord.upload.logo') }}', {
                                method: 'POST',
                                body: formData,


                              })).json();

                              if(this.response.status == 200){
                                this.message='<div class=&quot; space-y-2 mt-2 border border-teal-400 text-teal-700 bg-teal-100 p-4 &quot; role=&quot; alert &quot;> <strong class=&quot; font-bold &quot;>Success !</strong>'+this.response.message+'</div>';
                                this.loading=false;
                            }else{
                                this.message='<div class=&quot; space-y-2 mt-2 border border-red-400 text-red-700 bg-red-100 p-4 &quot; role=&quot; alert &quot;><strong class=&quot; font-bold &quot;>Error !</strong> '+this.response.message+'</div>';

                                this.loading=false;
                              }
                              event.target.disabled = false;
                            },

                            fileToDataUrl(event, callback) {
                                if (! event.target.files.length) return

                                let file = event.target.files[0],
                                    reader = new FileReader()

                                reader.readAsDataURL(file)
                                reader.onload = e => callback(e.target.result)
                              },
                        }">

                            <div class="col-span-12 xl:col-span-9">

                                <x-input-label for="AdminLogoWhite" :value="__('Admin Logo white')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />

                                <x-text-input id="AdminLogoWhite"
                                x-on:change="loading = ! loading"
                                @change="upload"
                                placeholder="Admin Logo  white"
                                class="block mt-1 w-full p-1 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="file"
                                name="AdminLogoWhite" :value="old('AdminLogoWhite')??get_setting('AdminLogoWhite')"
                                accept="image/*" />

                                <div  :class="loading ? '' : 'hidden'"  x-transition.opacity role="status">
                                    <svg aria-hidden="true" class="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                    </svg>
                                    <span class="sr-only">Uploading Please wait...</span>Uploading Please wait...
                                </div>

                            <div x-html="message" x-transition.opacity>

                            </div>
                                </div>
                                <div class="col-span-12 xl:col-span-3">
                                    <template x-if="imageUrl">
                                        <img :src="imageUrl"

                                             class="object-cover rounded border border-gray-200"
                                             style="width: 100px; height: 100px;"
                                        >
                                      </template>

                                      <!-- Show the gray box when image is not available -->
                                      <template x-if="!imageUrl">
                                        <div
                                             class="border rounded border-gray-200 bg-gray-100"
                                             style="width: 100px; height: 100px;"
                                        ><img src="{{ getLogo('adminlogo_white')->thumb }}"  /></div>
                                      </template>
                                    </div>



                        </div>


                        <!-- end of logo -->
                        <div class="grid grid-cols-12 gap-4 mb-5 md:gap-6 2xl:gap-7.5 dark:text-gray-100" x-data="{
                            loading:false,
                            FileSelected:'',
                            imageUrl: '',
                            message: '',
                            response: {},

                            async upload(event) {
                                this.fileToDataUrl(event, src => this.imageUrl = src)
                                event.target.disabled = true;
                                const formData = new FormData()
                                this.message='';
                                var files = event.target.files[0];
                                formData.append('file', files);
                                formData.append('_token', '{{ csrf_token() }}');
                                formData.append('logo', 'adminlogo_dark');

                              this.response = await (await fetch('{{ route('landlord.upload.logo') }}', {
                                method: 'POST',
                                body: formData,


                              })).json();

                              if(this.response.status == 200){
                                this.message='<div class=&quot; space-y-2 mt-2 border border-teal-400 text-teal-700 bg-teal-100 p-4 &quot; role=&quot; alert &quot;> <strong class=&quot; font-bold &quot;>Success !</strong>'+this.response.message+'</div>';
                                this.loading=false;
                            }else{
                                this.message='<div class=&quot; space-y-2 mt-2 border border-red-400 text-red-700 bg-red-100 p-4 &quot; role=&quot; alert &quot;><strong class=&quot; font-bold &quot;>Error !</strong> '+this.response.message+'</div>';

                                this.loading=false;
                              }
                              event.target.disabled = false;
                            },

                            fileToDataUrl(event, callback) {
                                if (! event.target.files.length) return

                                let file = event.target.files[0],
                                    reader = new FileReader()

                                reader.readAsDataURL(file)
                                reader.onload = e => callback(e.target.result)
                              },
                        }">

                            <div class="col-span-12 xl:col-span-9">

                                <x-input-label for="AdmiLogoDark" :value="__('Admin Logo Dark')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />

                                <x-text-input id="AdmiLogoDark"
                                x-on:change="loading = ! loading"
                                @change="upload"
                                placeholder="Admin Logo Dark"
                                class="block mt-1 w-full p-1 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="file"
                                name="AdmiLogoDark" :value="old('AdmiLogoDark')??get_setting('AdmiLogoDark')"
                                accept="image/*" />


                                        <div  :class="loading ? '' : 'hidden'"  x-transition.opacity role="status">
                                            <svg aria-hidden="true" class="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                                <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                            </svg>
                                            <span class="sr-only">Uploading Please wait...</span>Uploading Please wait...
                                        </div>

                                    <div x-html="message" x-transition.opacity>

                                    </div>
                                </div>
                                <div class="col-span-12 xl:col-span-3">
                                    <template x-if="imageUrl">
                                        <img :src="imageUrl"

                                             class="object-cover rounded border border-gray-200"
                                             style="width: 100px; height: 100px;"
                                        >
                                      </template>

                                      <!-- Show the gray box when image is not available -->
                                      <template x-if="!imageUrl">
                                        <div
                                             class="border rounded border-gray-200 bg-gray-100"
                                             style="width: 100px; height: 100px;"
                                        ><img src="{{ getLogo('adminlogo_dark')->thumb }}"  /></div>
                                      </template>
                                    </div>



                        </div>

                        <div class="grid grid-cols-12 gap-4 mb-5 md:gap-6 2xl:gap-7.5 dark:text-gray-100" x-data="{
                            loading:false,
                            FileSelected:'',
                            imageUrl: '',
                            message: '',
                            response: {},

                            async upload(event) {
                                this.fileToDataUrl(event, src => this.imageUrl = src)
                                event.target.disabled = true;
                                const formData = new FormData()
                                this.message='';
                                var files = event.target.files[0];
                                formData.append('file', files);
                                formData.append('_token', '{{ csrf_token() }}');
                                formData.append('logo', 'favicon');

                              this.response = await (await fetch('{{ route('landlord.upload.logo') }}', {
                                method: 'POST',
                                body: formData,


                              })).json();

                              if(this.response.status == 200){
                                this.message='<div class=&quot; space-y-2 mt-2 border border-teal-400 text-teal-700 bg-teal-100 p-4 &quot; role=&quot; alert &quot;> <strong class=&quot; font-bold &quot;>Success !</strong>'+this.response.message+'</div>';
                                this.loading=false;
                            }else{
                                this.message='<div class=&quot; space-y-2 mt-2 border border-red-400 text-red-700 bg-red-100 p-4 &quot; role=&quot; alert &quot;><strong class=&quot; font-bold &quot;>Error !</strong> '+this.response.message+'</div>';

                                this.loading=false;
                              }
                              event.target.disabled = false;
                            },

                            fileToDataUrl(event, callback) {
                                if (! event.target.files.length) return

                                let file = event.target.files[0],
                                    reader = new FileReader()

                                reader.readAsDataURL(file)
                                reader.onload = e => callback(e.target.result)
                              },
                        }">

                            <div class="col-span-12 xl:col-span-9  ">

                                <x-input-label for="favicon" :value="__('Favicon')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />

                                <x-text-input id="favicon"
                                x-on:change="loading = ! loading"
                                @change="upload"
                                placeholder="Favicon"
                                class="block mt-1 w-full p-1 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="file"
                                name="favicon" :value="old('Favicon')??get_setting('favicon')"
                                accept="image/*" />


                                        <div  :class="loading ? '' : 'hidden'"  x-transition.opacity role="status">
                                            <svg aria-hidden="true" class="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                                <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                            </svg>
                                            <span class="sr-only">Uploading Please wait...</span>Uploading Please wait...
                                        </div>

                                    <div x-html="message" x-transition.opacity>

                                    </div>
                                </div>
                                <div class="col-span-12 xl:col-span-3">
                                    <template x-if="imageUrl">
                                        <img :src="imageUrl"

                                             class="object-cover rounded border border-gray-200"
                                             style="width: 100px; height: 100px;"
                                        >
                                      </template>

                                      <!-- Show the gray box when image is not available -->
                                      <template x-if="!imageUrl">
                                        <div
                                             class="border rounded border-gray-200 bg-gray-100"
                                             style="width: 100px; height: 100px;"
                                        ><img src="{{ getLogo('favicon')->thumb }}"  /></div>
                                      </template>
                                    </div>



                        </div>

                        </div>

                        <div class="rounded-sm border border-stroke bg-white p-4  mt-5 shadow-default  dark:border-gray-700 sm:p-6 dark:bg-gray-800 md:p-6 xl:p-7.5" >
                            <h2 class="text-md font-semibold border-b border-gray-200 mb-4 pb-4 text-gray-900 sm:text-xl dark:text-white dark:border-gray-700"> Analytics</h2>


                            <form id="Analytics" method="POST" action="{{ route('landlord.update.analytics') }}">
                                @csrf

                            <div class="mb-4 w-full ">
                                <x-checkbox-input id="google_analytics_enable" value="{{ get_setting('google_analytics_enable') }}" name="google_analytics_enable" title="{{ __('Google Analytics Code') }}" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />

                                <x-input-error :messages="$errors->get('google_analytics_enable')" class="mt-2" />


                            </div>

                            <div class="mb-4 w-full mb-3">
                                <x-input-label for="google_analytics_id" :value="__('Tracking ID')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                <div class="relative mb-2">

                                <x-text-input id="google_analytics_id"
                                placeholder="Tracking ID"
                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="text" name="google_analytics_id" :value="old('google_analytics_id')??get_setting('google_analytics_id')"  />
                                </div>
                                <x-input-error :messages="$errors->get('google_analytics_id')" class="mt-2" />


                            </div>



                            <div class="mb-4 w-full ">
                                <x-checkbox-input id="facebook_pixel_enable" value="{{ get_setting('facebook_pixel_enable') }}" name="facebook_pixel_enable" title="{{ __('Facebook Pixel Enable') }}" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />

                                <x-input-error :messages="$errors->get('facebook_pixel_enable')" class="mt-2" />


                            </div>

                            <div class="mb-4 w-full mb-3">
                                <x-input-label for="facebook_pixel_id" :value="__('Facebook Pixel ID')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                <div class="relative mb-2">

                                <x-text-input id="facebook_pixel_id"
                                placeholder="Facebook Pixel ID"
                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="text" name="facebook_pixel_id" :value="old('facebook_pixel_id')??get_setting('facebook_pixel_id')"  />
                                </div>
                                <x-input-error :messages="$errors->get('facebook_pixel_id')" class="mt-2" />


                            </div>
                            <div class="mb-4 w-full ">
                                <x-checkbox-input id="google_map_api_enable" value="{{ get_setting('google_map_api_enable') }}" name="google_map_api_enable" title="{{ __('Google Map Enable') }}" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />

                                <x-input-error :messages="$errors->get('google_map_api_enable')" class="mt-2" />


                            </div>

                            <div class="mb-4 w-full mb-3">
                                <x-input-label for="google_map_api_key" :value="__('Google Map API Key')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                <div class="relative mb-2">

                                <x-text-input id="google_map_api_key"
                                placeholder="Google Map API Key"
                                class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                type="text" name="google_map_api_key" :value="old('google_map_api_key')??get_setting('google_map_api_key')"  />
                                </div>
                                <x-input-error :messages="$errors->get('google_map_api_key')" class="mt-2" />


                            </div>

                            <x-primary-button x-on:click="loading=true; document.getElementById('Analytics').submit();"  class="ms-3 w-full px-5 py-3 text-base font-medium text-center text-white dark:text-gray-dark-200 bg-blue-800 hover:bg-blue-700 rounded-lg hover:bg-blue-700 focus:ring-4 focus:bg-blue-800 sm:w-auto dark:bg-blue-dark-200 dark:hover:bg-blue-dark-300 dark:focus:bg-blue-dark-400">
                                {{ __('Save Changes') }}

                             </x-primary-button>

                            </form>

                            </div>

                            <div class="rounded-sm border border-stroke bg-white p-4  mt-5 shadow-default  dark:border-gray-700 sm:p-6 dark:bg-gray-800 md:p-6 xl:p-7.5" >
                            <h2 class="text-md font-semibold border-b border-gray-200 mb-4 pb-4 text-gray-900 sm:text-xl dark:text-white dark:border-gray-700"> Scripts</h2>


                            <form method="POST" id="Scripts" action="{{ route('landlord.update.scripts') }}">
                                @csrf

                                <div class="mb-4 w-full">
                                    <x-input-label for="header_scripts" :value="__('Header Scripts')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                    <div class="relative mb-2">

                                    <x-textarea-input id="header_scripts"
                                        placeholder="Header Scripts"
                                        class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                         name="header_scripts"  required autofocus >{{ @old('header_scripts')??get_setting('header_scripts') }}</x-textarea-input>
                                    </div>
                                    <x-input-error :messages="$errors->get('header_scripts')" class="mt-2" />


                                </div>

                                <div class="mb-4 w-full">
                                    <x-input-label for="footer_scripts" :value="__('Footer Scripts')" class="block mb-1 text-md font-medium text-gray-900 dark:text-white " />
                                    <div class="relative mb-2">

                                    <x-textarea-input id="footer_scripts"
                                        placeholder="Footer Scripts"
                                        class="block mt-1 w-full p-4 font-bold bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                         name="footer_scripts"  required autofocus >{{ @old('footer_scripts')??get_setting('footer_scripts') }}</x-textarea-input>
                                    </div>
                                    <x-input-error :messages="$errors->get('footer_scripts')" class="mt-2" />


                                </div>

                            <x-primary-button x-on:click="loading=true; document.getElementById('Scripts').submit();"  class="ms-3 w-full px-5 py-3 text-base font-medium text-center text-white dark:text-gray-dark-200 bg-blue-800 hover:bg-blue-700 rounded-lg hover:bg-blue-700 focus:ring-4 focus:bg-blue-800 sm:w-auto dark:bg-blue-dark-200 dark:hover:bg-blue-dark-300 dark:focus:bg-blue-dark-400">
                                {{ __('Save Changes') }}

                             </x-primary-button>

                            </form>

                            </div>

                        </div>

                        </div>

                    </div>
             </div>
            </div>
        </div>
    </div>

@endsection





