<!-- Footer container -->
<footer
  class="mt-8 text-center lg:text-left">

  <div class="w-full p-6 text-blue-100 bg-blue-800 connect-section border-b-1 dark:bg-blue-800 dark:text-blue-100">
    <div class="container mx-auto ">
        <div
        class="flex items-center justify-center lg:justify-between">
        <div class="hidden me-12 lg:block">
          <span>Get connected with us on social networks:</span>
        </div>
        <!-- Social network icons container -->
        <div class="flex justify-center">
          <a href="{{ get_setting('facebook')??'#' }}" target="_blank" class="me-6 [&>svg]:h-5 [&>svg]:w-">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 320 512">

              <path
                d="M80 299.3V512H196V299.3h86.5l18-97.8H196V166.9c0-51.7 20.3-71.5 72.7-71.5c16.3 0 29.4 .4 37 1.2V7.9C291.4 4 256.4 0 236.2 0C129.3 0 80 50.5 80 159.4v42.1H14v97.8H80z" />
            </svg>
          </a>
          <a href="{{ get_setting('twitter')??'#' }}" target="_blank" class="me-6 [&>svg]:h-5 [&>svg]:w-5 ">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 512 512">

              <path
                d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z" />
            </svg>
          </a>

          <a href="{{ get_setting('instagram')??'#' }}" target="_blank" class="me-6 [&>svg]:h-5 [&>svg]:w-5">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 448 512">

              <path
                d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z" />
            </svg>
          </a>
          <a href="{{ get_setting('youtube')??'#' }}" target="_blank" class="me-6 [&>svg]:h-5 [&>svg]:w-5">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-12 h-12"  fill="currentColor" viewBox="0 0 576 512">
            <path d="M549.7 124.1c-6.3-23.7-24.8-42.3-48.3-48.6C458.8 64 288 64 288 64S117.2 64 74.6 75.5c-23.5 6.3-42 24.9-48.3 48.6-11.4 42.9-11.4 132.3-11.4 132.3s0 89.4 11.4 132.3c6.3 23.7 24.8 41.5 48.3 47.8C117.2 448 288 448 288 448s170.8 0 213.4-11.5c23.5-6.3 42-24.2 48.3-47.8 11.4-42.9 11.4-132.3 11.4-132.3s0-89.4-11.4-132.3zm-317.5 213.5V175.2l142.7 81.2-142.7 81.2z"/></svg>
          </a>

        </div>
      </div>
      </div>
  </div>


  <div class="w-full foot-widgets " style="background-image:url('{{ asset('images/bg/bg18.jpg') }}'); background-size:cover; background-repeat:no-repeat;">
    <div class="w-full bg-gray-900 py-[50px]">

        <div class="container mx-auto text-blue-100 ">
            <div class="py-10 mx-6 text-left md:text-left">
              <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-4">

                <div class="">
                    <a class="block pb-5" href="{{ url('/') }}">
                        <x-application-logo logo="{{ asset('images/logo/logo-light.svg') }}" class="block w-auto text-gray-800 fill-current h-9 dark:text-gray-200" />
                    </a>
                  <p class="py-3 text-rent-blue-light-100">
                    {{ get_setting('description')??'' }}
                  </p>



                </div>
                <!-- Products section -->
                <div>
                  <h6
                    class="flex justify-center mb-4 font-semibold uppercase md:justify-start">
                    Legal
                  </h6>
                  <p class="mb-4">
                    <a href="{{ get_setting('page.show','terms-and-conditions')??'#' }}">Terms and Conditions</a>
                  </p>
                  <p class="mb-4">
                    <a href="{{ get_setting('page.show','privacy-policy')??'#' }}">Privacy Policy</a>
                  </p>
                  <p class="mb-4">
                    <a href="{{ get_setting('page.show','privacy-policy')??'#' }}">Cookie Policy</a>
                  </p>

                </div>
                <!-- Useful links section -->
                <div>
                  <h6
                    class="flex justify-center mb-4 font-semibold uppercase md:justify-start">
                    Useful links
                  </h6>
                  <p class="mb-4">
                    <a href="{{ get_setting('about')??'#' }}">About Us</a>
                  </p>

                  <p class="mb-4">
                    <a href="{{ route('login') }}">Login</a>
                  </p>
                  <p>
<<<<<<< HEAD
                    <a href="">Register</a>
=======
                    <a href="{{ route('register') }}">Register</a>
>>>>>>> main
                  </p>
                </div>
                <!-- Contact section -->
                <div>
                  <h6
                    class="flex justify-center mb-4 font-semibold uppercase md:justify-start">
                    Contact
                  </h6>
                  <p class="flex items-center justify-center mb-4 md:justify-start">
                    <span class="me-3 [&>svg]:h-5 [&>svg]:w-5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="currentColor">
                        <path
                          d="M11.47 3.84a.75.75 0 011.06 0l8.69 8.69a.75.75 0 101.06-1.06l-8.689-8.69a2.25 2.25 0 00-3.182 0l-8.69 8.69a.75.75 0 001.061 1.06l8.69-8.69z" />
                        <path
                          d="M12 5.432l8.159 8.159c.************.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 01-.75-.75v-4.5a.75.75 0 00-.75-.75h-3a.75.75 0 00-.75.75V21a.75.75 0 01-.75.75H5.625a1.875 1.875 0 01-1.875-1.875v-6.198a2.29 2.29 0 00.091-.086L12 5.43z" />
                      </svg>
                    </span>
                    {{ get_setting('office_address')??'No 2 Street, 1st Avenue, Lekki Phase 1, Lagos' }}
                  </p>
                  <p class="flex items-center justify-center mb-4 md:justify-start">
                    <span class="me-3 [&>svg]:h-5 [&>svg]:w-5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="currentColor">
                        <path
                          d="M1.5 8.67v8.58a3 3 0 003 3h15a3 3 0 003-3V8.67l-8.928 5.493a3 3 0 01-3.144 0L1.5 8.67z" />
                        <path
                          d="M22.5 6.908V6.75a3 3 0 00-3-3h-15a3 3 0 00-3 3v.158l9.714 5.978a1.5 1.5 0 001.572 0L22.5 6.908z" />
                      </svg>
                    </span>
                    {{ get_setting('email')??'<EMAIL>' }}
                  </p>
                  <p class="flex items-center justify-center mb-4 md:justify-start">
                    <span class="me-3 [&>svg]:h-5 [&>svg]:w-5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="currentColor">
                        <path
                          fill-rule="evenodd"
                          d="M1.5 4.5a3 3 0 013-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 01-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 006.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 011.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 01-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5z"
                          clip-rule="evenodd" />
                      </svg>
                    </span>
                    {{ get_setting('phone')??' +234 ************' }}
                  </p>

                </div>
              </div>
            </div>
          </div>
    </div>

  </div>
  <!--Copyright section-->
  <div class="p-6 text-white bg-gray-800 dark:bg-gray-900 text-start">

    <div class="container mx-auto">
    <span>© {{ date('Y') }} Copyright:</span>
    <a class="font-semibold" href="{{url('/')}}">{{ get_setting('sitename')??'' }}</a
>
    </div>
  </div>

</footer>
