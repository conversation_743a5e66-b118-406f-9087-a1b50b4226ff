<aside id="sidebar"
    class="fixed top-0 left-0 z-[60] flex flex-col flex-shrink-0 hidden w-64 h-full pt-16 font-normal duration-75 lg:flex transition-width"
    aria-label="Sidebar">
    <div
        class="relative flex flex-col flex-1 min-h-0 pt-0 bg-white border-r border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <div class="flex flex-col flex-1 pt-5 pb-4 overflow-y-auto">

            <div
                class="flex-1 px-3 mt-5 space-y-1 bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                <ul class="pb-2 space-y-2">

                   

        <li
            class="@if (Request::segment(2) == 'dashboard') bg-[#117D34] text-white rounded-lg @else hover:bg-gray-100 @endif">
            <a href="{{ route('admin.dashboard') }}"
                class="flex items-center p-2 text-base rounded-lg group
                    @if (Request::segment(2) == 'dashboard')
                        text-white
                    @else
                        text-gray-900 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700
                    @endif">
                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
                    <path d="M16.3333 10.5C16.0028 10.5 15.7259 10.388 15.5027 10.164C15.2794 9.94 15.1674 9.66311 15.1667 9.33333V4.66667C15.1667 4.33611 15.2787 4.05922 15.5027 3.836C15.7267 3.61278 16.0036 3.50078 16.3333 3.5H23.3333C23.6639 3.5 23.9412 3.612 24.1652 3.836C24.3892 4.06 24.5008 4.33689 24.5 4.66667V9.33333C24.5 9.66389 24.388 9.94117 24.164 10.1652C23.94 10.3892 23.6631 10.5008 23.3333 10.5H16.3333ZM4.66667 15.1667C4.33611 15.1667 4.05922 15.0547 3.836 14.8307C3.61278 14.6067 3.50078 14.3298 3.5 14V4.66667C3.5 4.33611 3.612 4.05922 3.836 3.836C4.06 3.61278 4.33689 3.50078 4.66667 3.5H11.6667C11.9972 3.5 12.2745 3.612 12.4985 3.836C12.7225 4.06 12.8341 4.33689 12.8333 4.66667V14C12.8333 14.3306 12.7213 14.6078 12.4973 14.8318C12.2733 15.0558 11.9964 15.1674 11.6667 15.1667H4.66667ZM16.3333 24.5C16.0028 24.5 15.7259 24.388 15.5027 24.164C15.2794 23.94 15.1674 23.6631 15.1667 23.3333V14C15.1667 13.6694 15.2787 13.3926 15.5027 13.1693C15.7267 12.9461 16.0036 12.8341 16.3333 12.8333H23.3333C23.6639 12.8333 23.9412 12.9453 24.1652 13.1693C24.3892 13.3933 24.5008 13.6702 24.5 14V23.3333C24.5 23.6639 24.388 23.9412 24.164 24.1652C23.94 24.3892 23.6631 24.5008 23.3333 24.5H16.3333ZM4.66667 24.5C4.33611 24.5 4.05922 24.388 3.836 24.164C3.61278 23.94 3.50078 23.6631 3.5 23.3333V18.6667C3.5 18.3361 3.612 18.0592 3.836 17.836C4.06 17.6128 4.33689 17.5008 4.66667 17.5H11.6667C11.9972 17.5 12.2745 17.612 12.4985 17.836C12.7225 18.06 12.8341 18.3369 12.8333 18.6667V23.3333C12.8333 23.6639 12.7213 23.9412 12.4973 24.1652C12.2733 24.3892 11.9964 24.5008 11.6667 24.5H4.66667Z"
                        fill="@if (Request::segment(2) == 'dashboard') #fff @else #0E7D34 @endif"/>
                </svg>
                <span class="ml-3 @if (Request::segment(2) == 'dashboard') text-white @else text-gray-900 dark:text-gray-200 @endif" sidebar-toggle-item>Dashboard</span>
            </a>
        </li>
             



                    {{-- Groups --}}


            <li class="@if (Request::segment(2) == 'groups') bg-[#117D34] text-white rounded-lg @else hover:bg-gray-100 @endif">
                <a href="{{ route('admin.groups.index') }}" 
                    class="flex items-center p-2 text-base rounded-lg group 
                        @if (Request::segment(2) == 'groups') 
                            text-white 
                        @else 
                            text-gray-900 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 
                        @endif">
                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
                        <path d="M1.16797 20.066C1.16797 19.4049 1.3383 18.7975 1.67897 18.2437C2.01964 17.6899 2.47152 17.2668 3.03464 16.9744C4.24019 16.3716 5.46519 15.9197 6.70964 15.6187C7.95408 15.3177 9.21797 15.1668 10.5013 15.166C11.7846 15.1653 13.0485 15.3162 14.293 15.6187C15.5374 15.9213 16.7624 16.3732 17.968 16.9744C18.5319 17.266 18.9841 17.6892 19.3248 18.2437C19.6655 18.7983 19.8354 19.4057 19.8346 20.066V20.9994C19.8346 21.641 19.6064 22.1905 19.1498 22.6479C18.6932 23.1052 18.1437 23.3335 17.5013 23.3327H3.5013C2.85964 23.3327 2.31052 23.1044 1.85397 22.6479C1.39741 22.1913 1.16875 21.6418 1.16797 20.9994V20.066ZM21.5263 23.3327C21.7402 22.9827 21.9008 22.6086 22.0081 22.2104C22.1155 21.8122 22.1687 21.4085 22.168 20.9994V19.8327C22.168 18.9772 21.93 18.1554 21.454 17.3675C20.978 16.5797 20.3021 15.9042 19.4263 15.341C20.418 15.4577 21.3513 15.6572 22.2263 15.9395C23.1013 16.2219 23.918 16.5668 24.6763 16.9744C25.3763 17.3633 25.911 17.7957 26.2805 18.2717C26.6499 18.7477 26.8346 19.268 26.8346 19.8327V20.9994C26.8346 21.641 26.6064 22.1905 26.1498 22.6479C25.6932 23.1052 25.1437 23.3335 24.5013 23.3327H21.5263ZM10.5013 13.9994C9.21797 13.9994 8.11936 13.5424 7.20547 12.6285C6.29158 11.7147 5.83464 10.616 5.83464 9.33271C5.83464 8.04938 6.29158 6.95077 7.20547 6.03688C8.11936 5.12299 9.21797 4.66605 10.5013 4.66605C11.7846 4.66605 12.8832 5.12299 13.7971 6.03688C14.711 6.95077 15.168 8.04938 15.168 9.33271C15.168 10.616 14.711 11.7147 13.7971 12.6285C12.8832 13.5424 11.7846 13.9994 10.5013 13.9994ZM22.168 9.33271C22.168 10.616 21.711 11.7147 20.7971 12.6285C19.8832 13.5424 18.7846 13.9994 17.5013 13.9994C17.2874 13.9994 17.0152 13.9753 16.6846 13.927C16.3541 13.8788 16.0819 13.8252 15.868 13.766C16.393 13.1438 16.7966 12.4535 17.079 11.6952C17.3613 10.9369 17.5021 10.1494 17.5013 9.33271C17.5005 8.51605 17.3597 7.72855 17.079 6.97021C16.7982 6.21188 16.3945 5.5216 15.868 4.89938C16.1402 4.80216 16.4124 4.73877 16.6846 4.70921C16.9569 4.67966 17.2291 4.66527 17.5013 4.66605C18.7846 4.66605 19.8832 5.12299 20.7971 6.03688C21.711 6.95077 22.168 8.04938 22.168 9.33271Z"
                            fill="@if (Request::segment(2) == 'groups') #fff @else #0E7D34 @endif"/>
                    </svg>
                    <span class="ml-3 @if (Request::segment(2) == 'groups') text-white @else text-gray-900 dark:text-gray-200 @endif" sidebar-toggle-item>Groups</span>
                </a>
            </li>

               {{-- Groups End --}}

                 {{-- User.Mgt --}}


             <li class="@if (Request::segment(2) == 'user-mgt') bg-[#117D34] text-white rounded-lg @else hover:bg-gray-100 @endif">
                <a href="{{route('admin.users.index')}}" 
                class="flex items-center p-2 text-base rounded-lg group 
                        @if (Request::segment(2) == 'user-mgt') 
                            text-white 
                        @else 
                            text-gray-900 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 
                        @endif">
                    
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12 10C14.2091 10 16 8.20914 16 6C16 3.79086 14.2091 2 12 2C9.79086 2 8 3.79086 8 6C8 8.20914 9.79086 10 12 10Z"
                            fill="@if (Request::segment(2) == 'user-mgt') #ffffff @else #117D34 @endif"/>
                        <path d="M20 17.5C20 19.985 20 22 12 22C4 22 4 19.985 4 17.5C4 15.015 7.582 13 12 13C16.418 13 20 15.015 20 17.5Z"
                            fill="@if (Request::segment(2) == 'user-mgt') #ffffff @else #117D34 @endif"/>
                    </svg>
                    
                    <span class="ml-3 @if (Request::segment(2) == 'user-mgt') text-white @else text-gray-900 dark:text-gray-200 @endif" 
                        sidebar-toggle-item>User Mgt.</span>
                </a>
            </li>


    


            

               {{-- User Mgt. End --}}

                {{-- Transactions --}}


            <li class="@if (Request::segment(2) == 'transactions') bg-[#117D34] text-white rounded-lg @else hover:bg-gray-100 @endif">
                <a href="{{route('admin.transactions.index')}}" 
                class="flex items-center p-2 text-base rounded-lg group 
                        @if (Request::segment(2) == 'transactions') 
                            text-white 
                        @else 
                            text-gray-900 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 
                        @endif">
                    
                   <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
                    <path d="M11.6667 6.41703C11.6665 5.52342 11.8944 4.64456 12.3289 3.86367C12.7633 3.08277 13.3899 2.42569 14.1492 1.95464C14.9086 1.4836 15.7757 1.21418 16.6683 1.1719C17.5609 1.12962 18.4495 1.31587 19.25 1.71303C20.0504 1.31635 20.9388 1.13047 21.8311 1.17298C22.7235 1.2155 23.5902 1.485 24.3492 1.95599C25.1083 2.42698 25.7347 3.08388 26.169 3.86452C26.6033 4.64515 26.8313 5.5237 26.8313 6.41703C26.8313 7.31036 26.6033 8.18891 26.169 8.96955C25.7347 9.75019 25.1083 10.4071 24.3492 10.8781C23.5902 11.3491 22.7235 11.6186 21.8311 11.6611C20.9388 11.7036 20.0504 11.5177 19.25 11.121C18.4495 11.5182 17.5609 11.7044 16.6683 11.6622C15.7757 11.6199 14.9086 11.3505 14.1492 10.8794C13.3899 10.4084 12.7633 9.7513 12.3289 8.9704C11.8944 8.1895 11.6665 7.31064 11.6667 6.41703ZM21.2917 9.3197C21.3873 9.32903 21.4846 9.3337 21.5833 9.3337C21.9781 9.33285 22.3686 9.25186 22.7312 9.09564C23.0937 8.93942 23.4208 8.71122 23.6926 8.42487C23.9644 8.13852 24.1752 7.79997 24.3122 7.42975C24.4493 7.05953 24.5098 6.66533 24.49 6.27104C24.4703 5.87675 24.3707 5.49057 24.1973 5.13591C24.0239 4.78125 23.7803 4.46548 23.4812 4.20774C23.1822 3.95 22.8339 3.75565 22.4576 3.63646C22.0812 3.51728 21.6846 3.47574 21.2917 3.51437C21.8624 4.37482 22.1668 5.38448 22.1667 6.41703C22.1668 7.44958 21.8624 8.45924 21.2917 9.3197ZM9.84083 15.4587C9.64928 15.4582 9.4595 15.4955 9.28234 15.5683C9.10518 15.6412 8.94411 15.7482 8.80833 15.8834L6.41667 18.275V22.7504H12.9815L19.7517 21.0587L23.8723 19.2994C24.0127 19.2235 24.1201 19.0985 24.174 18.9483C24.2278 18.7982 24.2243 18.6334 24.1642 18.4856C24.104 18.3379 23.9915 18.2175 23.848 18.1476C23.7046 18.0778 23.5404 18.0633 23.387 18.107L23.3637 18.1129L15.883 19.8337H11.6667V17.5004H15.3125C15.5832 17.5004 15.8429 17.3928 16.0343 17.2014C16.2258 17.0099 16.3333 16.7503 16.3333 16.4795C16.3333 16.2088 16.2258 15.9491 16.0343 15.7577C15.8429 15.5663 15.5832 15.4587 15.3125 15.4587H9.84083ZM18.6515 16.8027L22.7955 15.8495C23.2369 15.7335 23.6989 15.7201 24.1463 15.8105C24.5936 15.9008 25.0143 16.0925 25.376 16.3707C25.7377 16.649 26.0309 17.0064 26.233 17.4156C26.435 17.8248 26.5406 18.2748 26.5417 18.7312C26.5412 19.2852 26.3867 19.8281 26.0953 20.2993C25.804 20.7705 25.3874 21.1514 24.892 21.3994L24.8605 21.4157L20.4972 23.2765L13.2685 25.0837H0V16.6254H4.767L7.161 14.2314C7.51373 13.8798 7.93224 13.6012 8.39266 13.4115C8.85307 13.2217 9.34635 13.1245 9.84433 13.1254H15.3125C15.7803 13.1253 16.2429 13.2231 16.6706 13.4124C17.0983 13.6018 17.4817 13.8785 17.7962 14.2248C18.1106 14.5711 18.3492 14.9793 18.4965 15.4233C18.6438 15.8672 18.6966 16.3371 18.6515 16.8027ZM4.08333 18.9587H2.33333V22.7504H4.08333V18.9587Z" fill="#0E7D34"/>
                    </svg>

                    
                    <span class="ml-3 @if (Request::segment(2) == 'transactions') text-white @else text-gray-900 dark:text-gray-200 @endif" 
                        sidebar-toggle-item>Transactions</span>
                </a>
            </li>

               {{-- Transactions End --}}

                 {{-- Revenue --}}


                <li class="@if (Request::segment(2) == 'revenue') bg-[#117D34] text-white rounded-lg @else hover:bg-gray-100 @endif">
                <a href="" 
                class="flex items-center p-2 text-base rounded-lg group 
                        @if (Request::segment(2) == 'revenue') 
                            text-white 
                        @else 
                            text-gray-900 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 
                        @endif">
                    
                   <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M14.0245 3.5C11.3896 3.5 8.98914 4.37033 7.56872 5.08142C7.44039 5.14558 7.32061 5.20819 7.20939 5.26925C6.98889 5.38942 6.80105 5.50142 6.65172 5.6L8.26755 7.97883L9.02822 8.28158C12.0009 9.78133 15.9874 9.78133 18.9606 8.28158L19.824 7.83358L21.3517 5.6C21.0349 5.39415 20.7059 5.20772 20.3665 5.04175C18.9531 4.33825 16.6098 3.5 14.0251 3.5M10.2672 6.19267C9.69517 6.08501 9.13007 5.9432 8.57497 5.768C9.90555 5.17708 11.8883 4.55 14.0251 4.55C15.505 4.55 16.9044 4.851 18.0617 5.2325C16.7055 5.42325 15.2582 5.747 13.8792 6.14542C12.7942 6.45925 11.5261 6.42542 10.2672 6.19267ZM19.5772 9.14667L19.4337 9.219C16.1636 10.8687 11.8259 10.8687 8.55572 9.219L8.4198 9.15017C3.50639 14.5407 -0.244448 24.4983 14.0245 24.4983C28.2934 24.4983 24.4516 14.3558 19.5772 9.14667ZM13.4184 14C13.109 14 12.8122 14.1229 12.5934 14.3417C12.3746 14.5605 12.2517 14.8572 12.2517 15.1667C12.2517 15.4761 12.3746 15.7728 12.5934 15.9916C12.8122 16.2104 13.109 16.3333 13.4184 16.3333V14ZM14.5851 12.8333V12.25H13.4184V12.8333C12.7995 12.8333 12.2061 13.0792 11.7685 13.5168C11.3309 13.9543 11.0851 14.5478 11.0851 15.1667C11.0851 15.7855 11.3309 16.379 11.7685 16.8166C12.2061 17.2542 12.7995 17.5 13.4184 17.5V19.8333C12.9109 19.8333 12.4786 19.5096 12.3176 19.0557C12.2937 18.9815 12.2552 18.9128 12.2043 18.8537C12.1534 18.7946 12.0912 18.7463 12.0213 18.7117C11.9514 18.6771 11.8753 18.6568 11.7974 18.6521C11.7196 18.6474 11.6415 18.6584 11.568 18.6844C11.4945 18.7104 11.4269 18.7508 11.3692 18.8034C11.3116 18.856 11.2651 18.9195 11.2325 18.9904C11.1999 19.0612 11.1818 19.1379 11.1793 19.2159C11.1768 19.2938 11.19 19.3715 11.2181 19.4442C11.3789 19.8993 11.6769 20.2932 12.0709 20.5719C12.465 20.8505 12.9358 21.0001 13.4184 21V21.5833H14.5851V21C15.2039 21 15.7974 20.7542 16.235 20.3166C16.6726 19.879 16.9184 19.2855 16.9184 18.6667C16.9184 18.0478 16.6726 17.4543 16.235 17.0168C15.7974 16.5792 15.2039 16.3333 14.5851 16.3333V14C15.0926 14 15.5248 14.3237 15.6858 14.7776C15.7097 14.8518 15.7482 14.9205 15.7991 14.9796C15.85 15.0387 15.9123 15.087 15.9822 15.1216C16.052 15.1563 16.1282 15.1765 16.206 15.1812C16.2839 15.1859 16.3619 15.1749 16.4354 15.1489C16.509 15.123 16.5766 15.0825 16.6342 15.0299C16.6918 14.9774 16.7383 14.9138 16.771 14.843C16.8036 14.7721 16.8217 14.6954 16.8241 14.6175C16.8266 14.5395 16.8134 14.4619 16.7854 14.3891C16.6246 13.9341 16.3266 13.5401 15.9325 13.2615C15.5384 12.9828 15.0677 12.8333 14.5851 12.8333ZM14.5851 17.5V19.8333C14.8945 19.8333 15.1912 19.7104 15.41 19.4916C15.6288 19.2728 15.7517 18.9761 15.7517 18.6667C15.7517 18.3572 15.6288 18.0605 15.41 17.8417C15.1912 17.6229 14.8945 17.5 14.5851 17.5Z" fill="#0E7D34"/>
                    </svg>

                    
                    <span class="ml-3 @if (Request::segment(2) == 'user-mgt') text-white @else text-gray-900 dark:text-gray-200 @endif" 
                        sidebar-toggle-item>Revenue</span>
                </a>
               </li>

               {{-- Revenue End --}}

                 {{-- Reward --}}


                <li class="@if (Request::segment(2) == 'rewards') bg-[#117D34] text-white rounded-lg @else hover:bg-gray-100 @endif">
                <a href="" 
                class="flex items-center p-2 text-base rounded-lg group 
                        @if (Request::segment(2) == 'rewards') 
                            text-white 
                        @else 
                            text-gray-900 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 
                        @endif">
                    
                   <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
                    <path d="M10.5713 2.252C8.3663 2.24033 6.21964 4.36367 7.19964 7.00033H3.5013C2.88246 7.00033 2.28897 7.24617 1.85139 7.68375C1.4138 8.12134 1.16797 8.71483 1.16797 9.33367V11.667C1.16797 11.9764 1.29089 12.2732 1.50968 12.492C1.72847 12.7108 2.02522 12.8337 2.33464 12.8337H12.8346V9.33367H15.168V12.8337H25.668C25.9774 12.8337 26.2741 12.7108 26.4929 12.492C26.7117 12.2732 26.8346 11.9764 26.8346 11.667V9.33367C26.8346 8.71483 26.5888 8.12134 26.1512 7.68375C25.7136 7.24617 25.1201 7.00033 24.5013 7.00033H20.803C22.168 3.18533 17.0346 0.490335 14.6663 3.78033L14.0013 4.667L13.3363 3.757C12.6013 2.71867 11.5863 2.26367 10.5713 2.252ZM10.5013 4.667C11.5396 4.667 12.0646 5.927 11.3296 6.662C10.5946 7.397 9.33463 6.872 9.33463 5.83367C9.33463 5.52425 9.45755 5.2275 9.67634 5.00871C9.89514 4.78992 10.1919 4.667 10.5013 4.667ZM17.5013 4.667C18.5396 4.667 19.0646 5.927 18.3296 6.662C17.5946 7.397 16.3346 6.872 16.3346 5.83367C16.3346 5.52425 16.4576 5.2275 16.6763 5.00871C16.8951 4.78992 17.1919 4.667 17.5013 4.667ZM2.33464 14.0003V23.3337C2.33464 23.9525 2.58047 24.546 3.01805 24.9836C3.45564 25.4212 4.04913 25.667 4.66797 25.667H23.3346C23.9535 25.667 24.547 25.4212 24.9846 24.9836C25.4221 24.546 25.668 23.9525 25.668 23.3337V14.0003H15.168V23.3337H12.8346V14.0003H2.33464Z" fill="#0E7D34"/>
                    <path d="M10.7901 25.6673L10.3234 21.934C10.0707 21.8368 9.83266 21.7201 9.60944 21.584C9.38622 21.4479 9.16727 21.302 8.9526 21.1465L5.48177 22.6048L2.27344 17.0632L5.2776 14.7882C5.25816 14.652 5.24844 14.521 5.24844 14.395V13.6075C5.24844 13.4807 5.25816 13.3493 5.2776 13.2132L2.27344 10.9382L5.48177 5.39648L8.9526 6.85482C9.16649 6.69926 9.3901 6.55343 9.62344 6.41732C9.85677 6.28121 10.0901 6.16454 10.3234 6.06732L10.7901 2.33398H17.2068L17.6734 6.06732C17.9262 6.16454 18.1646 6.28121 18.3886 6.41732C18.6126 6.55343 18.8312 6.69926 19.0443 6.85482L22.5151 5.39648L25.7234 10.9382L22.7193 13.2132C22.7387 13.3493 22.7484 13.4807 22.7484 13.6075V14.3938C22.7484 14.5206 22.729 14.652 22.6901 14.7882L25.6943 17.0632L22.4859 22.6048L19.0443 21.1465C18.8304 21.302 18.6068 21.4479 18.3734 21.584C18.1401 21.7201 17.9068 21.8368 17.6734 21.934L17.2068 25.6673H10.7901ZM14.0568 18.084C15.1845 18.084 16.147 17.6854 16.9443 16.8882C17.7415 16.0909 18.1401 15.1284 18.1401 14.0007C18.1401 12.8729 17.7415 11.9104 16.9443 11.1132C16.147 10.3159 15.1845 9.91732 14.0568 9.91732C12.9095 9.91732 11.942 10.3159 11.1541 11.1132C10.3662 11.9104 9.97266 12.8729 9.97344 14.0007C9.97422 15.1284 10.3682 16.0909 11.1553 16.8882C11.9424 17.6854 12.9095 18.084 14.0568 18.084Z" fill="#0E7D34"/>
                    </svg>

                    
                    <span class="ml-3 @if (Request::segment(2) == 'rewards') text-white @else text-gray-900 dark:text-gray-200 @endif" 
                        sidebar-toggle-item>Rewards</span>
                </a>
               </li>

               {{-- Reward End --}}




                 


                   

                   

                    {{-- Department --}}

                    {{-- <li
                        class="@if (Request::segment(2) == 'savings') true @else false @endif   dark:text-gray-200  dark:hover:bg-gray-700 dark:bg-gray-600">
                        <button type="button"
                            class="flex items-center w-full p-2 text-base text-gray-900 transition duration-75 rounded-lg group hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
                            aria-controls="dropdown-savings" data-collapse-toggle="dropdown-savings"
                            aria-expanded="{{ Request::segment(2) == 'savings' ? 'true' : 'false' }}">
                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V5Zm16 14a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v2ZM4 13a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-6Zm16-2a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v6Z"/>
                              </svg>
                            <span class="flex-1 ml-3 text-left whitespace-nowrap" sidebar-toggle-item>Department</span>
                            <svg sidebar-toggle-item class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <ul id="dropdown-savings"
                            class="@if (Request::segment(2) != 'savings') hidden @endif py-2 space-y-2 bg-gray-100">

                            <li>
                                <a href=""
                                    class="flex items-center p-2 text-base transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">Add
                                    Department </a>
                            </li>

                            <li>
                                <a href=""
                                    class="flex items-center p-2 text-base transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">View
                                    Departments </a>
                            </li>
                        </ul>
                    </li> --}}

                    

                    {{-- Department --}}

                    


                    

                   


                  

                   


                  


                    {{-- settings --}}

                    <li class="@if (Request::segment(2) == 'settings') true @else false @endif  dark:text-gray-200  dark:hover:bg-gray-700 dark:bg-gray-600">
                        <a href="{{ route('admin.setting.general') }}"
                        class="flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
                        <path d="M10.7901 25.6673L10.3234 21.934C10.0707 21.8368 9.83266 21.7201 9.60944 21.584C9.38622 21.4479 9.16727 21.302 8.9526 21.1465L5.48177 22.6048L2.27344 17.0632L5.2776 14.7882C5.25816 14.652 5.24844 14.521 5.24844 14.395V13.6075C5.24844 13.4807 5.25816 13.3493 5.2776 13.2132L2.27344 10.9382L5.48177 5.39648L8.9526 6.85482C9.16649 6.69926 9.3901 6.55343 9.62344 6.41732C9.85677 6.28121 10.0901 6.16454 10.3234 6.06732L10.7901 2.33398H17.2068L17.6734 6.06732C17.9262 6.16454 18.1646 6.28121 18.3886 6.41732C18.6126 6.55343 18.8312 6.69926 19.0443 6.85482L22.5151 5.39648L25.7234 10.9382L22.7193 13.2132C22.7387 13.3493 22.7484 13.4807 22.7484 13.6075V14.3938C22.7484 14.5206 22.729 14.652 22.6901 14.7882L25.6943 17.0632L22.4859 22.6048L19.0443 21.1465C18.8304 21.302 18.6068 21.4479 18.3734 21.584C18.1401 21.7201 17.9068 21.8368 17.6734 21.934L17.2068 25.6673H10.7901ZM14.0568 18.084C15.1845 18.084 16.147 17.6854 16.9443 16.8882C17.7415 16.0909 18.1401 15.1284 18.1401 14.0007C18.1401 12.8729 17.7415 11.9104 16.9443 11.1132C16.147 10.3159 15.1845 9.91732 14.0568 9.91732C12.9095 9.91732 11.942 10.3159 11.1541 11.1132C10.3662 11.9104 9.97266 12.8729 9.97344 14.0007C9.97422 15.1284 10.3682 16.0909 11.1553 16.8882C11.9424 17.6854 12.9095 18.084 14.0568 18.084Z" fill="#0E7D34"/>
                        </svg>
                        <span class="ml-3" sidebar-toggle-item> Settings</span>
                    </a>
                    </li>
















        


                </ul>




            </div>
        </div>
        <div class="absolute bottom-0 left-0 justify-center hidden w-full p-4 space-x-4 bg-white lg:flex dark:bg-gray-800 dark:text-gray-100"
            sidebar-bottom-menu>
            Logged in as {{ Auth::user()->first_name }}

            <form method="POST" action="{{ route('logout') }}">
                @csrf

                <button type="submit" data-tooltip-target="tooltip-logout"
                    class="block w-full px-4 py-2 text-gray-700 text-start text-md hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white">
                    <svg class="icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2">
                        </path>
                        <path d="M9 12h12l-3 -3"></path>
                        <path d="M18 15l3 -3"></path>
                    </svg>
                </button>
            </form>

            <div id="tooltip-logout" role="tooltip"
                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                Logout from you account
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>

        </div>
    </div>
</aside>
