
@extends('user.frontend.layouts.app')
@section('title', 'Forget Password')
@section('content')


    <div class="w-full mb-[6rem] mt-[10rem]">
        <div class="w-full px-4  mx-auto my-8">
            <div class="container mx-auto">

                {{-- login --}}

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4 mb-4">

    {{-- Right Section --}}
    <div class="relative flex justify-center items-center mt-4">
            <img src="{{ asset('images/logo.png') }}" class="absolute -top-14">

            <div class="bg-white flex justify-center items-center">
            <img src="{{ asset('images/reset-password.png') }}" class="w-full max-w-[692px] h-auto md:h-[672.73px] object-contain" alt="Signin image">
            </div>
    </div>

    {{-- left Section --}}
    <div class="p-6">
        <h2 class="text-3xl font-semibold text-[#0e7d34]   dark:text-white">Reset Password?</h2>
        <form action="{{ route('password.store') }}" method="POST">
            @csrf


                    <!-- Password Reset Token -->
                    <input type="hidden" name="token" value="{{ $request->route('token') }}">
                      <div class="mt-4">
                <x-input-label for="email" class="block font-medium text-md text-gray-700 dark:text-white mb-3" :value="__('Email')" />
                    <div class="relative mb-2">
                        <x-input-icon>
                                      <svg class="w-5 h-5 text-gray-800 dark:text-white" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path fill-rule="evenodd"
                                d="M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1Zm2-3a2 2 0 1 1 4 0v3h-4V7Zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                                clip-rule="evenodd" />
                        </svg>
                        </x-input-icon>
                        <x-text-input type="email" :value="old('email', $request->email)" name="email" id="email" class="block mt-1 p-4 font-medium w-full bg-gray-transperent rounded-sm border border-gray-400 ps-10" required autocomplete="new-password" />
                    </div>
                    <x-input-error :messages="$errors->get('email')" class="mt-2" />

            </div>

            <div class="mt-4">
                <x-input-label for="password" class="block font-medium text-md text-gray-700 dark:text-white mb-3" :value="__('Enter new password')" />
                    <div class="relative mb-2">
                        <x-input-icon>
                                      <svg class="w-5 h-5 text-gray-800 dark:text-white" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path fill-rule="evenodd"
                                d="M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1Zm2-3a2 2 0 1 1 4 0v3h-4V7Zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                                clip-rule="evenodd" />
                        </svg>
                        </x-input-icon>
                        <x-text-input type="password" name="password" id="password" class="block mt-1 p-4 font-medium w-full bg-gray-transperent rounded-sm border border-gray-400 ps-10" required autocomplete="new-password" />
                    </div>
                    <x-input-error :messages="$errors->get('password')" class="mt-2" />

            </div>

            {{-- confirm password --}}

              <div class="mt-4">
                <x-input-label for="password_confirmation" class="block font-medium text-md text-gray-700 dark:text-white mb-3" :value="__('Enter new password')" />
                    <div class="relative mb-2">
                        <x-input-icon>
                                      <svg class="w-5 h-5 text-gray-800 dark:text-white" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path fill-rule="evenodd"
                                d="M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1Zm2-3a2 2 0 1 1 4 0v3h-4V7Zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                                clip-rule="evenodd" />
                        </svg>
                        </x-input-icon>
                        <x-text-input type="password" name="password_confirmation" id="password_confirmation" class="block mt-1 p-4 font-medium w-full bg-gray-transperent rounded-sm border border-gray-400 ps-10" placeholder="Enter your password" />
                    </div>
                    <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />

            </div>


 <div class="flex justify-center items-center w-full mt-8">
                        <x-primary-button class="w-full flex justify-center items-center px-4 py-4">
                            {{ __('Reset Password') }}
                        </x-primary-button>
                    </div>

        </form>

    </div>

</div>


                {{-- login --}}
            </div>
        </div>
    </div>





@endsection





