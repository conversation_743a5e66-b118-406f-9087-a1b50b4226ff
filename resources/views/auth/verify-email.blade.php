@extends('user.frontend.layouts.app')
@section('title', 'verify-email')
@section('content')

<section class="w-full mb-[6rem] mt-[12rem]">
    <div class="w-full px-4  mx-auto my-8">
        <div class="container mx-auto">

                           <div class="grid grid-cols-2 gap-8">
                        <div>
                            <div class="flex mx-auto w-full  items-center justify-center mb-8">
                                <div class="">
                                    <img src="{{ asset('images/signup-logo.png') }}" alt="">
                                </div>
                            </div>
                           <div class="w-full mx-auto">
                                <img src="{{ asset('images/enter-otp.png') }}" alt="">
                           </div>
                        </div>

                        <div>
                            <div class="w-full mx-auto">
                                <!-- Form Header -->
                                <div class="mb-8">
                                    <h2 class="text-3xl font-bold text-[#0E7D34] mb-2">OTP Verification</h2>
                                    <p class="text-gray-600">Enter the 6- digit code sent to your email</p>
                                </div>

                                 <!-- Alert Messages -->
        <div id="alert-container"></div>

        @if(session('error'))
            <div class="alert alert-error">
                {{ session('error') }}
            </div>
        @endif

        @if(session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif

                                <!-- Registration Form -->
                                <form action="{{ route('verifyotp') }}" method="POST" class="space-y-6">

                                    @csrf

                                    <!-- Name Fields -->
                                        <div class="flex space-x-2 justify-center">
                                            <input type="text" maxlength="1" name="token[]" class="w-12 h-12 text-center border text-[#0E7D34] text-lg  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="1"/>
                                            <input type="text" maxlength="1"  name="token[]" class="w-12 h-12 text-center border  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="2"/>
                                            <input type="text" maxlength="1"  name="token[]" class="w-12 h-12 text-center border  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="3"/>
                                            <input type="text"  maxlength="1"  name="token[]" class="w-12 h-12 text-center border  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="-"/>
                                            <input type="text" maxlength="1"  name="token[]" class="w-12 h-12 text-center border  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="-"/>
                                            <input type="text" maxlength="1"  name="token[]" class="w-12 h-12 text-center border  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="-"/>
                                        </div>


                                    <!-- Submit Button -->
                                    <button type="submit"
                                            class="w-full bg-[#0E7D34] hover:bg-[#39A75E] text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        Verify OTP
                                    </button>
  {{-- <!-- Resend OTP Section -->
        <div class="border-t border-gray-200 pt-6">
            <div class="text-center">
                <p class="text-gray-600 mb-4">Didn't receive the code?</p>

                <!-- Countdown Section -->
                <div id="countdown-section" class="hidden mb-4">
                    <p class="text-sm text-gray-500">
                        You can request a new code in
                        <span id="countdown" class="font-semibold text-blue-600">60</span>
                        seconds
                    </p>
                </div>

                <!-- Resend Button -->
                <button id="resendBtn" onclick="resendOtp()" class="inline-flex items-center justify-center px-4 py-2 bg-[#0E7D34] hover:bg-[#39A75E] disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                    <!-- Loading Spinner -->
                    <svg id="resendSpinner" class="hidden animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>

                    <!-- Resend Icon -->
                    <svg id="resendIcon" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>

                    <span id="resendText">Resend Code</span>
                </button>

                <input type="hidden" id="userEmail" value="{{ auth()->user()->email ?? '' }}">
            </div>
        </div> --}}

            <div class="resend-section border-t border-gray-200 pt-6">
                 <div class="text-center">
                    <p class="text-gray-600 mb-4">Didn't receive the code?</p>
                    <button id="resendBtn" onclick="resendOtp()" class="inline-flex items-center justify-center px-4 py-2 bg-[#0E7D34] hover:bg-[#39A75E] disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <svg id="resendIcon" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg> Resend Code
                    </button>
                 </div>
            </div>


                                    <!-- Login Link -->
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">
                                            Already have an account?
                                            <a href="{{ route('login') }}" class="text-[#0E7D34] hover:text-[#39A75E] font-medium">
                                                Sign in here
                                            </a>
                                        </p>
                                    </div>
                                </form>
                            </div>
                        </div>
                     </div>
        </div>
    </div>
</section>

@endsection





<script>
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input[name="token[]"]');

    inputs.forEach((input, index) => {
        // Move to next input on input
        input.addEventListener('input', function(e) {
            const value = e.target.value;

            // Only allow digits
            if (!/^\d$/.test(value)) {
                e.target.value = '';
                return;
            }

            // Move to next input if available
            if (value && index < inputs.length - 1) {
                inputs[index + 1].focus();
            }
        });

        // Handle backspace to move to previous input
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' && !e.target.value && index > 0) {
                inputs[index - 1].focus();
            }
        });

        // Handle paste event
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedData = e.clipboardData.getData('text');
            const digits = pastedData.replace(/\D/g, '').slice(0, 6);

            digits.split('').forEach((digit, i) => {
                if (inputs[index + i]) {
                    inputs[index + i].value = digit;
                }
            });

            // Focus on the next empty input or the last filled input
            const nextIndex = Math.min(index + digits.length, inputs.length - 1);
            inputs[nextIndex].focus();
        });
    });
});
</script>

{{--
 <script>

        // Resend OTP functionality
        let countdownTimer;
        let countdownActive = false;

        function resendOtp() {
            if (countdownActive) return;

            const email = document.getElementById('userEmail').value;
            const resendBtn = document.getElementById('resendBtn');
            const resendText = document.getElementById('resendText');
            const resendSpinner = document.getElementById('resendSpinner');

            if (!email) {
                showAlert('Email address not found. Please refresh the page.', 'error');
                return;
            }

            // Show loading state
            resendBtn.disabled = true;
            resendText.style.display = 'none';
            resendSpinner.style.display = 'inline-block';

            // Make AJAX request
            fetch('{{ route("resend.otp") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    email: email
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                    startCountdown();
                    clearOtpInputs();
                } else {
                    showAlert(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('An error occurred while resending the code. Please try again.', 'error');
            })
            .finally(() => {
                // Reset button state
                resendBtn.disabled = false;
                resendText.style.display = 'inline';
                resendSpinner.style.display = 'none';
            });
        }

        function startCountdown() {
            countdownActive = true;
            let timeLeft = 60;

            const resendBtn = document.getElementById('resendBtn');
            const countdownSection = document.getElementById('countdown-section');
            const countdownElement = document.getElementById('countdown');

            resendBtn.style.display = 'none';
            countdownSection.style.display = 'block';

            countdownTimer = setInterval(() => {
                timeLeft--;
                countdownElement.textContent = timeLeft;

                if (timeLeft <= 0) {
                    clearInterval(countdownTimer);
                    countdownActive = false;
                    resendBtn.style.display = 'inline-block';
                    countdownSection.style.display = 'none';
                }
            }, 1000);
        }

        function clearOtpInputs() {
            const otpInputs = document.querySelectorAll('.otp-input');
            otpInputs.forEach(input => {
                input.value = '';
            });
            otpInputs[0].focus();
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';

            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;

            // Auto-hide after 5 seconds
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        // Auto-submit form when all inputs are filled
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('otp-input')) {
                const otpInputs = document.querySelectorAll('.otp-input');
                const allFilled = Array.from(otpInputs).every(input => input.value.length === 1);

                if (allFilled) {
                    // Optional: Auto-submit after a short delay
                    setTimeout(() => {
                        document.getElementById('otpForm').submit();
                    }, 500);
                }
            }
        });
    </script> --}}





<script>
function resendOtp() {
    const email = '{{ auth()->user()->email ?? "" }}';

    fetch('{{ route("resend.otp") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({ email: email })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('New verification code sent to your email!');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('An error occurred. Please try again.');
    });
}
</script>
