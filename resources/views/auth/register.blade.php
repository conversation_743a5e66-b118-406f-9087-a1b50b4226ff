@extends('user.frontend.layouts.app')
@section('title', 'Register')
@section('content')


    <section>

        <div class="w-full mb-[6rem] mt-[10rem]">
            <div class="w-full px-4  mx-auto my-8">
                <div class="container mx-auto">
                    <div class="grid grid-cols-2 gap-8">
                        <div>
                             <div class="flex mx-auto w-full  items-center justify-center mb-8">
                                <div class="">
                                    <img src="{{ asset('images/signup-logo.png') }}" alt="">
                                </div>
                            </div>
                           <div class="w-full mx-auto">
                                <img src="{{ asset('images/signup.png') }}" alt="">
                           </div>
                        </div>

                        <div>
                            <div class="w-full mx-auto">
                                <!-- Form Header -->
                                <div class="mb-8">
                                    <h2 class="text-3xl font-bold text-[#0E7D34] mb-2">Sign Up</h2>
                                    <p class="text-gray-600">Welcome to Osusu. Register to get started</p>
                                </div>

                                <!-- Registration Form -->

                                <form action="{{ route('register') }}" method="POST" class="space-y-6"> {{$errors}}
                                    @csrf

                                    <!-- Name Fields -->
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
                                                First Name
                                            </label>
                                            <div class="relative">
                                            <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                 <path fill-rule="evenodd" d="M12 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8Zm-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4h-4Z" clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                            <input type="text" id="first_name"  name="first_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required placeholder="John">
                                             </div>
                                            <x-input-error :messages="$errors->get('first_name')" class="mt-2" />
                                        </div>
                                        <div>
                                            <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
                                                Last Name
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                 <path fill-rule="evenodd" d="M12 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8Zm-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4h-4Z" clip-rule="evenodd"/>
                                                </svg>
                                                </div>
                                                <input type="text" id="last_name"  name="last_name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required placeholder="Mike">
                                            </div>
                                            <x-input-error :messages="$errors->get('last_name')" class="mt-2" />
                                        </div>
                                    </div>

                                    <!-- Email -->
                                    <div>
                                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                            Email Address
                                        </label>
                                         <div class="relative">
                                            <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                <svg class="w-4 h-4 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 16">
                                                    <path d="m10.036 8.278 9.258-7.79A1.979 1.979 0 0 0 18 0H2A1.987 1.987 0 0 0 .641.541l9.395 7.737Z"/>
                                                    <path d="M11.241 9.817c-.36.275-.801.425-1.255.427-.428 0-.845-.138-1.187-.395L0 2.6V14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2.5l-8.759 7.317Z"/>
                                                </svg>
                                            </div>
                                            <input type="text" id="email" name="email" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="<EMAIL>">
                                        </div>
                                            <x-input-error :messages="$errors->get('email')" class="mt-2" />
                                    </div>


                                       <!-- Country -->

                                            {{-- city/country --}}
                                  <!-- locality -->
                                  <div class="" >
                                    <div class="" x-data="{
                                        loading:false,
                                        response:{},
                                            states: [],
                                             async getStates(event) {
                                            this.response = await (await fetch('/location/state/'+event.target.value+'/json', {
                                            headers: {
                                                'Content-Type': 'application/json',
                                                'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                                            }

                                            })).json();
                                             if(this.response.status == 200){
                                                this.loading=false;
                                                this.states = this.response.data;
                                            }
                                }
                                    }">
                                    <div class="col">
                                        <div class="d-flex mb-2">
                                            <x-input-label for="state" class="d-inline mb-2 text-md font-medium text-gray-700 dark:text-white flex ">{{ __('Select Country') }}

                                            </x-input-label>
                                            <div role="status" x-show="loading" class="ml-3">
                                                <svg aria-hidden="loading" width="16px" height="16px" class="w-5 h-5 text-gray-200 d-inline animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                                    <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                                </svg>
                                                <span class="sr-only">Loading...</span>
                                            </div>
                                        </div>

                                        <div class="relative mb-2">


                                        <x-select-input id="country"
                                        x-on:change="loading =! loading"  @change="getStates"
                                         value="{{ old('country_id') }}"
                                        class="block w-full p-2 form-control   bg-gray-transparent border border-gray-300 text-gray-700 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                        name="country_id"  required autofocus >
                                        <option value="" >Select your country</option>
                                         @foreach ($countries as $country)
                                        <option {{ @old('country') == $country->id ? 'selected' : '' }}value="{{ $country->id }}">
                                        {{ $country->name }}</option>
                                         @endforeach
                                        </x-select-input>
                                        </div>
                                        <x-input-error :messages="$errors->get('country')" class="mt-2" />
                                    </div>

                                    </div>
                                    </div>

                                    {{-- city/country --}}

                                    <!-- Password -->
                                    <div>
                                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                            Password
                                        </label>
                                        <input type="password" id="password" name="password" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" placeholder="••••••••">
                                        <x-input-error :messages="$errors->get('password')" class="mt-2" />
                                    </div>

                                    <!-- Confirm Password -->
                                    <div>
                                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                                            Confirm Password
                                        </label>
                                        <input type="password" id="password_confirmation" name="password_confirmation" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                            placeholder="••••••••">
                                    <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
                                    </div>

                                    <!-- Terms and Conditions -->
                                    <div class="flex items-start">
                                        <input type="checkbox" id="terms" name="terms"  requiredclass="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                        <label for="terms" class="ml-3 text-sm text-gray-600">
                                            Your personal information is securely handled in compliance with  privacy standards. By signing up, you agree to
                                            <a href="#" class="text-[#0E7D34]hover:text-[#39A75E] font-medium">Terms of Service</a>
                                            and
                                            <a href="#" class="text-[#0E7D34] hover:text-[#39A75E] font-medium">Privacy Policy.</a>
                                        </label>
                                    </div>
                                    <!-- Submit Button -->
                                    <button type="submit"
                                            class="w-full bg-[#0E7D34] hover:bg-[#39A75E] text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        Create Account
                                    </button>

                                    <!-- Social Registration -->
                                    <div class="mt-6">
                                        <div class="relative">
                                            <div class="absolute inset-0 flex items-center">
                                                <div class="w-full border-t border-gray-300"></div>
                                            </div>
                                            <div class="relative flex justify-center text-sm">
                                                <span class="px-2 bg-gray-50 text-gray-500">Or continue with</span>
                                            </div>
                                        </div>

                                        <div class="mt-6 grid grid-cols-2 gap-3">
                                            <button type="button"
                                                    class="w-full inline-flex justify-center py-3 px-4 border border-[#0E7D34] rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors">

                                            <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M20.2879 10.2249C20.2879 9.56658 20.2295 8.94158 20.1295 8.33325H10.7129V12.0916H16.1045C15.8629 13.3249 15.1546 14.3666 14.1046 15.0749V17.5749H17.3212C19.2045 15.8332 20.2879 13.2666 20.2879 10.2249Z" fill="#4285F4"/>
                                            <path d="M10.7121 19.9999C13.4121 19.9999 15.6704 19.0999 17.3204 17.5749L14.1038 15.0749C13.2038 15.6749 12.0621 16.0415 10.7121 16.0415C8.10377 16.0415 5.89544 14.2832 5.10377 11.9082H1.78711V14.4832C3.42877 17.7499 6.80377 19.9999 10.7121 19.9999Z" fill="#34A853"/>
                                            <path d="M5.10455 11.9083C4.89622 11.3083 4.78788 10.6666 4.78788 9.99993C4.78788 9.33326 4.90455 8.6916 5.10455 8.0916V5.5166H1.78789C1.10456 6.8666 0.712891 8.38326 0.712891 9.99993C0.712891 11.6166 1.10456 13.1333 1.78789 14.4833L5.10455 11.9083Z" fill="#FBBC05"/>
                                            <path d="M10.7121 3.95833C12.1871 3.95833 13.5038 4.46666 14.5454 5.45833L17.3954 2.60833C15.6704 0.991666 13.4121 0 10.7121 0C6.80377 0 3.42877 2.25 1.78711 5.51666L5.10377 8.09166C5.89544 5.71666 8.10377 3.95833 10.7121 3.95833Z" fill="#EA4335"/>
                                            </svg>

                                                <span class="ml-2">Google</span>
                                            </button>

                                            <button type="button"
                                                    class="w-full inline-flex justify-center py-3 px-4 border border-[#0E7D34] rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors">

                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M13.0642 6.68494L13.8092 6.37894C14.4142 6.13894 15.1962 5.89394 16.1192 6.04894C18.0102 6.36694 19.3142 7.38794 20.0912 8.74194C20.3912 9.26394 20.1492 9.95194 19.5892 10.1709C19.1095 10.3586 18.6994 10.6898 18.415 11.1194C18.1306 11.5489 17.9858 12.0558 18.0003 12.5707C18.0149 13.0857 18.1881 13.5836 18.4963 13.9963C18.8045 14.4091 19.2326 14.7167 19.7222 14.8769C20.2402 15.0469 20.5322 15.6219 20.3622 16.1399C19.9202 17.4819 19.2842 18.7209 18.5312 19.7209C17.7872 20.7089 16.8792 21.5289 15.8682 21.9299C15.2082 22.1899 14.5002 22.0929 13.8232 21.9249L13.4212 21.8179L12.8242 21.6449C12.5532 21.5659 12.2742 21.4979 12.0002 21.4979C11.7252 21.4979 11.4472 21.5659 11.1762 21.6449L10.5792 21.8179L10.1772 21.9249C9.50021 22.0929 8.7912 22.1909 8.13221 21.9299C6.85921 21.4259 5.73621 20.2499 4.88721 18.8629C3.9692 17.335 3.36287 15.6404 3.1032 13.8769C2.8762 12.3229 2.9992 10.5779 3.7182 9.10194C4.4582 7.58094 5.81421 6.39694 7.88121 6.04894C8.72121 5.90794 9.4432 6.09694 10.0212 6.31394L10.3522 6.44394L10.9362 6.68494C11.3362 6.84194 11.6512 6.93394 12.0002 6.93394C12.3482 6.93394 12.6642 6.84194 13.0642 6.68494ZM11.7682 2.76794C12.7442 1.79094 14.2432 1.70694 14.5962 2.06094C14.9502 2.41394 14.8662 3.91294 13.8892 4.88894C12.9132 5.86494 11.4142 5.94894 11.0612 5.59594C10.7072 5.24294 10.7912 3.74394 11.7682 2.76794Z" fill="#3A3A3A"/>
                                                    </svg>

                                                <span class="ml-2">Apple</span>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Login Link -->
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">
                                            Already have an account?
                                            <a href="{{ route('login') }}" class="text-[#0E7D34] hover:text-[#39A75E] font-medium">
                                                Sign in here
                                            </a>
                                        </p>
                                    </div>
                                </form>
                            </div>
                        </div>
                     </div>
                </div>
            </div>
        </div>
    </section>

@endsection
