@extends('user.frontend.layouts.app')
@section('title', 'Register')
@section('content')

<section class="w-full mb-[6rem] mt-[10rem]">
    <div class="w-full px-4  mx-auto my-8">
        <div class="container mx-auto">

                           <div class="grid grid-cols-2 gap-8">
                        <div>
                            <div class="flex mx-auto w-full  items-center justify-center mb-8">
                                <div class="">
                                    <img src="{{ asset('images/signup-logo.png') }}" alt="">
                                </div>
                            </div>
                           <div class="w-full mx-auto">
                                <img src="{{ asset('images/personal-info.png') }}" alt="">
                           </div>
                        </div>

                        <div>
                            <div class="w-full mx-auto">
                                <!-- Form Header -->
                                <div class="mb-8">
                                    <h2 class="text-3xl font-bold text-[#0E7D34] mb-2">Personal Information</h2>
                                    <p class="text-gray-600">Complete your personal infomation to get started</p>
                                </div>

                                <!-- Registration Form -->

                               <!-- Registration Form -->

                                <form action="{{ route('store.info') }}" method="POST" class="space-y-6"> {{$errors}}
                                    @csrf

                                    <!-- Put this at the top of each page -->
                                    <div class="flex mx-auto w-full  items-center justify-center mb-8">
                                        <div class="flex items-center w-full mx-auto">
                                            <!-- Steps: update 'currentStep' to 1, 2, 3, or 4 -->
                                            <script>
                                            const currentStep = 1; // 👈 Change this on each page
                                            </script>

                                            <div class="flex w-full mx-auto items-center justify-between">
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-1">1</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-1"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-2">2</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-2"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-3">3</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-3"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-4">4</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Address -->
                                    <div>
                                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                         Address
                                        </label>
                                         <div class="relative">
                                            <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                <svg class="w-4 h-4 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 16">
                                                    <path d="m10.036 8.278 9.258-7.79A1.979 1.979 0 0 0 18 0H2A1.987 1.987 0 0 0 .641.541l9.395 7.737Z"/>
                                                    <path d="M11.241 9.817c-.36.275-.801.425-1.255.427-.428 0-.845-.138-1.187-.395L0 2.6V14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2.5l-8.759 7.317Z"/>
                                                </svg>
                                            </div>
                                            <input type="text" id="address" name="address" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Enter your Address">
                                        </div>
                                            <x-input-error :messages="$errors->get('address')" class="mt-2" />
                                    </div>

                                    <!-- Phone -->
                                    <div>
                                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                            Phone Number
                                        </label>
                                         <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                 <path fill-rule="evenodd" d="M12 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8Zm-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4h-4Z" clip-rule="evenodd"/>
                                                </svg>
                                                </div>
                                                <input type="number" id="phone"  name="phone" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required placeholder="+****************">
                                            </div>
                                            <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                                    </div>

                                     <!-- Date of Birth -->
                                    <div>
                                        <label for="date" class="block text-sm font-medium text-gray-700 mb-2">
                                            Date of Birth
                                        </label>
                                         <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                               <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 10h16m-8-3V4M7 7V4m10 3V4M5 20h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Zm3-7h.01v.01H8V13Zm4 0h.01v.01H12V13Zm4 0h.01v.01H16V13Zm-8 4h.01v.01H8V17Zm4 0h.01v.01H12V17Zm4 0h.01v.01H16V17Z"/>
                                                </svg>
                                                </div>
                                                <input type="date" id="date"  name="data_of_birth" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required placeholder="+****************">
                                        </div>
                                          <x-input-error :messages="$errors->get('data_of_birth')" class="mt-2" />
                                    </div>

                                    <!-- Gender -->
                                    <div>

                                          <x-input-label for="gender" :value="__('Gender')" class="block mb-2 font-medium text-gray-700 text-start text-l dark:text-white" />

                                            <x-select-input  type="text" id="gender" name="gender" value="{{ old('gender') }}" class="block w-full text-sm p-2 mt-1 font-bold text-gray-700 border border-gray-300 rounded-lg bg-gray-transparent sm:text-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" autofocus>
                                                    <option value="">Select your gender</option>
                                                    @foreach (App\Enums\Gender::cases() as $gender)
                                                    <option value="{{$gender->value}}">{{$gender->name}}</option>
                                                    @endforeach
                                            </x-select-input>
                                            <x-input-error :messages="$errors->get('gender')" class="mt-2" />
                                    </div>

                                    <!-- Submit Button -->
                                    <button type="submit"
                                            class="w-full bg-[#0E7D34] hover:bg-[#39A75E] text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        Proceed
                                    </button>
                                </form>
                            </div>
                        </div>
                     </div>
        </div>
    </div>
</section>

@endsection


{{--
@extends('user.frontend.layouts.app')
@section('title', 'Register')
@section('content')

<section class="w-full mb-[6rem] mt-[10rem]">
    <div class="w-full px-4  mx-auto my-8">
        <div class="container mx-auto">

                           <div class="grid grid-cols-2 gap-8">
                        <div>
                            <div class="flex mx-auto w-full  items-center justify-center mb-8">
                                <div class="">
                                    <img src="{{ asset('images/signup-logo.png') }}" alt="">
                                </div>
                            </div>
                           <div class="w-full mx-auto">
                                <img src="{{ asset('images/personal-info.png') }}" alt="">
                           </div>
                        </div>

                        <div>
                            <div class="w-full mx-auto">


                                <!-- Registration Form -->

                               <!-- Registration Form -->

                                <form id="multiStepForm" class="max-w-xl mx-auto bg-white p-6 rounded shadow">
                                    <!-- Step 1 -->

                                    <div class="form-step" data-step="0">

                                    <!-- Form Header -->
                                <div class="mb-8">
                                    <h2 class="text-3xl font-bold text-[#0E7D34] mb-2">Personal Information</h2>
                                    <p class="text-gray-600">Complete your personal infomation to get started</p>
                                </div>
                                     <!-- Put this at the top of each page -->
                                    <div class="flex mx-auto w-full  items-center justify-center mb-8">
                                        <div class="flex items-center w-full mx-auto">
                                            <!-- Steps: update 'currentStep' to 1, 2, 3, or 4 -->
                                            <script>
                                            const currentStep = 1; // 👈 Change this on each page
                                            </script>

                                            <div class="flex w-full mx-auto items-center justify-between">
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-1">1</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-1"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-2">2</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-2"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-3">3</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-3"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-4">4</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Address -->
                                    <div class="mt-4">
                                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                         Address
                                        </label>
                                         <div class="relative">
                                            <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                <svg class="w-4 h-4 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 16">
                                                    <path d="m10.036 8.278 9.258-7.79A1.979 1.979 0 0 0 18 0H2A1.987 1.987 0 0 0 .641.541l9.395 7.737Z"/>
                                                    <path d="M11.241 9.817c-.36.275-.801.425-1.255.427-.428 0-.845-.138-1.187-.395L0 2.6V14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2.5l-8.759 7.317Z"/>
                                                </svg>
                                            </div>
                                            <input type="text" id="address" name="address" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Enter your Address">
                                        </div>
                                            <x-input-error :messages="$errors->get('address')" class="mt-2" />
                                    </div>

                                    <!-- Phone -->
                                    <div class="mt-4">
                                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                            Phone Number
                                        </label>
                                         <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                 <path fill-rule="evenodd" d="M12 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8Zm-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4h-4Z" clip-rule="evenodd"/>
                                                </svg>
                                                </div>
                                                <input type="number" id="phone"  name="phone" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required placeholder="+****************">
                                            </div>
                                            <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                                    </div>

                                     <!-- Date of Birth -->
                                    <div class="mt-4">
                                        <label for="date" class="block text-sm font-medium text-gray-700 mb-2">
                                            Date of Birth
                                        </label>
                                         <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                               <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 10h16m-8-3V4M7 7V4m10 3V4M5 20h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Zm3-7h.01v.01H8V13Zm4 0h.01v.01H12V13Zm4 0h.01v.01H16V13Zm-8 4h.01v.01H8V17Zm4 0h.01v.01H12V17Zm4 0h.01v.01H16V17Z"/>
                                                </svg>
                                                </div>
                                                <input type="date" id="date"  name="data_of_birth" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required placeholder="+****************">
                                        </div>
                                          <x-input-error :messages="$errors->get('data_of_birth')" class="mt-2" />
                                    </div>

                                    <!-- Gender -->
                                    <div class="mt-4">

                                          <x-input-label for="gender" :value="__('Gender')" class="block mb-2 font-medium text-gray-700 text-start text-l dark:text-white" />

                                            <x-select-input  type="text" id="gender" name="gender" value="{{ old('gender') }}" class="block w-full text-sm p-2 mt-1 font-bold text-gray-700 border border-gray-300 rounded-lg bg-gray-transparent sm:text-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" autofocus>
                                                    <option value="">Select your gender</option>
                                                    @foreach (App\Enums\Gender::cases() as $gender)
                                                    <option value="{{$gender->value}}">{{$gender->name}}</option>
                                                    @endforeach
                                            </x-select-input>
                                            <x-input-error :messages="$errors->get('gender')" class="mt-2" />
                                    </div>
                                    <div class="flex justify-end mt-4">
                                        <button type="button" class="next-btn bg-[#0E7D34] hover:bg-[#39A75E] text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Next</button>
                                    </div>
                                    </div>

                                    <!-- Step 2 -->
                                    <div class="form-step hidden" data-step="1">
                                         <!-- Form Header -->
                                <div class="mb-8">
                                    <h2 class="text-3xl font-bold text-[#0E7D34] mb-2">KYC Verification</h2>
                                    <p class="text-gray-600">Please submit the following documents to complete the verification process</p>
                                </div>

                                  <!-- Put this at the top of each page -->
                                    <div class="flex mx-auto w-full  items-center justify-center mb-8">
                                        <div class="flex items-center w-full mx-auto">
                                            <!-- Steps: update 'currentStep' to 1, 2, 3, or 4 -->
                                            <script>
                                            const currentStep = 2; // 👈 Change this on each page
                                            </script>

                                            <div class="flex w-full mx-auto items-center justify-between">
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-1">1</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-1"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-2">2</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-2"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-3">3</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-3"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-4">4</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>



                    <div class="grid grid-cols-1 gap-4mt-5" x-data="{
                            loading:true,
                                response:{},
                                    meansofid: [],
                                    async  getIdentification() {
                                    this.response = await (await fetch('/identification', {
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRF-TOKEN': document.head.querySelector('meta[name=csrf-token]').content
                                    }

                                    })).json();
                                    if(this.response.status == 200){
                                        this.loading = false;
                                        this.meansofid = this.response.data;
                                    }
                        }
                            }" x-init="getIdentification()">


                        <div class="w-full ml-2 col-span-1">
                            <x-input-label for="meansofid" class="block mb-4 text-md font-medium text-gray-900 dark:text-white flex ">{{ __('Select I.D Document to upload') }}
                                <div role="status" x-show="loading">
                                <svg aria-hidden="loading" class="w-5 h- text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                                    <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                                </svg>
                                <span class="sr-only">Loading...</span>
                            </div>
                            </x-input-label>
                            <div class="relative mb-2">
                                <x-input-icon>
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H4Zm10 5a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1Zm0 3a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1Zm0 3a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1Zm-8-5a3 3 0 1 1 6 0 3 3 0 0 1-6 0Zm1.942 4a3 3 0 0 0-2.847 2.051l-.044.133-.004.012c-.042.126-.055.167-.***************.**************.**************.146.155A1 1 0 0 0 6 17h6a1 1 0 0 0 .811-.415.713.713 0 0 1 .146-.155c.019-.016.031-.026.038-.04.014-.027 0-.068-.042-.194l-.004-.012-.044-.133A3 3 0 0 0 10.059 14H7.942Z" clip-rule="evenodd"/>
                                    </svg>


                                </x-input-icon>
                            <x-select-input id="meansofid"

                            class="block mt-1 w-full p-4 font-bold ps-10 bg-gray-transparent border border-gray-300 text-gray-900 sm:text-md rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"  name="meansofid"  required autofocus >
                                <option value="">Select</option>
                                <template x-for="meanid in meansofid" :key="meanid.id">
                                    <option :value="meanid.id" x-text="meanid.name"></option>
                                </template>


                            </x-select-input>
                            </div>
                            <x-input-error :messages="$errors->get('meansofid')" class="mt-2" />
                        </div>

                 <div x-data="uploadFile()">
                                        <!-- Drop zone 1: Government ID -->
                                        <div class="mt-4">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Upload selected identification document</label>

                                            <form id="kycForm">
                                        <!-- Government ID -->
                                        <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Government ID</label>
                                        <input type="file" name="means_of_verification" id="government_id" required class="block w-full border border-gray-300 p-2 rounded">
                                        </div>

                                        <!-- Address Proof -->
                                        <div class="mt-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Address Proof</label>
                                        <input type="file" name="proof_address" id="address_proof" required class="block w-full border border-gray-300 p-2 rounded">
                                        </div>

                                        <!-- Progress Bar -->
                                        <div class="mt-4 w-full bg-gray-200 rounded-full h-4">
                                        <div id="progressBar" class="bg-blue-500 h-full text-xs text-white text-center leading-4 rounded" style="width: 0%">0%</div>
                                        </div>

                                        <div id="status" class="text-sm text-green-600 mt-2 hidden">Upload complete!</div>
                                    </form>

                                    </div>





                                    <div class="flex justify-between mt-4">
                                        <button type="button" class="prev-btn bg-[#0E7D34] hover:bg-[#39A75E] disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200">Back</button>
                                        <button type="button" class="next-btn bg-[#0E7D34] hover:bg-[#39A75E] disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200">Next</button>
                                    </div>
                                    </div>

                                    <!-- Step 3 -->
                                    <div class="form-step hidden" data-step="2">
                                    <h2 class="text-xl font-bold mb-4">Step 3: Confirm & Submit</h2>
                                    <p class="mb-4">Please review your information before submitting.</p>
                                    <div class="flex justify-between">
                                        <button type="button" class="prev-btn bg-gray-300 px-4 py-2 rounded">Back</button>
                                        <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded">Submit</button>
                                    </div>
                                    </div>
                            </form>
                            </div>
                        </div>
                     </div>
        </div>
    </div>
</section>

@endsection



 <script>
    document.addEventListener("DOMContentLoaded", function () {
      const steps = document.querySelectorAll(".form-step");
      let currentStep = 0;

      function showStep(index) {
        steps.forEach((step, i) => {
          step.classList.toggle("hidden", i !== index);
        });
      }

      document.querySelectorAll(".next-btn").forEach(button => {
        button.addEventListener("click", () => {
          if (currentStep < steps.length - 1) {
            currentStep++;
            showStep(currentStep);
          }
        });
      });

      document.querySelectorAll(".prev-btn").forEach(button => {
        button.addEventListener("click", () => {
          if (currentStep > 0) {
            currentStep--;
            showStep(currentStep);
          }
        });
      });

      document.getElementById("multiStepForm").addEventListener("submit", function (e) {
        e.preventDefault();
        alert("Form submitted!");
        // You can handle actual form submission here (AJAX, fetch, etc.)
      });

      // Show initial step
      showStep(currentStep);
    });
  </script> --}}
