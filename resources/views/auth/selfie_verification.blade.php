@extends('user.frontend.layouts.app')
@section('title', 'Register')
@section('content')

<section class="w-full mb-[6rem] mt-[10rem]">
    <div class="w-full px-4  mx-auto my-8">
        <div class="container mx-auto">

                           <div class="grid grid-cols-2 gap-8">
                        <div>
                            <div class="flex mx-auto w-full  items-center justify-center mb-8">
                                <div class="">
                                    <img src="{{ asset('images/signup-logo.png') }}" alt="">
                                </div>
                            </div>
                           <div class="w-full mx-auto">
                                <img src="{{ asset('images/personal-info.png') }}" alt="">
                           </div>
                        </div>

                        <div>
                            <div class="w-full mx-auto">
                                <!-- Form Header -->
                                <div class="mb-8">
                                    <h2 class="text-3xl font-bold text-[#0E7D34] mb-2">Take A Selfie</h2>
                                    <p class="text-gray-600">Please submit the following documents to complete the verification process</p>
                                </div>

                                <!-- Registration Form -->

                               <!-- Registration Form -->

                                <form action="{{ route('register') }}" method="POST" class="space-y-6">
                                    @csrf

                                    <!-- Put this at the top of each page -->
                                    <div class="flex mx-auto w-full  items-center justify-center mb-8">
                                        <div class="flex items-center w-full mx-auto">
                                            <!-- Steps: update 'currentStep' to 1, 2, 3, or 4 -->
                                            <script>
                                            const currentStep = 3; // 👈 Change this on each page
                                            </script>

                                            <div class="flex w-full mx-auto items-center justify-between">
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-1">1</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-1"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-2">2</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-2"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-3">3</div>
                                                    <div class="flex-1 h-1 bg-gray-300 mx-2" id="line-3"></div>
                                                </div>
                                                <div class="flex-1 relative flex items-center">
                                                    <div class="rounded-full w-8 h-8 flex items-center justify-center text-white font-bold" id="step-4">4</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

    <div class="relative">
      <video id="video" autoplay playsinline class="w-full rounded-md border border-gray-300"></video>
      <canvas id="canvas" class="hidden"></canvas>
    </div>


    <div id="previewContainer" class="mt-4 hidden">
      <p class="text-sm text-gray-600 mb-2">Captured Selfie:</p>
      <img id="selfiePreview" class="rounded border border-gray-300 w-full" />
      <button id="uploadBtn" class="mt-4 w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">
        Upload Selfie
      </button>
    </div>




                                </form>

                                 <!-- Submit Button -->
                                        <div class="mt-6">
                                            <button type="button" onclick="location.href='/success'" class="w-full bg-[#0E7D34] hover:bg-[#39A75E] disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200">
                                                Proceed
                                            </button>
                                        </div>
                            </div>
                        </div>
                     </div>
        </div>
    </div>
</section>

@endsection



