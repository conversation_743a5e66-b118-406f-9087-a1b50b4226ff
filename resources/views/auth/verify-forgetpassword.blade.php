@extends('user.frontend.layouts.app')
@section('title', 'verify-Password')
@section('content')

<section class="w-full mb-[6rem] mt-[12rem]">
    <div class="w-full px-4  mx-auto my-8">
        <div class="container mx-auto">

                           <div class="grid grid-cols-2 gap-8">
                        <div>
                            <div class="flex mx-auto w-full  items-center justify-center mb-8">
                                <div class="">
                                    <img src="{{ asset('images/signup-logo.png') }}" alt="">
                                </div>
                            </div>
                           <div class="w-full mx-auto">
                                <img src="{{ asset('images/enter-otp.png') }}" alt="">
                           </div>
                        </div>

                        <div>
                            <div class="w-full mx-auto">
                                <!-- Form Header -->
                                <div class="mb-8">
                                    <h2 class="text-3xl font-bold text-[#0E7D34] mb-2">OTP Verification</h2>
                                    <p class="text-gray-600">Enter the 6- digit code sent to your email</p>
                                </div>

                                <!-- Registration Form -->

                                <form action="{{ route('verifyotp-forget') }}" method="POST" class="space-y-6">
                                    @csrf

                                    <!-- Name Fields -->
                                        <div class="flex space-x-2 justify-center">
                                            <input type="text" maxlength="1" name="token[]" class="w-12 h-12 text-center border text-[#0E7D34] text-lg  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="1"/>
                                            <input type="text" maxlength="1"  name="token[]" class="w-12 h-12 text-center border  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="2"/>
                                            <input type="text" maxlength="1"  name="token[]" class="w-12 h-12 text-center border  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="3"/>
                                            <input type="text"  maxlength="1"  name="token[]" class="w-12 h-12 text-center border  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="-"/>
                                            <input type="text" maxlength="1"  name="token[]" class="w-12 h-12 text-center border  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="-"/>
                                            <input type="text" maxlength="1"  name="token[]" class="w-12 h-12 text-center border  border-[#0E7D34] rounded focus:outline-none focus:ring-2 focus:ring-[#39A75E]" placeholder="-"/>
                                        </div>
                                    <!-- Submit Button -->
                                    <button type="submit"
                                            class="w-full bg-[#0E7D34] hover:bg-[#39A75E] text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        Verify OTP
                                    </button>



                                    <!-- Login Link -->
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">
                                            Didn't get the otp ?
                                            <a href="{{ route('login') }}" class="text-[#0E7D34] hover:text-[#39A75E] font-medium">
                                                request another one
                                            </a>
                                        </p>
                                    </div>
                                </form>
                            </div>
                        </div>
                     </div>
        </div>
    </div>
</section>

@endsection





<script>
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input[name="token[]"]');

    inputs.forEach((input, index) => {
        // Move to next input on input
        input.addEventListener('input', function(e) {
            const value = e.target.value;

            // Only allow digits
            if (!/^\d$/.test(value)) {
                e.target.value = '';
                return;
            }

            // Move to next input if available
            if (value && index < inputs.length - 1) {
                inputs[index + 1].focus();
            }
        });

        // Handle backspace to move to previous input
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' && !e.target.value && index > 0) {
                inputs[index - 1].focus();
            }
        });

        // Handle paste event
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedData = e.clipboardData.getData('text');
            const digits = pastedData.replace(/\D/g, '').slice(0, 6);

            digits.split('').forEach((digit, i) => {
                if (inputs[index + i]) {
                    inputs[index + i].value = digit;
                }
            });

            // Focus on the next empty input or the last filled input
            const nextIndex = Math.min(index + digits.length, inputs.length - 1);
            inputs[nextIndex].focus();
        });
    });
});
</script>


