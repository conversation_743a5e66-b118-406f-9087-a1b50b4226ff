@extends('user.frontend.layouts.app')
@section('title', 'Sign In')
@section('content')


    <div class="w-full mb-[6rem] mt-[10rem]">
        <div class="w-full px-4  mx-auto my-8">
            <div class="container mx-auto">

                {{-- login --}}

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4 mb-4">

    {{-- Right Section --}}
    <div class="relative flex justify-center items-center mt-4">
            <img src="{{ asset('images/logo.png') }}" class="absolute -top-14">

            <div class="bg-white flex justify-center items-center">
            <img src="{{ asset('images/signin.png') }}" class="w-full max-w-[692px] h-auto md:h-[672.73px] object-contain" alt="Signin image">
            </div>
    </div>

    {{-- left Section --}}
    <div class="p-6">
        <h2 class="text-3xl font-bold text-[#0e7d34]   dark:text-white">Welcome Back!</h2>
        <p class="text-gray-600 mt-4">Sign in to access your account</p>
        <form action="{{ route('login') }}" method="POST">
            @csrf

            <div class="mt-4">
                <x-input-label for="email" class="block font-medium text-md text-gray-700 dark:text-white mb-3" :value="__('Email')" />
                    <div class="relative mb-2">
                        <x-input-icon>
                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                            viewBox="0 0 24 24">
                                            <path
                                                d="M2.038 5.61A2.01 2.01 0 0 0 2 6v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6c0-.12-.01-.238-.03-.352l-.866.65-7.89 6.032a2 2 0 0 1-2.429 0L2.884 6.288l-.846-.677Z" />
                                            <path
                                                d="M20.677 4.117A1.996 1.996 0 0 0 20 4H4c-.225 0-.44.037-.642.105l.758.607L12 10.742 19.9 4.7l.777-.583Z" />
                                        </svg>
                        </x-input-icon>
                        <x-text-input type="email" name="email" id="email" class="block mt-1 p-4 font-medium w-full bg-gray-transperent rounded-sm border border-gray-400 ps-10" placeholder="Enter your email address" />
                        <x-input-error :messages="$errors->get('email')" class="mt-2" />

                    </div>
            </div>

        <div class="mt-4">
                <x-input-label for="password" class="block font-medium mb-3 text-md text-gray-700 dark:text-white" :value="__('Password')" />

                <div class="relative mb-2">
                    {{-- Left Lock Icon --}}
                    <div class="absolute inset-y-0 left-0 flex items-center ps-3 pointer-events-none">
                        <svg class="w-5 h-5 text-gray-800 dark:text-white" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path fill-rule="evenodd"
                                d="M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1Zm2-3a2 2 0 1 1 4 0v3h-4V7Zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>

                    {{-- Input Field --}}
                    <x-text-input
                        type="password"
                        name="password"
                        id="password"
                        class="block p-4 font-bold bg-gray-100 border border-gray-400 w-full ps-10 pe-10" placeholder="*******"
                    />
                  <x-input-error :messages="$errors->get('password')" class="mt-2" />



                </div>
       </div>

       {{-- remember me --}}

        <div class="flex items-start mt-2 mb-8">
                <div class="flex items-center h-4 mt-1 ml-1">
                <input id="remember_me" aria-describedby="remember_me" name="remember_me" type="checkbox"
                    class="appearance-none w-4 h-4 rounded-none scale-150 transform bg-white dark:bg-gray-900 border border-[#39a75e] dark:border-gray-700 text-indigo-600 focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-600 dark:focus:ring-offset-gray-800">
                    </div>
                <div class="ml-3 text-md">
                    <label for="remember_me"
                        class="text-gray-900 dark:text-white text-md font-medium">{{ __('Remember me') }}</label>
                </div>

                @if (Route::has('password.request'))
                    <a class="ml-auto  text-md font-medium text-[#0e7d34] underline dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
                        href="{{ route('password.request') }}">
                        {{ __('Forgot your password?') }}
                    </a>
                @endif


        </div>
     <div class="w-full flex justify-center mt-4">
        {{-- x-on:click="loading=true; document.getElementById('Login').submit();" --}}
         <x-primary-button type="submit"
                            value="Login"
                            class=" w-full flex justify-center items-center px-5 py-5 text-base font-medium text-center text-white dark:text-rent-gray-dark-70 bg-rent-blue-light-800 hover:bg-rent-blue-light-700 rounded-lg hover:bg-rent-blue-light-700 focus:ring-4 focus:bg-rent-blue-light-900  dark:bg-rent-blue-dark-200 dark:hover:bg-rent-blue-dark-300 dark:focus:bg-rent-blue-dark-400">
                            {{ __('Proceed') }}
        </x-primary-button>
    </div>


        <div class="flex items-center mt-8">
            <div class="flex-grow border-t border-[#0e7d34]"></div>
            <p class="mx-4 text-gray-600 text-sm">Or sign in with</p>
            <div class="flex-grow border-t border-[#0e7d34]"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">

           <button class="flex justify-center items-center gap-2 border border-gray-300 px-4 py-2 text-gray-700 ">
            <svg class="w-5 h-5" viewBox="0 0 48 48">
            <path fill="#fbc02d" d="M43.6 20.5H42V20H24v8h11.3c-1.5 4-5.3 7-9.3 7-5.5 0-10-4.5-10-10s4.5-10 10-10c2.6 0 5 .9 6.9 2.6l6-6C35.2 8.2 29.9 6 24 6 12.9 6 4 14.9 4 26s8.9 20 20 20 20-8.9 20-20c0-1.9-.2-3.4-.4-5.5z"/>
            <path fill="#e53935" d="M6.3 14.1l6.6 4.8C14.6 15.5 18.9 13 24 13c2.6 0 5 .9 6.9 2.6l6-6C35.2 8.2 29.9 6 24 6 16 6 9.1 10.5 6.3 14.1z"/>
            <path fill="#4caf50" d="M24 46c5.9 0 11.2-2.2 15.3-5.8l-7-5.7c-2.1 1.6-4.9 2.5-8.3 2.5-4 0-7.8-3-9.3-7l-6.6 5C9.1 41.5 16 46 24 46z"/>
            <path fill="#1565c0" d="M43.6 20.5H42V20H24v8h11.3c-.8 2-2.1 3.8-3.7 5.2l7 5.7c3.5-3.2 5.9-8.1 5.9-13.9 0-1.9-.2-3.4-.4-5.5z"/>
            </svg>
            <span>Google</span>

           </button>

             <!-- Apple Sign-in -->
            <button class="flex items-center justify-center gap-2 border border-gray-300 rounded-lg px-4 py-2 text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-white dark:hover:bg-gray-700 w-full">
                <svg class="w-5 h-5 fill-current" viewBox="0 0 24 24">
                    <path d="M16.365 1.43c0 1.14-.424 2.08-1.27 2.83-.846.755-1.774 1.19-2.78 1.3-.07-.13-.124-.33-.16-.59-.04-.25-.06-.5-.06-.74 0-1.13.42-2.06 1.26-2.82.84-.76 1.77-1.2 2.78-1.31.06.13.12.32.16.59.04.25.07.5.07.74zM20.71 15.31c-.31.71-.67 1.37-1.08 1.97-.41.61-.86 1.18-1.37 1.7-.43.44-.89.83-1.37 1.17-.49.35-1 .61-1.56.78-.49.13-.97.19-1.45.17-.48 0-.94-.05-1.39-.14-.46-.1-.89-.24-1.3-.43-.48-.2-.92-.46-1.33-.77-.4-.32-.75-.65-1.05-1-.28-.31-.56-.66-.82-1.06-.26-.39-.48-.79-.65-1.2-.16-.39-.31-.81-.43-1.25-.11-.42-.2-.85-.28-1.28-.08-.45-.13-.9-.16-1.35-.02-.38-.03-.76-.01-1.13.03-.61.14-1.22.33-1.81.19-.6.45-1.14.77-1.61.29-.44.66-.85 1.1-1.23.4-.34.83-.65 1.3-.91.42-.22.86-.38 1.3-.48.46-.11.92-.17 1.37-.16.45 0 .91.06 1.35.17.4.1.79.26 1.16.46.38.21.74.46 1.07.74.3.27.59.56.87.87.29.33.56.68.81 1.05.33.48.63 1.01.9 1.58.21.44.4.89.57 1.34.17.45.31.91.42 1.38.11.45.2.91.26 1.37.06.47.1.94.13 1.42.03.5.04 1 .03 1.5-.02.47-.07.94-.14 1.41-.08.48-.18.95-.3 1.41z"/>
                </svg>
                <span>Apple</span>
            </button>

        </div>

        <div class="mt-8 flex justify-center items-center">
            <p class="font-medium text-gray-700">Don't Have an Account <span class="text-[#0e7d34]"><a href="{{ route('register') }}">Sign in?</a></span></p>
        </div>

        <div class="mt-8 flex justify-center items-center">
            <p class="font-medium text-sm text-center text-gray-700">By signing in you agree to <br> our  Terms of Service, Privacy Policy and Acceptable Use Policy.</p>
        </div>






        </form>

    </div>

</div>


                {{-- login --}}
            </div>
        </div>
    </div>





@endsection
