import './bootstrap';
import 'flowbite';

// import 'jquery.easing';
import Alpine from 'alpinejs';
import persist from '@alpinejs/persist';
import 'laravel-datatables-vite';
import Chart from 'chart.js/auto';



Alpine.plugin(persist)
window.Alpine = Alpine;

Alpine.start();


// chart.js


// Alpine.js
// Initialize Alpine.js with persist plugin
Alpine.plugin(persist)
window.Alpine = Alpine;

Alpine.start();


// Example: init a chart
document.addEventListener('DOMContentLoaded', () => {
    // Transaction Chart
    const txCtx = document.getElementById('transactionChart');
    if (txCtx) {
        new Chart(txCtx, {
            type: 'line',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{
                    label: 'Transactions',
                    data: [1200, 1900, 3000, 2500],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.2)',
                    tension: 0.4,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    // User Growth Chart
    const userCtx = document.getElementById('userGrowthChart');
    if (userCtx) {
        new Chart(userCtx, {
            type: 'bar',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{
                    label: 'Users',
                    data: [300, 450, 600, 720],
                    backgroundColor: '#10b981',
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }
});







//Register step indicator
//Register step indicator

  const stepColors = ["bg-gray-300", "bg-[#0E7D34]"];
  const stepCount = 4;
  for (let i = 1; i <= stepCount; i++) {
    const step = document.getElementById(`step-${i}`);
    const line = document.getElementById(`line-${i}`);
    if (i < currentStep) {
      step.classList.add("bg-gray-300");
    } else if (i === currentStep) {
      step.classList.add("bg-[#0E7D34]");
    } else {
      step.classList.add("bg-gray-300");
    }

    if (line && i < currentStep) {
      line.classList.remove("bg-gray-300");
      line.classList.add("bg-[#0E7D34]");
    }
  }



//KYC Verification
//KYC Verification
//KYC Verification

  const governmentInput = document.getElementById('government_id');
  const addressInput = document.getElementById('address_proof');
  const progressBar = document.getElementById('progressBar');
  const statusText = document.getElementById('status');

  let governmentSelected = false;
  let addressSelected = false;

//   function tryAutoUpload() {
//     const govFile = governmentInput.files[0];
//     const addrFile = addressInput.files[0];

//     if (!govFile || !addrFile) return;

//     const formData = new FormData();
//     formData.append('means_of_verification', govFile);
//     formData.append('proof_address', addrFile);

//     const xhr = new XMLHttpRequest();
//     xhr.open('POST', '/upload-kyc');
//     xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').content);

//     xhr.upload.addEventListener('progress', function (e) {
//       if (e.lengthComputable) {
//         const percent = Math.round((e.loaded / e.total) * 100);
//         progressBar.style.width = percent + '%';
//         progressBar.innerText = percent + '%';
//       }
//     });

//     xhr.onload = function () {
//       if (xhr.status === 200) {
//         progressBar.classList.replace('bg-blue-500', 'bg-green-500');
//         progressBar.innerText = '100%';
//         statusText.classList.remove('hidden');
//         governmentInput.disabled = true;
//         addressInput.disabled = true;
//       } else {
//         alert('Upload failed: ' + xhr.responseText);
//       }
//     };

//     xhr.onerror = function () {
//       alert('Network error during upload.');
//     };

//     xhr.send(formData);
//   }

//   governmentInput.addEventListener('change', tryAutoUpload);
//   addressInput.addEventListener('change', tryAutoUpload);

function tryAutoUpload() {
  const govFile = governmentInput.files[0];
  const addrFile = addressInput.files[0];
  const meansofid = document.getElementById('meansofid').value;

  // Ensure all fields are filled
  if (!govFile || !addrFile || !meansofid) {
    alert('Please select both files and an ID type.');
    return;
  }

  const formData = new FormData();
  formData.append('meansofid', meansofid);
  formData.append('means_of_verification', govFile);
  formData.append('proof_address', addrFile);

  const xhr = new XMLHttpRequest();
  xhr.open('POST', '/upload-kyc');
  xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').content);

  xhr.upload.addEventListener('progress', function (e) {
    if (e.lengthComputable) {
      const percent = Math.round((e.loaded / e.total) * 100);
      progressBar.style.width = percent + '%';
      progressBar.innerText = percent + '%';
    }
  });

  xhr.onload = function () {
    if (xhr.status === 200) {
      progressBar.classList.replace('bg-blue-500', 'bg-green-500');
      progressBar.innerText = '100%';
      statusText.classList.remove('hidden');
      governmentInput.disabled = true;
      addressInput.disabled = true;
    } else {
      alert('Upload failed: ' + xhr.responseText);
    }
  };

  xhr.onerror = function () {
    alert('Network error during upload.');
  };

  xhr.send(formData);
}


governmentInput.addEventListener('change', tryAutoUpload);
addressInput.addEventListener('change', tryAutoUpload);




