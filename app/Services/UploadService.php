<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Intervention\Image\ImageManager;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Drivers\Gd\Driver;




class UploadService {

    /**
     * Upload file via ftp to server
     *
     * @param file $file
     * @param dir $dir
     *
     *
     * @return mexid array
     *
     * */

     public function fileUploadFTP(File $file, string $dirPath):array
     {
         if ($file->isValid()) {
             $extension = $file->extension();
             $mimeType = $file->getClientMimeType();
             $fileSize = $file->getSize();
             $filename = time().'-'.str_replace(' ', '-', $file->getClientOriginalName());

         $file_ftp =  Storage::disk('ftp')->put($dirPath.$filename, fopen($file, 'r+'));
             return [
                 'name' => $filename,
                 'mime_type' => $mimeType,
                 'path' => $dirPath.$filename,
                 'extension' => $extension,
                 'fileSize'=>$fileSize,
             ];
         }
         return [];
     }


     /**
      * Remove file from server via ftp
      * @param string $filepath the path to file in storage
      *
      * @return bool
      */
     public function removeFileFTP(string $filePath):bool
     {

         if(Storage::disk('ftp')->exists($filePath)){
             Storage::disk('ftp')->delete($filePath);

             return true;
         }

         return false;

     }

    /**
     * Upload file and crop using image intervention
     *
     * @param file $file
     * @param dir $dir
     *
     *
     * @return mexid array
     *
     * */

     public function fileUploadThumbold($file, string $dirPath):array
     {


         if ($file->isValid()) {
             $extension = $file->extension();
             $mimeType = $file->getClientMimeType();
             $filename = time().'-'.str_replace(' ', '-', $file->getClientOriginalName());

           //  $path = 'app/public/'.$dirPath .'thumb';


            $path = public_path('storage/'.$dirPath .'thumb/');

            if(!File::isDirectory($path)){
                File::makeDirectory($path, 0777, true, true);
            }
            if(!File::isDirectory($path.'500/')){
                File::makeDirectory($path.'500/', 0777, true, true);
            }
            if(!File::isDirectory($path.'800/')){
                File::makeDirectory($path.'800/', 0777, true, true);
            }

            $img = Image::make($file->path());

            // resize the thubnail and save to db
            $img->resize(500, 500, function ($constraint) {
                $constraint->aspectRatio();
            })->save($path.'500/'.$filename);
            // resize 1200 x image
            $img->resize(800, 800, function ($constraint) {
                $constraint->aspectRatio();
            })->save($path.'800/'.$filename);

            //store default image
            $file->storeAs($dirPath, $filename, 'public');


             return [
                 'name' => $filename,
                 'mime_type' => $mimeType,
                 'path' => $path,
                 'extension' => $extension,
             ];
         }

         return [];
     }


    //  public function fileUpload($file, string $dirPath):array
    //  {

    //     if ($file->isValid()) {
    //         $extension = $file->extension();
    //         $mimeType = $file->getClientMimeType();
    //         $filename = time().'-'.str_replace(' ', '-', $file->getClientOriginalName());


    //        $path = public_path('/storage/'.$dirPath);

    //        if (!file_exists($path)) {
    //         Storage::makeDirectory($dirPath);
    //           // mkdir($path, 666, true);
    //       }
    //     //store default image
    //     $file->storeAs($dirPath, $filename, 'public');


    //     return [
    //         'name' => $file->getClientOriginalName(),
    //         'filename' => $filename,
    //         'mime_type' => $mimeType,
    //         'path' => $path,
    //         'extension' => $extension,
    //     ];
    //     }

    //     return [];
    //  }

    public function fileUpload($file, string $dirPath): array
{
    if ($file->isValid()) {
        $extension = $file->extension();
        $mimeType = $file->getClientMimeType();
        $filename = time() . '-' . str_replace(' ', '-', $file->getClientOriginalName());

        // Create the storage directory if it doesn't exist
        $storagePath = storage_path('app/public/' . $dirPath);
        if (!file_exists($storagePath)) {
            mkdir($storagePath, 0755, true);
        }

        // Also ensure the public symlink directory exists
        $publicPath = public_path('storage/' . $dirPath);
        if (!file_exists($publicPath)) {
            mkdir($publicPath, 0755, true);
        }

        // Store the file
        $file->storeAs($dirPath, $filename, 'public');

        \Log::info('File uploaded successfully', [
            'filename' => $filename,
            'storage_path' => $storagePath . $filename,
            'public_path' => $publicPath . $filename,
            'exists_in_storage' => file_exists($storagePath . $filename),
            'exists_in_public' => file_exists($publicPath . $filename)
        ]);

        return [
            'name' => $file->getClientOriginalName(),
            'filename' => $filename,
            'mime_type' => $mimeType,
            'path' => $publicPath,
            'storage_path' => $storagePath,
            'extension' => $extension,
        ];
    }

    return [];
}




    public function fileUploadThumb($file, $dirPath){


        if ($file->isValid()) {

            $name =filter_var($file->getClientOriginalName(), FILTER_SANITIZE_STRING);
            $extension = $file->extension();
            $mimeType = $file->getClientMimeType();
            $filename = time().'-'.str_replace(' ', '-', $file->getClientOriginalName());

            $manager = new ImageManager(Driver::class);
            $path = public_path('/storage/'.$dirPath);
            $thumbpath = public_path('/storage/'.$dirPath.'/thumb/');
            File::ensureDirectoryExists($path);
            File::ensureDirectoryExists($thumbpath);
            if (!file_exists($path)) {
             Storage::makeDirectory($dirPath);

           }
           if (!file_exists($thumbpath)) {
            Storage::makeDirectory($dirPath.'/thumb/');
           }
         //store default image
            $file->storeAs($dirPath, $filename);

           // $imagethumsave = public_path($dirPath .'/thumb/');


            $image = $manager->read($file);
            // resize the thubnail and save to db
            // $image->resize(500, 500, function ($constraint) {
            //     $constraint->aspectRatio();
            // })->save(public_path('/storage/uploads/thumb/').$filename);

            $image = $image->cover(500, 500,'center');
            $image->save(public_path('/storage/uploads/thumb/').$filename);


            return [
               'name' => $name,
               'filename' =>$filename,
                'mime_type' => $mimeType,
                'path' => $thumbpath,
                'extension' => $extension,
            ];
        }

        return [];
    }
}
