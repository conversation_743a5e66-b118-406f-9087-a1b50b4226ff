<?php

namespace App\Notifications;

use App\Enums\TxStatus;
use Illuminate\Bus\Queueable;
use App\Models\kycVerification;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class KycStatusNotification extends Notification
{
    use Queueable;

    /**
     * The KYC verification instance.
     *
     * @var \App\Models\kycVerification
     */
    protected $kycVerification;

    /**
     * The status of the KYC verification.
     *
     * @var mixed
     */
    protected $status;

    /**
     * Create a new notification instance.
     */
    public function __construct(kycVerification $kycVerification)
    {
        //
        $this->kycVerification = $kycVerification;
        $this->status = $kycVerification->status;

    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database' , 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $message = (new MailMessage)
            ->subject('KYC Verification Status Update')
            ->line('Your KYC verification status has been updated.');

        if ($this->status == TxStatus::Pending) {
            $message->line('Your KYC verification is now pending review. We will notify you once it has been processed.');
        } elseif ($this->status == TxStatus::Completed) {
            $message->line('Congratulations! Your KYC verification has been approved.');
        } elseif ($this->status == TxStatus::Rejected) {
            $message->line('Unfortunately, your KYC verification has been rejected. Please check the details and try again.');
        }

        return $message
            ->action('View Details', url('/dashboard'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
      public function toArray(object $notifiable): array
    {
        $message = '';

        if ($this->status == TxStatus::Pending) {
            $message = 'Your KYC verification is pending review.';
        } elseif ($this->status == TxStatus::Completed) {
            $message = 'Your KYC verification has been approved!';
        } elseif ($this->status == TxStatus::Rejected) {
            $message = 'Your KYC verification has been rejected.';
        }

        return [
            'title' => 'KYC Verification Update',
            'message' => $message,
            'kyc_id' => $this->kycVerification->id,
            'status' => $this->status?->value,
            'status_name' => $this->status?->name,
        ];
    }
}
