<?php

namespace App\Notifications;

use App\Enums\TxStatus;
use Illuminate\Bus\Queueable;
use App\Models\kycVerification;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Log;

class KycStatusNotification extends Notification
{
    use Queueable;

    /**
     * The KYC verification instance.
     *
     * @var \App\Models\kycVerification
     */
    protected $kycVerification;

    /**
     * The status of the KYC verification.
     *
     * @var mixed
     */
    protected $status;

    /**
     * Create a new notification instance.
     */
    public function __construct(kycVerification $kycVerification)
    {
        $this->kycVerification = $kycVerification;

        // Log the raw status value for debugging
       Log::info('Constructing KycStatusNotification', [
        'kyc_id' => $kycVerification->id,
        'status' => $kycVerification->status,
    ]);

        // Ensure status is properly set as a TxStatus enum
        if ($kycVerification->status instanceof TxStatus) {
            $this->status = $kycVerification->status;
        } else {
            // Try to convert to enum if it's a string or integer
            try {
                $rawStatus = $kycVerification->getRawOriginal('status');
                if (is_string($rawStatus) || is_numeric($rawStatus)) {
                    $this->status = TxStatus::tryFrom($rawStatus);

                    if (!$this->status) {
                        // If tryFrom returns null, try to match by name
                        foreach (TxStatus::cases() as $case) {
                            if (strtolower($case->name) === strtolower($rawStatus)) {
                                $this->status = $case;
                                break;
                            }
                        }
                    }
                }
            } catch (\Throwable $e) {
                Log::error('Failed to convert KYC status to enum', [
                    'kyc_id' => $kycVerification->id,
                    'error' => $e->getMessage()
                ]);
            }

            // If all else fails, default to Pending
            if (!$this->status) {
                $this->status = TxStatus::Pending;
                Log::warning('Defaulting KYC status to Pending', [
                    'kyc_id' => $kycVerification->id
                ]);
            }
        }

        // Log the final status we'll use
        Log::info('Final KYC status for notification', [
            'kyc_id' => $kycVerification->id,
            'status_name' => $this->status->name,
            'status_value' => $this->status->value
        ]);
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $message = (new MailMessage)
            ->subject('KYC Verification Status Update')
            ->line('Your KYC verification status has been updated.');

        if ($this->status == TxStatus::Pending) {
            $message->line('Your KYC verification is now pending review. We will notify you once it has been processed.');
        } elseif ($this->status == TxStatus::Completed) {
            $message->line('Congratulations! Your KYC verification has been approved.');
        } elseif ($this->status == TxStatus::Rejected) {
            $message->line('Unfortunately, your KYC verification has been rejected. Please check the details and try again.');
        } else {
            $message->line('Current status: ' . $this->status->name);
        }

        return $message
            ->action('View Details', url('/dashboard'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $message = '';

        if ($this->status == TxStatus::Pending) {
            $message = 'Your KYC verification is pending review.';
        } elseif ($this->status == TxStatus::Completed) {
            $message = 'Your KYC verification has been approved!';
        } elseif ($this->status == TxStatus::Rejected) {
            $message = 'Your KYC verification has been rejected.';
        } else {
            $message = 'Your KYC verification status is now: ' . $this->status->name;
        }

        return [
            'title' => 'KYC Verification Update',
            'message' => $message,
            'kyc_id' => $this->kycVerification->id,
            'status' => $this->status->value,
            'status_name' => $this->status->name,
        ];
    }
}
