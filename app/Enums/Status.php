<?php

namespace App\Enums;

use Illuminate\Support\Facades\Auth;

enum Status: string
{
    case Active = '1';
    case Inactive = '0';

    public function color(): string
    {
        return match($this)
        {

            self::Active => 'bg-gray-500 text-gray-800 text-sm font-medium me-2 px-3 py-1 rounded dark:bg-gray-700 dark:text-gray-300',
            self::Inactive => 'bg-blue-100 text-blue-800 text-sm font-medium me-2 px-3 py-1 rounded dark:bg-blue-900 dark:text-blue-300',

        };
    }
        // Coloring the cases to use in datatables as badges
        public function icon(): string
        {
            return match($this)
            {
                self::Active => '<i class="fa-solid bg-success text-green-800  bg-green-100 dark:bg-green-900 dark:text-green-300 fa-circle-check"></i>',
                self::Inactive => '<i class="fa-solid dark:bg-red-900 bg-red-100 text-red-800 dark:text-red-300 fa-circle-xmark"></i>',



            };
        }


    public function colorTailwind(): string
    {
        return match ($this) {
            self::Active=> 'bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-ful dark:bg-green-900 dark:text-green-300',
            self::Inactive => 'bg-red-100 text-red-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-ful dark:bg-red-900 dark:text-red-300',
        };
    }





    public function badgeClass(): string
    {
        return match ($this) {
            self::Inactive => 'bg-success',
            self::Active => 'bg-warning text-dark',



        };
    }

     public function label(): string
    {
        return match ($this) {
            self::Inactive => 'Inactive',
            self::Active => 'active',

        };
    }

    public function checked(): string
    {
        return match($this)
        {

            self::Active => 'checked',
            self::Inactive => '',

        };
    }
    // for easy looping of enum values
  public static function values(): array
  {
      return array_column(self::cases(), 'name', 'value');
  }
}
