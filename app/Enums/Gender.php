<?php

namespace App\Enums;

enum Gender: string
{
    case Male = 'male';
    case Female = 'female';


      // Coloring the cases to use in datatables as badges
      public function icon(): string
      {

          return match($this)
          {
              self::Male => '<svg w-6 h-6 text-gray-800 dark:text-white class="w-5 h-5 inline" xmlns="http://www.w3.org/2000/svg"  fill="currentColor" viewBox="0 0 320 512"><path d="M112 48a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm40 304l0 128c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-223.1L59.4 304.5c-9.1 15.1-28.8 20-43.9 10.9s-20-28.8-10.9-43.9l58.3-97c17.4-28.9 48.6-46.6 82.3-46.6l29.7 0c33.7 0 64.9 17.7 82.3 46.6l58.3 97c9.1 15.1 4.2 34.8-10.9 43.9s-34.8 4.2-43.9-10.9L232 256.9 232 480c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-128-16 0z"/></svg>',
              self::Female => '<svg w-6 h-6 text-gray-800 dark:text-white class="w-5 h-5 inline" xmlns="http://www.w3.org/2000/svg"  fill="currentColor" viewBox="0 0 320 512"><path d="M160 0a48 48 0 1 1 0 96 48 48 0 1 1 0-96zM88 384l-17.8 0c-10.9 0-18.6-10.7-15.2-21.1L93.3 248.1 59.4 304.5c-9.1 15.1-28.8 20-43.9 10.9s-20-28.8-10.9-43.9l53.6-89.2c20.3-33.7 56.7-54.3 96-54.3l11.6 0c39.3 0 75.7 20.6 96 54.3l53.6 89.2c9.1 15.1 4.2 34.8-10.9 43.9s-34.8 4.2-43.9-10.9l-33.9-56.3L265 362.9c3.5 10.4-4.3 21.1-15.2 21.1L232 384l0 96c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-96-16 0 0 96c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-96z"/></svg>',


          };
      }


    public function color(): string
    {
        return match($this)
        {

            self::Male => 'badge bg-success',
            self::Female => 'badge bg-danger',


        };
    }

    public function colorTailwind(): string
    {
        return match ($this) {
            self::Male=> 'bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-ful dark:bg-green-900 dark:text-green-300',
            self::Female => 'bg-red-100 text-red-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-ful dark:bg-red-900 dark:text-red-300',
        };
    }

    public function checked(): string
    {
        return match($this)
        {

            self::Male => 'checked',
            self::Female => '',

        };
    }
    // for easy looping of enum values
  public static function values(): array
  {
      return array_column(self::cases(), 'name', 'value');
  }
}
