<?php

namespace App\Enums;

enum IsAdmin: string
{
    case No = "0";
    case Yes = "1";


    public function icon(): string
    {

        return match($this)
        {
            self::No => '<svg class=" w-5 h-5 text-gray-800 dark:text-white inline" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304l-91.4 0z"/></svg>',
            self::Yes => '<svg class=" w-5 h-5 text-gray-800 dark:text-white inline" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M96 128a128 128 0 1 0 256 0A128 128 0 1 0 96 128zm94.5 200.2l18.6 31L175.8 483.1l-36-146.9c-2-8.1-9.8-13.4-17.9-11.3C51.9 342.4 0 405.8 0 481.3c0 17 13.8 30.7 30.7 30.7l131.7 0c0 0 0 0 .1 0l5.5 0 112 0 5.5 0c0 0 0 0 .1 0l131.7 0c17 0 30.7-13.8 30.7-30.7c0-75.5-51.9-138.9-121.9-156.4c-8.1-2-15.9 3.3-17.9 11.3l-36 146.9L238.9 359.2l18.6-31c6.4-10.7-1.3-24.2-13.7-24.2L224 304l-19.7 0c-12.4 0-20.1 13.6-13.7 24.2z"/></svg>',


        };
    }

    public function color(): string
    {
        return match($this)
        {

            self::Yes => 'bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md dark:bg-gray-700 dark:text-green-400 border border-green-100 dark:border-green-500',
            self::No => 'bg-gray-200 text-gray-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-gray-300',

        };
    }


    public function checked(): string
    {
        return match($this)
        {

            self::Yes => 'checked',
            self::No => '',

        };
    }
    // for easy looping of enum values
  public static function values(): array
  {
      return array_column(self::cases(), 'name', 'value');
  }
}
