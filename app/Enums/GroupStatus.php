<?php

namespace App\Enums;

enum GroupStatus: string
{
    case Pending = 'pending';
    case Active = 'active';
    case Banned = 'banned';
    case Closed = 'Closed';
    case Suspended = 'suspended';
    case Deactivated = 'deactivated';



     // Coloring the cases to use in datatables as badges
     public function icon(): string
     {
         return match($this)
         {
            self::Pending => '<i class="fa-solid  fa-circle-xmark"></i>',
             self::Active => '<i class="fa-solid  fa-circle-check"></i>',
             self::Banned => '<i class="fa-solid fa-ban "></i>',
             self::Suspended => '<i class="fa-solid fa-user-slash "></i>',
             self::Deactivated => '<i class="fa-solid fa-user-lock"></i>',


         };
     }
     public function btcolor(): string
     {
         return match($this)
         {
            self::Pending => 'badge  badge-warning',
            self::Active => 'badge badge-success',
            self::Banned => 'badge  badge-danger ',
            self::Suspended => 'badge badge-secondary',
            self::Deactivated => 'badge badge-dark',
         };
     }


      // Coloring the cases to use in datatables as badges
      public function color(): string
      {
          return match($this)
          {
              self::Pending => 'bg-red-100 text-red-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300',
              self::Banned => 'bg-yellow-100 text-yellow-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300',
              self::Active => 'bg-green-100 text-green-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300',
              self::Suspended => 'bg-indigo-100 text-indigo-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded dark:bg-indigo-900 dark:text-indigo-300',
              self::Deactivated => 'bg-pink-100 text-pink-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded dark:bg-pink-900 dark:text-pink-300',
          };
      }

      // for easy looping of enum values
    public static function values(): array
    {
        return array_column(self::cases(), 'name', 'value');
    }
}
