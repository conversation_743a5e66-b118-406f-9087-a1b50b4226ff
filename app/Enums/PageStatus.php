<?php
namespace App\Enums;


enum PageStatus:string
{
    case Draft = 'draft';
    case Published = 'published';

    public function color(): string
    {
        return match($this)
        {

            self::Draft => 'bg-gray-200 text-gray-800 text-sm font-medium me-2 px-3 py-1 rounded dark:bg-gray-700 dark:text-gray-300',
            self::Published => 'bg-green-100 text-green-800 text-xs font-medium mr-2 px-3 py-1 rounded-md dark:bg-gray-700 dark:text-green-400 border border-green-100 dark:border-green-500',

        };
    }


    public function checked(): string
    {
        return match($this)
        {

            self::Published => 'checked',
            self::Draft => '',

        };
    }
    // for easy looping of enum values
  public static function values(): array
  {
      return array_column(self::cases(), 'name', 'value');
  }
}
