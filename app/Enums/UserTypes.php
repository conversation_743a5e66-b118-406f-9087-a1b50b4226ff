<?php

namespace App\Enums;

enum UserTypes: string
{
    case User = "user";
    case Admin = "admin";
    case Student = "student";
    case CustomerService = "Student-data";
    case Accountant = "accountant";

    // Admin privileges function
    public function hasAdminPrivileges(): bool
    {
        return match($this) {
            self::Admin => true,
            self::User => false,
            self::Student => false,
            self::CustomerService => false,
            self::Accountant => false,
        };
    }

    public function label(): string
    {
        return match($this) {
            self::Admin => 'Admin',
            self::User => 'User',
            self::Student => 'Student',
            self::CustomerService => 'Customer Service',
            self::Accountant => 'Accountant',
        };
    }

    // Your existing values function
    public static function values(): array
    {
        return array_column(self::cases(), 'name', 'value');
    }

    
}
