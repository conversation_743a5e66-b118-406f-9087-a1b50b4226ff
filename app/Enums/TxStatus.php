<?php

namespace App\Enums;

enum TxStatus: string
{
    case Pending = 'pending';
    case Active = 'active';
    case Approved = 'approved';
    case Rejected = 'rejected';
    case InReview = 'inreview';
    case Inprogress = 'inprogress';
    case Completed = 'completed';
    case Accepted = 'accepted';
    case Cancelled = 'cancelled';


    public static function values(): array
    {
        return array_column(self::cases(), 'name', 'value');
    }

    public function tailwindCss(): string
    {
        return match ($this) {
            self::Pending=> 'bg-red-200 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md dark:bg-red-900 dark:text-red-400 border border-red-200 dark:border-red-500',
            self::Active => 'bg-green-200 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md dark:bg-green-700 dark:text-green-400 border border-green-200 dark:border-green-500',
            self::Approved => 'bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md dark:bg-green-700 dark:text-green-400 border border-green-100 dark:border-green-500',
            self::Rejected => 'bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md dark:bg-red-900 dark:text-red-400 border border-red-200 dark:border-red-500',
            self::InReview => 'bg-orange-100 text-orange-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md border border-orange-100 dark:bg-gray-700 dark:border-orange-300 dark:text-orange-300',
            self::Inprogress => 'bg-purple-100 text-purple-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md border border-purple-100 dark:bg-gray-700 dark:border-purple-500 dark:text-purple-400',
            self::Completed => 'bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md dark:bg-gray-700 dark:text-green-400 border border-green-100 dark:border-green-500',
            self::Accepted => 'bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md dark:bg-gray-700 dark:text-green-400 border border-green-100 dark:border-green-500',
            self::Cancelled => 'bg-gray-300 text-gray-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md dark:bg-gray-700 dark:text-gray-400 border border-gray-100 dark:border-gray-500',

        };
    }
}
