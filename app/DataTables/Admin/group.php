<?php

namespace App\DataTables\Admin;

use App\Models\Group;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Ya<PERSON>ra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;

class GroupsDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder<Group> $query Results from query() method.
     */
    // public function dataTable(QueryBuilder $query): EloquentDataTable
    // {
    //     return (new EloquentDataTable($query))
    //         ->addColumn('action', function ($data) {

    //             $btn = " <div class='col-span relative'>

    //                         <div x-data='{ isOpen: false }'>
    //                     <button @click.prevent='isOpen = !isOpen'  class='text-gray-900 bg-white flex border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700' type='button'>Action <svg class='w-2.5 h-2.5 ms-3 mt-2' aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 10 6'>
    //                         <path stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 1 4 4 4-4'/>
    //                         </svg>
    //                         </button>

    //                     <!-- Dropdown menu -->
    //                     <div @click.outside='isOpen = false' x-show='isOpen' class='z-10 absolute  bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700'>
    //                         <ul class='py-2 text-sm text-start text-gray-700 dark:text-gray-200' aria-labelledby='ActionDropdownButton-" . $data->id . "'>

    //                            <a href='javascript:void(0)data-url='" . route('admin.groups.edit', $data->id) . "'data-id='" . $data->id . "'class='block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white open-edit-modal'>Edit</a>
    //                              <li>
    //                               <a href='" . route('admin.groups.edit', $data->id) . "' class='block underline px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>View</a>
    //                             </li>


    //                             <li>
    //                                 <form class='inline-flex' id='deleteItem-{$data->id}' action='". route('admin.groups.destroy', ['group' => $data]) . "' method='POST' x-data='{
    //                                             confirmDelete(event){
    //                                                 event.preventDefault();
    //                                                 var choice = confirm(` Are you sure you want to delete {$data->name} ? `);

    //                                                 if (choice) {
    //                                                     document.getElementById('deleteItem-{$data->id}').submit();
    //                                                 }}
    //                                             }
    //                                         '>

    //                                     <input type='hidden' name='_token' value='" . csrf_token() . "'/>
    //                                     <input type='hidden' name='_method' value='DELETE' />

    //                                     <button  @click='confirmDelete' class=' inline-flex w-full items-center delete mt-2 px-5 py-2.5 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900 '>
    //                                         <svg class='w-4 h-4' fill='currentColor' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'><path fill-rule='evenodd' d='M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z' clip-rule='evenodd'></path></svg>

    //                                         Delete
    //                                     </button>

    //                                 </form>

    //                             </li>

    //                         </ul>
    //                     </div>
    //                     </div>
    //                     </div>
    //            ";

    //             return $btn;
    //         })
            
            
    //         // ->editColumn('amount', function ($data) {
    //         //     return $data->amount . ' /' . $data->frequency;
    //         // })
    //         ->editColumn('amount', function ($data) {
    //             $symbols = \App\Models\Group::currencySymbols();
    //             $symbol = $symbols[$data->currency] ?? $data->currency;
    //             return $symbol . number_format($data->amount, 2) . ' / ' . ucfirst($data->frequency);
    //         })
    //         ->editColumn('number_of_numbers', function($data) {
    //            return ($data->members->count() ?? $data->number_of_numbers ?? 0) . ' / ' . $data->max_number_of_member;
    //        })

        
    //     ->rawColumns(['action'])
    //     ->addIndexColumn()
    //         ->setRowId('id');
    // }

    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
    return (new EloquentDataTable($query))
    ->addColumn('action', function ($data) {
        $groupName = htmlspecialchars($data->group_name, ENT_QUOTES);
        $groupRules = htmlspecialchars($data->group_rules ?? '', ENT_QUOTES);
        
        // Handle enum values
        $statusValue = $data->status instanceof \App\Enums\GroupStatus ? $data->status->value : $data->status;
        $frequencyValue = $data->frequency instanceof \App\Enums\Frequency ? $data->frequency->value : $data->frequency;
        $currencyValue = $data->currency instanceof \App\Enums\Currency ? $data->currency->value : $data->currency;
        
        $btn = "<div class='col-span relative'>
            <div x-data='{ isOpen: false, showDeleteConfirm: false }'>
                <!-- Action Buttons -->
                <ul class='py-2 text-sm text-start text-[#0E7D34] flex justify-center dark:text-gray-200' aria-labelledby='ActionDropdownButton-{$data->id}'>
                    <li>
                        <a href='#' @click.prevent='isOpen = true' class='block underline text-gray-700 px-2 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>Edit</a>
                    </li>
                    <li>
                        <a href='#' @click.prevent='showDeleteConfirm = true' class='block underline px-2 py-2 text-red-500 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>Delete</a>
                    </li>
                </ul>

                <!-- Edit Modal -->
                <div x-show='isOpen'
                     x-transition:enter='transition ease-out duration-300'
                     x-transition:enter-start='opacity-0 transform scale-90'
                     x-transition:enter-end='opacity-100 transform scale-100'
                     x-transition:leave='transition ease-in duration-200'
                     x-transition:leave-start='opacity-100 transform scale-100'
                     x-transition:leave-end='opacity-0 transform scale-90'
                     class='fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4'
                     @click.self='isOpen = false'
                     style='display: none;'>
                    <div class='bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto' @click.stop>
                        <div class='p-6'>
                            <div class='flex justify-between items-center mb-4'>
                                <h3 class='text-lg font-medium text-gray-900'>Edit Group</h3>
                                <button @click='isOpen = false' class='text-gray-400 hover:text-gray-600'>
                                    <svg class='w-6 h-6' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12'></path>
                                    </svg>
                                </button>
                            </div>

                            <form action='" . route('admin.groups.update', $data->id) . "' method='POST'>" 
                                . csrf_field() . method_field('PUT') . "
                                
                                <div class='grid grid-cols-1 md:grid-cols-2 gap-4'>
                                    <!-- Group Name -->
                                    <div class='mb-4'>
                                        <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Group Name</label>
                                        <input type='text' name='group_name' value='{$groupName}' class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34]' required>
                                    </div>

                                    <!-- Amount -->
                                    <div class='mb-4'>
                                        <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Amount</label>
                                        <input type='number' name='amount' value='{$data->amount}' min='0' step='0.01' class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34]' required>
                                    </div>

                                    <!-- Frequency -->
                                    <div class='mb-4'>
                                        <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Frequency</label>
                                        <select name='frequency' class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34]' required>";

        // Add frequency options
        foreach (\App\Enums\Frequency::cases() as $frequency) {
            $selected = $frequency->value === $frequencyValue ? 'selected' : '';
            $frequencyLabel = ucfirst(str_replace('-', ' ', $frequency->value));
            $btn .= "<option value='{$frequency->value}' {$selected}>{$frequencyLabel}</option>";
        }

        $btn .= "                    </select>
                                    </div>

                                    <!-- Max Members -->
                                    <div class='mb-4'>
                                        <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Max Members</label>
                                        <input type='number' name='max_number_of_member' value='{$data->max_number_of_member}' min='1' class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34]' required>
                                    </div>

                                    <!-- Order of Payment -->
                                    <div class='mb-4'>
                                        <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Order of Payment</label>
                                        <select name='orderOfPayment' class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34]' required>
                                            <option value='random' " . ($data->orderOfPayment === 'random' ? 'selected' : '') . ">Random</option>
                                            <option value='sequential' " . ($data->orderOfPayment === 'sequential' ? 'selected' : '') . ">Sequential</option>
                                            <option value='lottery' " . ($data->orderOfPayment === 'lottery' ? 'selected' : '') . ">Lottery</option>
                                        </select>
                                    </div>

                                    <!-- Currency -->
                                    <div class='mb-4'>
                                        <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Currency</label>
                                        <select name='currency' class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34]' required>";

        // Add currency options
        foreach (\App\Enums\Currency::cases() as $currency) {
            $selected = $currency->value === $currencyValue ? 'selected' : '';
            $currencyLabel = strtoupper($currency->value);
            $btn .= "<option value='{$currency->value}' {$selected}>{$currencyLabel}</option>";
        }

        $btn .= "                    </select>
                                    </div>

                                    <!-- Status -->
                                    <div class='mb-4'>
                                        <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Status</label>
                                        <select name='status' class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34]' required>";

        // Add status options
        foreach (\App\Enums\GroupStatus::cases() as $status) {
            $selected = $status->value === $statusValue ? 'selected' : '';
            $statusLabel = ucfirst(str_replace('-', ' ', $status->value));
            $btn .= "<option value='{$status->value}' {$selected}>{$statusLabel}</option>";
        }

        $btn .= "                    </select>
                                    </div>
                                </div>

                                <!-- Group Rules -->
                                <div class='mb-6'>
                                    <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Group Rules</label>
                                    <textarea name='group_rules' rows='4' class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0E7D34]' placeholder='Enter group rules...'>{$groupRules}</textarea>
                                </div>

                                <div class='flex justify-end space-x-3'>
                                    <button type='button' @click='isOpen = false' class='px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500'>Cancel</button>
                                    <button type='submit' class='px-4 py-2 text-sm font-medium text-white bg-[#0E7D34] rounded-md hover:bg-[#39A75E] focus:outline-none focus:ring-2 focus:ring-[#0E7D34]'>Update Group</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Delete Confirmation Modal -->
                <div x-show='showDeleteConfirm'
                     x-transition:enter='transition ease-out duration-300'
                     x-transition:enter-start='opacity-0 transform scale-90'
                     x-transition:enter-end='opacity-100 transform scale-100'
                     x-transition:leave='transition ease-in duration-200'
                     x-transition:leave-start='opacity-100 transform scale-100'
                     x-transition:leave-end='opacity-0 transform scale-90'
                     class='fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4'
                     @click.self='showDeleteConfirm = false'
                     style='display: none;'>
                    <div class='bg-white rounded-lg shadow-xl max-w-sm w-full' @click.stop>
                        <div class='p-6'>
                            <div class='flex items-center mb-4'>
                                <div class='flex-shrink-0'>
                                    <svg class='w-6 h-6 text-red-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'></path>
                                    </svg>
                                </div>
                                <div class='ml-3'>
                                    <h3 class='text-lg font-medium text-gray-900'>Delete Group</h3>
                                    <p class='text-sm text-gray-500 mt-1'>Are you sure you want to delete \"{$groupName}\"? This action cannot be undone.</p>
                                </div>
                            </div>
                            <div class='flex justify-end space-x-3'>
                                <button type='button' @click='showDeleteConfirm = false' class='px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500'>Cancel</button>
                                <form action='" . route('admin.groups.destroy', $data->id) . "' method='POST' class='inline'>" 
                                    . csrf_field() . method_field('DELETE') . "
                                    <button type='submit' class='px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500'>Delete Group</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>";

        return $btn;
    })
    ->editColumn('amount', function ($data) {
        $symbols = \App\Models\Group::currencySymbols();
        $symbol = $symbols[$data->currency] ?? $data->currency;
        return $symbol . number_format($data->amount, 2) . ' / ' . ucfirst($data->frequency);
    })
    ->editColumn('number_of_numbers', function($data) {
        return ($data->members->count() ?? $data->number_of_numbers ?? 0) . ' / ' . $data->max_number_of_member;
    })
    ->editColumn('status', function($data) {
        $statusValue = $data->status instanceof \App\Enums\GroupStatus ? $data->status->value : $data->status;
        $statusClass = match($statusValue) {
            'active' => 'bg-green-100 text-green-700',
            'pending' => 'bg-yellow-100 text-yellow-700',
            'completed' => 'bg-blue-100 text-blue-700',
            'cancelled' => 'bg-red-100 text-red-700',
            default => 'bg-gray-100 text-gray-700'
        };
        return '<span class="inline-block px-3 py-1 rounded-full text-xs font-semibold ' . $statusClass . '">' . ucfirst($statusValue) . '</span>';
    })
    ->rawColumns(['action', 'status'])
    ->addIndexColumn()
    ->setRowId('id');
    }
    




    

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<Group>
     */
    // public function query(Group $model): QueryBuilder
    // {
    //     return $model->newQuery();
    // }

   
        public function query(Group $model): QueryBuilder
        {
            return $model->newQuery()->withCount('members');
        }

    

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
                    ->setTableId('groups-table')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    ->orderBy(1)
                    ->selectStyleSingle()
                    ->buttons([
                        Button::make('excel'),
                        Button::make('csv'),
                        Button::make('pdf'),
                        Button::make('print'),
                        Button::make('reset'),
                        Button::make('reload')
                    ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            
            Column::make('id'),
            Column::make('group_name')->title('Group Name'),
            Column::make('number_of_numbers')->title('Members Count'),
            Column::make('max_number_of_member')->title('Maximum number'),
            Column::make('amount')->title('Total Contribution'),
            Column::make('last_activity_date')->title('last Activity Date'),
            Column::make('status')->title('Status'),
            Column::computed('action')
                  ->searchable(false)
                  ->orderable(false)
                  ->width(60)
            ->addClass('text-center')
     
                ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'Groups_' . date('YmdHis');
    }
}



