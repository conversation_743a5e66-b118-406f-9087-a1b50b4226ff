<?php

namespace App\DataTables\Admin;

use App\Models\Transaction;
use App\Models\UserTransaction;
use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;

class UserTransactionsDataTable extends DataTable
{
    protected $userId;

    public function __construct($userId = null)
    {
        $this->userId = $userId;
        parent::__construct();
    }

    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder<UserTransaction> $query Results from query() method.
     */
       public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('action', function ($data) {

                $btn = " <div class='col-span relative'>

                            <div x-data='{ isOpen: false }'>
                        

                        <!-- Dropdown menu -->
                            <ul class='py-2 text-sm text-start text-[#0E7D34]  dark:text-gray-200' aria-labelledby='ActionDropdownButton-" . $data->id . "'>

                                <li>
                                  <a href='" . route('admin.users.transactions', $data->user->id) . "' class='block underline px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>View</a>
                                </li>

                             

                            </ul>
                        </div>
                        </div>
               ";

                return $btn;
            })
            
            

            ->editColumn('created_at', function($data) {
                return  $data->created_at->format('d M Y');
            })
            ->editColumn('amount', function($data) {
                return '₦' . number_format($data->amount, 2);
            })
            ->editColumn('status', function($data) {
                $statusClass = match($data->status->value) {
                    'completed' => 'bg-green-100 text-green-700',
                    'pending' => 'bg-yellow-100 text-yellow-700',
                    'rejected' => 'bg-red-100 text-red-700',
                    default => 'bg-gray-100 text-gray-700'
                };
                return '<span class="inline-block px-3 py-1 rounded-full text-xs font-semibold ' . $statusClass . '">' . ucfirst($data->status->value) . '</span>';
            })
            ->editColumn('tx_type', function($data) {
                return ucfirst($data->tx_type);
            })
            ->addColumn('proof', function($data) {
                // Check if this transaction has an associated contribution with payment proof
                $contribution = \App\Models\Contribution::where('user_id', $data->user_id)
                    ->where('amount', $data->amount)
                    ->whereDate('created_at', $data->created_at->toDateString())
                    ->with('group')
                    ->first();

                if ($contribution && $contribution->payment_proof) {
                    $imageSrc = asset('storage/' . $contribution->payment_proof);
                    $uploadTime = $contribution->created_at->format('M d, Y h:i A');
                    $transactionType = ucfirst($data->tx_type);
                    $groupName = $contribution->group->group_name ?? 'N/A';

                    return '<button onclick="openImageModal(\'' . $imageSrc . '\', \'' . $uploadTime . '\', \'' . $transactionType . '\', \'' . $groupName . '\')" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        View Proof
                    </button>';
                }

                return '<span class="text-gray-400 text-xs">No proof</span>';
            })

           
            // ->editColumn('country_id', function($data) {
            //     return $data->country->name;
            // })

        
        ->rawColumns(['action', 'status', 'proof'])
        ->addIndexColumn()
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<UserTransaction>
     */
    public function query(Transaction $model): QueryBuilder
    {
        // Get both transactions and contributions for the user
        return $model->newQuery()
            ->where('user_id', $this->userId)
            ->with(['user', 'bank'])
            ->orderBy('created_at', 'desc');
    }

   
    

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
                    ->setTableId('usertransactions-table')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    ->orderBy(1)
                    ->selectStyleSingle()
                    ->buttons([
                        Button::make('excel'),
                        Button::make('csv'),
                        Button::make('pdf'),
                        Button::make('print'),
                        Button::make('reset'),
                        Button::make('reload')
                    ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [

            Column::make('id'),
            Column::make('tx_type')->title('Type'),
            Column::make('amount')->title('Amount'),
            Column::make('status')->title('Status'),
            Column::computed('proof')->title('Payment Proof')
                  ->searchable(false)
                  ->orderable(false)
                  ->width(120)
                  ->addClass('text-center'),
            Column::make('created_at')->title('Date'),

            Column::computed('action')
                  ->searchable(false)
                  ->orderable(false)
                  ->width(60)
            ->addClass('text-center')

                ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'UserTransactions_' . date('YmdHis');
    }
}
