<?php

namespace App\DataTables\Admin;

use App\Models\Transaction;
use App\Models\UserTransaction;
use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;

class UserTransactionsDataTable extends DataTable
{
    protected $userId;

    public function __construct($userId = null)
    {
        $this->userId = $userId;
        parent::__construct();
    }

    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder<UserTransaction> $query Results from query() method.
     */
       public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('action', function ($data) {

                $btn = " <div class='col-span relative'>

                            <div x-data='{ isOpen: false }'>
                        

                        <!-- Dropdown menu -->
                            <ul class='py-2 text-sm text-start text-[#0E7D34]  dark:text-gray-200' aria-labelledby='ActionDropdownButton-" . $data->id . "'>

                                <li>
                                  <a href='" . route('admin.users.transactions', $data->user->id) . "' class='block underline px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>View</a>
                                </li>

                             

                            </ul>
                        </div>
                        </div>
               ";

                return $btn;
            })
            
            

            ->editColumn('created_at', function($data) {
                return  $data->created_at->format('d M Y');
            })
            ->editColumn('amount', function($data) {
                return '₦' . number_format($data->amount, 2);
            })
            ->editColumn('status', function($data) {
                $statusClass = match($data->status->value) {
                    'completed' => 'bg-green-100 text-green-700',
                    'pending' => 'bg-yellow-100 text-yellow-700',
                    'rejected' => 'bg-red-100 text-red-700',
                    default => 'bg-gray-100 text-gray-700'
                };
                return '<span class="inline-block px-3 py-1 rounded-full text-xs font-semibold ' . $statusClass . '">' . ucfirst($data->status->value) . '</span>';
            })
            ->editColumn('tx_type', function($data) {
                return ucfirst($data->tx_type);
            })

           
            // ->editColumn('country_id', function($data) {
            //     return $data->country->name;
            // })

        
        ->rawColumns(['action', 'status'])
        ->addIndexColumn()
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<UserTransaction>
     */
    public function query(Transaction $model): QueryBuilder
    {
        return $model->newQuery()->where('user_id', $this->userId)->with(['user', 'bank'])->orderBy('created_at', 'desc');
    }

   
    

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
                    ->setTableId('usertransactions-table')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    ->orderBy(1)
                    ->selectStyleSingle()
                    ->buttons([
                        Button::make('excel'),
                        Button::make('csv'),
                        Button::make('pdf'),
                        Button::make('print'),
                        Button::make('reset'),
                        Button::make('reload')
                    ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            
            Column::make('id'),
            Column::make('tx_type')->title('Type'),
            Column::make('amount')->title('Amount'),
            Column::make('status')->title('Status'),
            Column::make('created_at')->title('Date'),

            Column::computed('action')
                  ->searchable(false)
                  ->orderable(false)
                  ->width(60)
            ->addClass('text-center')
     
                ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'UserTransactions_' . date('YmdHis');
    }
}
