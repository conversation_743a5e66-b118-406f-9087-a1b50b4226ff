<?php

namespace App\DataTables\Admin;

use App\Enums\TxStatus;
use App\Models\FailedRequest;
use App\Models\PayoutRequest;
use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;

class FailedRequestsDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder<PayoutRequest> $query Results from query() method.
     */
      public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('action', function ($data) {

                $btn = " <div class='col-span relative'>
<div class='flex gap-3 items-center'>
    " . ($data->status->value === 'pending' ? "
        <!-- Approve Button -->
        <form method='POST' action='" . route('admin.transactions.payout-requests.approve', $data->id) . "' class='inline'>
            " . csrf_field() . "
            <button type='submit'
                    class='px-6 py-3 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-300 transition-colors duration-200'
                    onclick='return confirm(\"Are you sure you want to approve this payout request?\")'>
                Approve
            </button>
        </form>
        
        <!-- Reject Button -->
        <form method='POST' action='" . route('admin.transactions.payout-requests.reject', $data->id) . "' class='inline'>
            " . csrf_field() . "
            <button type='submit'
                    class='px-6 py-3 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-300 transition-colors duration-200'
                    onclick='return confirm(\"Are you sure you want to reject this payout request?\")'>
                Reject
            </button>
        </form>
    " : ($data->status->value === 'rejected' ? "
        <!-- Rejected Status -->
        <span class='px-4 py-2 text-sm font-medium text-red-600 bg-red-100 rounded-lg'>
            Rejected
        </span>
        
        <!-- Retry Button -->
        <form method='POST' action='" . route('admin.transactions.payout-requests.retry', $data->id) . "' class='inline'>
            " . csrf_field() . "
            <button type='submit'
                    class='px-6 py-3 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-300 transition-colors duration-200'
                    onclick='return confirm(\"Are you sure you want to retry this payout request?\")'>
                Retry
            </button>
        </form>
    " : "
        <!-- Other Status -->
        <span class='px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg'>
            " . ucfirst($data->status->value) . "
        </span>
    ")) . "
</div>




                </div>
               ";

                return $btn;
            })
            
            ->editColumn('group_id', function($data) {
                return $data->group?->group_name ;
            })

            // ->editColumn('created_at', function($data) {
            //     return  $data->group->created_at->format('d M Y');
            // })

            
            ->editColumn('user_id', function($data) {
                return $data->user->first_name . ' ' . $data->user->last_name;
            })
             ->editColumn('number_of_numbers', function($data) {
               return ($data->members->count() ?? $data->number_of_numbers ?? 0) . ' / ' . $data->group->max_number_of_member;
           })

           ->editColumn('payout_amount', function ($data) {
                $symbols = \App\Models\PayoutRequest::currencySymbols();
                $symbol = $symbols[$data->currency] ?? $data->currency;
                return $symbol . number_format($data->payout_amount, 2);
            })

        
        ->rawColumns(['action', 'status'])
        ->addIndexColumn()
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<PayoutRequest>
     */
 public function query(PayoutRequest $model)
{
    return $model->newQuery()
        ->where('status', TxStatus::Rejected->value)->with(['user', 'group', 'members'])->select('payout_requests.*');
}



    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
                    ->setTableId('failedrequests-table')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    ->orderBy(1)
                    ->selectStyleSingle()
                    ->buttons([
                        Button::make('excel'),
                        Button::make('csv'),
                        Button::make('pdf'),
                        Button::make('print'),
                        Button::make('reset'),
                        Button::make('reload')
                    ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            
            Column::make('id'),
            Column::make('group_id')->title('Group Name'),
            Column::make('number_of_numbers')->title('Members Count'),
            Column::make('payout_amount')->title('Total Contribution'),
            Column::make('status')->title('Status'),
            Column::make('user_id')->title('Members Name'),
            Column::computed('action')
                  ->searchable(false)
                  ->orderable(false)
                  ->width(60)
            ->addClass('text-center')
     
                ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'FailedRequests_' . date('YmdHis');
    }
}
