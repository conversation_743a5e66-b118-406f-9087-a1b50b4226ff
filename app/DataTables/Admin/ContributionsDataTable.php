<?php

namespace App\DataTables\Admin;

use App\Models\Contribution;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Ya<PERSON>ra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;

class ContributionsDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder<Contribution> $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('action', function ($data) {

                $btn = " <div class='col-span relative'>

                            <div x-data='{ isOpen: false }'>
                        

                        <!-- Dropdown menu -->
                            <ul class='py-2 text-sm text-start text-[#0E7D34]  dark:text-gray-200' aria-labelledby='ActionDropdownButton-" . $data->id . "'>

                                <li>
                                  <a href='" . route('admin.users.groups.show', ['user' => $data->user->id, 'group' => $data->group->id]) . "' class='block underline px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>View</a>
                                </li>

                             

                            </ul>
                        </div>
                        </div>
               ";

                return $btn;
            })
            
             ->editColumn('user_id', function($data) {
                return $data->user?->first_name . ' ' . $data->user?->last_name;
            })
            ->editColumn('group_id', function($data) {
                return $data->group?->group_name ;
            })

            ->editColumn('created_at', function($data) {
                return  $data->created_at->format('d M Y');
            })

            ->editColumn('status', function($data) {
                $statusValue = $data->status instanceof \App\Enums\TxStatus ? $data->status->value : $data->status;
                $statusClass = match($statusValue) {
                    'active' => 'bg-green-100 text-green-700',
                    'pending' => 'bg-yellow-100 text-yellow-700',
                    'banned' => 'bg-red-100 text-red-700',
                    'suspended' => 'bg-orange-100 text-orange-700',
                    'deactivated' => 'bg-gray-100 text-gray-700',
                    default => 'bg-gray-100 text-gray-700'
                };
                return '<span class="inline-block px-3 py-1 rounded-full text-xs font-semibold ' . $statusClass . '">' . ucfirst($statusValue) . '</span>';
            })
            // ->editColumn('country_id', function($data) {
            //     return $data->country->name;
            // })

        
        ->rawColumns(['action', 'status'])
        ->addIndexColumn()
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<Contribution>
     */
    public function query(Contribution $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
                    ->setTableId('contributions-table')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    ->orderBy(1)
                    ->selectStyleSingle()
                    ->buttons([
                        Button::make('excel'),
                        Button::make('csv'),
                        Button::make('pdf'),
                        Button::make('print'),
                        Button::make('reset'),
                        Button::make('reload')
                    ]);
    }

    /**
     * Get the dataTable columns definition.
     */
 public function getColumns(): array
    {
        return [
            
            Column::make('id'),
            Column::make('user_id')->title('User'),
            Column::make('amount')->title('Amount'),
            Column::make('status')->title('Status'),
            Column::make('group_id')->title('Group Name'),
            Column::make('created_at')->title('Date'),
            Column::computed('action')
                  ->searchable(false)
                  ->orderable(false)
                  ->width(60)
            ->addClass('text-center')
     
                ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'Contributions_' . date('YmdHis');
    }
}
