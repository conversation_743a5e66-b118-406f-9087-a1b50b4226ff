<?php

namespace App\DataTables\Admin;

use App\Enums\Status;
use App\Models\Country;
use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;

class CountriesDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder<Country> $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))

    ->addColumn('action', function ($data) {
        $isActive = $data->status === Status::Active;
        $confirmText = $isActive ? 'deactivate' : 'activate';
        $toggleRoute = route('admin.location.toggle-country', $data->id);
        $statusText = $isActive ? 'Active' : 'Inactive';

        $btn = "
        <div class='flex items-center justify-center'>
            <form action='{$toggleRoute}' method='POST' class='inline'>
                <input type='hidden' name='_token' value='" . csrf_token() . "'/>
                <input type='hidden' name='_method' value='PATCH'/>
                <label class='inline-flex items-center cursor-pointer'>
                    <input type='checkbox' " . ($isActive ? 'checked' : '') . " class='sr-only peer' 
                        onchange=\"if(confirm('Are you sure you want to {$confirmText} this country?')) { 
                            this.form.submit(); 
                        } else { 
                            this.checked = !this.checked; 
                        }\">
                    <div class='relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[\"\"] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 dark:peer-checked:bg-blue-600'></div>
                    <span class='ms-3 text-sm font-medium text-gray-900 dark:text-gray-300'>{$statusText}</span>
                </label>
            </form>
        </div>";

        return $btn;
    })
    ->editColumn('status', function ($data) {
        $isActive = $data->status === Status::Active;
        $statusClass = $isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
        $statusText = $isActive ? 'Active' : 'Inactive';
        
        return "<span class='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {$statusClass}'>
                    <div class='w-2 h-2 rounded-full " . ($isActive ? 'bg-green-500' : 'bg-red-500') . " mr-1'></div>
                    {$statusText}
                </span>";
    })

                ->rawColumns(['action', 'status'])
                ->addIndexColumn()
                ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<Country>
     */
    public function query(Country $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('countries-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->orderBy(1)
            ->selectStyleSingle()
            ->buttons([
                Button::make('excel'),
                Button::make('csv'),
                Button::make('pdf'),
                Button::make('print'),
                Button::make('reset'),
                Button::make('reload')
            ])
            ->parameters([
                'responsive' => true,
                'autoWidth' => false,
                'lengthMenu' => [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'All']],
                'pageLength' => 25,
            ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->title('ID')->width(60),
            Column::make('name')->title('Name'),
            Column::make('code')->title('Code'),
            Column::computed('status')->title('Status')->width(120)->addClass('text-center'),
            Column::computed('action')
                ->exportable(false)
                ->printable(false)
                ->width(150)
                ->addClass('text-center')
                ->title('Toggle Status')
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'Countries_' . date('YmdHis');
    }
}
