<?php

namespace App\DataTables\Admin;

use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class LocationsDataTable extends DataTable
{
    protected $locationType;

    public function __construct($locationType = 'countries')
    {
        $this->locationType = $locationType;
    }

    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('action', function ($data) {
                $toggleRoute = $this->getToggleRoute($data);
                $statusText = $data->status == '1' ? 'Turn Off' : 'Turn On';
                $statusClass = $data->status == '1' ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600';
                $confirmText = $data->status == '1' ? 'deactivate' : 'activate';

                $btn = "
                <div class='flex space-x-2'>
                    <form action='{$toggleRoute}' method='POST' class='inline'>
                        <input type='hidden' name='_token' value='" . csrf_token() . "'/>
                        <input type='hidden' name='_method' value='PATCH'/>
                        <button type='submit' 
                                class='{$statusClass} text-white px-3 py-1 rounded text-xs font-medium transition-colors duration-200'
                                onclick=\"return confirm('Are you sure you want to {$confirmText} this " . $this->getLocationTypeSingular() . "?')\">
                            {$statusText}
                        </button>
                    </form>
                </div>";

                return $btn;
            })
            ->editColumn('status', function ($data) {
                $statusClass = $data->status == '1' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                $statusText = $data->status == '1' ? 'Active' : 'Inactive';
                
                return "<span class='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {$statusClass}'>
                            {$statusText}
                        </span>";
            })
            ->addColumn('parent_location', function ($data) {
                return $this->getParentLocation($data);
            })
            ->rawColumns(['action', 'status'])
            ->addIndexColumn()
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(): QueryBuilder
    {
        switch ($this->locationType) {
            case 'states':
                return State::with('country')->newQuery();
            case 'cities':
                return City::with(['state', 'state.country'])->newQuery();
            default:
                return Country::newQuery();
        }
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('locations-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->orderBy(1)
            ->selectStyleSingle()
            ->buttons([
                Button::make('excel'),
                Button::make('csv'),
                Button::make('pdf'),
                Button::make('print'),
                Button::make('reset'),
                Button::make('reload')
            ])
            ->parameters([
                'responsive' => true,
                'autoWidth' => false,
                'lengthMenu' => [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'All']],
                'pageLength' => 25,
            ]);
    }

    /**
     * Get the dataTable columns definition.
     */
     public function getColumns(): array
    {
        return [
            
            Column::make('id'),
            Column::make('countries')->title('Country'),
            Column::make('states')->title('State'),
            Column::make('cities')->title('City'),
            Column::computed('action')
                  ->searchable(false)
                  ->orderable(false)
                  ->width(60)
            ->addClass('text-center')
     
                ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return ucfirst($this->locationType) . '_' . date('YmdHis');
    }

    /**
     * Get toggle route based on location type
     */
    private function getToggleRoute($data): string
    {
        switch ($this->locationType) {
            case 'states':
                return route('admin.location.toggle-state', $data->id);
            case 'cities':
                return route('admin.location.toggle-city', $data->id);
            default:
                return route('admin.location.toggle-country', $data->id);
        }
    }

    /**
     * Get parent location information
     */
    private function getParentLocation($data): string
    {
        switch ($this->locationType) {
            case 'states':
                return $data->country->name ?? 'N/A';
            case 'cities':
                $state = $data->state->name ?? 'N/A';
                $country = $data->state->country->name ?? 'N/A';
                return "{$state} / {$country}";
            default:
                return '';
        }
    }

    /**
     * Get location type singular form
     */
    private function getLocationTypeSingular(): string
    {
        switch ($this->locationType) {
            case 'states':
                return 'state';
            case 'cities':
                return 'city';
            default:
                return 'country';
        }
    }
}
