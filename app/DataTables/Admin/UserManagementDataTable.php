<?php

namespace App\DataTables\Admin;

use App\Models\User;
use App\Models\UserManagement;
use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;

class UserManagementDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder<UserManagement> $query Results from query() method.
     */
     public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('action', function ($data) {

                $btn = " <div class='col-span relative'>

                            <div x-data='{ isOpen: false }'>
                        <button @click.prevent='isOpen = !isOpen'  class='text-gray-900 bg-white flex border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700' type='button'>Action <svg class='w-2.5 h-2.5 ms-3 mt-2' aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 10 6'>
                            <path stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 1 4 4 4-4'/>
                            </svg>
                            </button>

                        <!-- Dropdown menu -->
                        <div @click.outside='isOpen = false' x-show='isOpen' class='z-10 absolute  bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700'>
                            <ul class='py-2 text-sm text-start text-gray-700 dark:text-gray-200' aria-labelledby='ActionDropdownButton-" . $data->id . "'>

                                <li>
                                    <a href='" . route('admin.users.show', $data->id) . "' class='block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>View</a>
                                </li>

                                <li>
                                    <form class='inline-flex' id='deleteItem-{$data->id}' action='". route('admin.groups.destroy', ['group' => $data]) . "' method='POST' x-data='{
                                                confirmDelete(event){
                                                    event.preventDefault();
                                                    var choice = confirm(` Are you sure you want to delete {$data->name} ? `);

                                                    if (choice) {
                                                        document.getElementById('deleteItem-{$data->id}').submit();
                                                    }}
                                                }
                                            '>

                                        <input type='hidden' name='_token' value='" . csrf_token() . "'/>
                                        <input type='hidden' name='_method' value='DELETE' />

                                        <button  @click='confirmDelete' class=' inline-flex w-full items-center delete mt-2 px-5 py-2.5 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900 '>
                                            <svg class='w-4 h-4' fill='currentColor' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'><path fill-rule='evenodd' d='M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z' clip-rule='evenodd'></path></svg>

                                            Delete
                                        </button>

                                    </form>

                                </li>

                            </ul>
                        </div>
                        </div>
                        </div>
               ";

                return $btn;
            })
            
            
            ->editColumn('first_name', function($data) {
               return ($data->first_name) . ' ' . $data->last_name;
           })
            ->editColumn('group_members', function($data) {
                return $data->groups ? $data->groups->count() : 0;
            })
            ->editColumn('country_id', function($data) {
                return $data->country->name;
            })

        
        ->rawColumns(['action'])
        ->addIndexColumn()
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<UserManagement>
     */
    public function query(User $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
                    ->setTableId('usermanagement-table')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    ->orderBy(1)
                    ->selectStyleSingle()
                    ->buttons([
                        Button::make('excel'),
                        Button::make('csv'),
                        Button::make('pdf'),
                        Button::make('print'),
                        Button::make('reset'),
                        Button::make('reload')
                    ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            
            Column::make('id'),
            Column::make('first_name')->title('Name'),
            Column::make('email')->title('Email'),
            Column::make('group_members')->title('Groups'),
            Column::make('country_id')->title('Country'),
            Column::make('status')->title('Status'),
            Column::computed('action')
                  ->searchable(false)
                  ->orderable(false)
                  ->width(60)
            ->addClass('text-center')
     
                ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'UserManagement_' . date('YmdHis');
    }
}
