<?php

namespace App\DataTables\Admin;

use App\Models\User;
use App\Models\UserManagement;
use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;

class UserManagementDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder<UserManagement> $query Results from query() method.
     */
     public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
          ->addColumn('action', function ($data) {
    $userName = htmlspecialchars($data->name, ENT_QUOTES);
    
    $btn = "<div class='col-span relative'>
        <div x-data='{ isOpen: false, showDeleteConfirm: false, showSuspendConfirm: false }'>
            <!-- Dropdown Button -->
            <button @click.prevent='isOpen = !isOpen' 
                    class='text-gray-900 bg-white flex border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700' 
                    type='button'>
                Action 
                <svg class='w-2.5 h-2.5 ms-3 mt-2' aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 10 6'>
                    <path stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 1 4 4 4-4'/>
                </svg>
            </button>

            <!-- Dropdown menu -->
            <div @click.outside='isOpen = false' 
                 x-show='isOpen' 
                 class='z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700'
                 style='display: none;'>
                <ul class='py-2 text-sm text-start text-gray-700 dark:text-gray-200' aria-labelledby='ActionDropdownButton-{$data->id}'>
                    <li>
                        <a href='" . route('admin.users.show', $data->id) . "' 
                           class='block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>
                            <svg class='w-4 h-4 inline mr-2' fill='currentColor' viewBox='0 0 20 20'>
                                <path d='M10 12a2 2 0 100-4 2 2 0 000 4z'></path>
                                <path fill-rule='evenodd' d='M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z' clip-rule='evenodd'></path>
                            </svg>
                            View
                        </a>
                    </li>
                    <li>
                        <a href='#' 
                           @click.prevent='showSuspendConfirm = true; isOpen = false' 
                           class='block px-4 py-2 text-orange-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>
                            <svg class='w-4 h-4 inline mr-2' fill='currentColor' viewBox='0 0 20 20'>
                                <path fill-rule='evenodd' d='M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z' clip-rule='evenodd'></path>
                            </svg>
                            Suspend
                        </a>
                    </li>
                    <li>
                        <a href='#' 
                           @click.prevent='showDeleteConfirm = true; isOpen = false' 
                           class='block px-4 py-2 text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>
                            <svg class='w-4 h-4 inline mr-2' fill='currentColor' viewBox='0 0 20 20'>
                                <path fill-rule='evenodd' d='M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z' clip-rule='evenodd'></path>
                            </svg>
                            Delete
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Suspend Confirmation Modal -->
            <div x-show='showSuspendConfirm'
                 x-transition:enter='transition ease-out duration-300'
                 x-transition:enter-start='opacity-0 transform scale-90'
                 x-transition:enter-end='opacity-100 transform scale-100'
                 x-transition:leave='transition ease-in duration-200'
                 x-transition:leave-start='opacity-100 transform scale-100'
                 x-transition:leave-end='opacity-0 transform scale-90'
                 class='fixed inset-0 z-50 overflow-y-auto backdrop-blur-sm bg-white/10 flex items-center justify-center p-4'
                 @click.self='showSuspendConfirm = false'
                 style='display: none;'>
                <div class='bg-white rounded-lg shadow-xl max-w-sm w-full' @click.stop>
                    <div class='p-6'>
                        <div class='flex items-center mb-4'>
                            <div class='flex-shrink-0'>
                                <svg class='w-6 h-6 text-orange-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                    <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'></path>
                                </svg>
                            </div>
                            <div class='ml-3'>
                                <h3 class='text-lg font-medium text-gray-900'>Suspend User</h3>
                                <p class='text-sm text-gray-500 mt-1'>Are you sure you want to suspend \"{$userName}\"?</p>
                            </div>
                        </div>
                        <div class='flex justify-end space-x-3'>
                            <button type='button' @click='showSuspendConfirm = false' class='px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500'>Cancel</button>
                            <form action='" . route('admin.users.suspend', $data->id) . "' method='POST' class='inline'>" 
                                . csrf_field() . method_field('PATCH') . "
                                <button type='submit' class='px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500'>Suspend User</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Delete Confirmation Modal -->
            <div x-show='showDeleteConfirm'
                 x-transition:enter='transition ease-out duration-300'
                 x-transition:enter-start='opacity-0 transform scale-90'
                 x-transition:enter-end='opacity-100 transform scale-100'
                 x-transition:leave='transition ease-in duration-200'
                 x-transition:leave-start='opacity-100 transform scale-100'
                 x-transition:leave-end='opacity-0 transform scale-90'
                 class='fixed inset-0 z-50 overflow-y-auto backdrop-blur-sm bg-white/10 flex items-center justify-center p-4'
                 @click.self='showDeleteConfirm = false'
                 style='display: none;'>
                <div class='bg-white rounded-lg shadow-xl max-w-sm w-full' @click.stop>
                    <div class='p-6'>
                        <div class='flex items-center mb-4'>
                            <div class='flex-shrink-0'>
                                <svg class='w-6 h-6 text-red-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                    <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'></path>
                                </svg>
                            </div>
                            <div class='ml-3'>
                                <h3 class='text-lg font-medium text-gray-900'>Delete User</h3>
                                <p class='text-sm text-gray-500 mt-1'>Are you sure you want to delete \"{$userName}\"? This action cannot be undone.</p>
                            </div>
                        </div>
                        <div class='flex justify-end space-x-3'>
                            <button type='button' @click='showDeleteConfirm = false' class='px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500'>Cancel</button>
                            <form action='" . route('admin.users.destroy', $data->id) . "' method='POST' class='inline'>" 
                                . csrf_field() . method_field('DELETE') . "
                                <button type='submit' class='px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500'>Delete User</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>";
    
    return $btn;
})

            
            
            ->editColumn('first_name', function($data) {
               return ($data->first_name) . ' ' . $data->last_name;
           })
            ->editColumn('group_members', function($data) {
                return $data->groups ? $data->groups->count() : 0;
            })
            ->editColumn('country_id', function($data) {
                return $data->country->name;
            })

        
        ->rawColumns(['action'])
        ->addIndexColumn()
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<UserManagement>
     */
    public function query(User $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
                    ->setTableId('usermanagement-table')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    ->orderBy(1)
                    ->selectStyleSingle()
                    ->buttons([
                        Button::make('excel'),
                        Button::make('csv'),
                        Button::make('pdf'),
                        Button::make('print'),
                        Button::make('reset'),
                        Button::make('reload')
                    ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            
            Column::make('id'),
            Column::make('first_name')->title('Name'),
            Column::make('email')->title('Email'),
            Column::make('group_members')->title('Groups'),
            Column::make('country_id')->title('Country'),
            Column::make('status')->title('Status'),
            Column::computed('action')
                  ->searchable(false)
                  ->orderable(false)
                  ->width(60)
            ->addClass('text-center')
     
                ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'UserManagement_' . date('YmdHis');
    }
}
