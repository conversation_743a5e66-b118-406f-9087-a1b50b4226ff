<?php

namespace App\DataTables\Admin;

use App\Models\Task;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Ya<PERSON>ra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;

class TaskDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder<Task> $query Results from query() method.
     */
    // public function dataTable(QueryBuilder $query): EloquentDataTable
    // {
    //     return (new EloquentDataTable($query))
    //         ->addColumn('action', function ($data) {

    //               $btn = " <div class='col-span relative'>

    //                         <div x-data='{ isOpen: false }'>
                        

    //                     <!-- Dropdown menu -->
    //                         <ul class='py-2 text-sm text-start text-[#0E7D34] flex justify-center  dark:text-gray-200' aria-labelledby='ActionDropdownButton-" . $data->id . "'>

    //                             <li>
    //                               <a href='" . route('admin.tasks.edit', $data->id) . "' class='block underline text-gray-700 px-2 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>Edit</a>
    //                             </li>


    //                             <li>
    //                               <a href='" . route('admin.tasks.destroy', $data->id) . "' class='block underline px-2 py-2 text-red-500 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>Delete</a>
    //                             </li>
                                


    //                         </ul>
    //                     </div>
    //                     </div>
    //            ";

    //             return $btn;
    //         })
            
           

    //         ->editColumn('created_at', function($data) {
    //             return  $data->created_at->format('d M Y');
    //         })

    //         ->editColumn('status', function($data) {
    //             $statusValue = $data->status instanceof \App\Enums\TxStatus ? $data->status->value : $data->status;
    //             $statusClass = match($statusValue) {
    //                 'active' => 'bg-green-100 text-green-700',
    //                 'pending' => 'bg-yellow-100 text-yellow-700',
    //                 'banned' => 'bg-red-100 text-red-700',
    //                 'suspended' => 'bg-orange-100 text-orange-700',
    //                 'deactivated' => 'bg-gray-100 text-gray-700',
    //                 default => 'bg-gray-100 text-gray-700'
    //             };
    //             return '<span class="inline-block px-3 py-1 rounded-full text-xs font-semibold ' . $statusClass . '">' . ucfirst($statusValue) . '</span>';
    //         })
    //         // ->editColumn('country_id', function($data) {
    //         //     return $data->country->name;
    //         // })

        
    //     ->rawColumns(['action', 'status'])
    //     ->addIndexColumn()
    //         ->setRowId('id');
    // }

public function dataTable(QueryBuilder $query): EloquentDataTable
{
    return (new EloquentDataTable($query))
        ->addColumn('action', function ($data) {
            $taskName = htmlspecialchars($data->task_name, ENT_QUOTES);
            $taskDescription = htmlspecialchars($data->task_description, ENT_QUOTES);
            
            // Fix: Convert enum to string value
            $statusValue = $data->status instanceof \App\Enums\TxStatus ? $data->status->value : $data->status;
            $taskStatus = htmlspecialchars($statusValue, ENT_QUOTES);
            
            $btn = "
            <div class='col-span relative'>
                <div x-data='{ isOpen: false, showDeleteConfirm: false }'>
                    <!-- Action Buttons -->
                    <ul class='py-2 text-sm text-start text-[#0E7D34] flex justify-center dark:text-gray-200' aria-labelledby='ActionDropdownButton-{$data->id}'>
                        <li>
                            <a href='#' 
                               @click.prevent='isOpen = true'
                               class='block underline text-gray-700 px-2 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>
                               Edit
                            </a>
                        </li>
                        <li>
                            <a href='#' 
                               @click.prevent='showDeleteConfirm = true'
                               class='block underline px-2 py-2 text-red-500 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>
                                Delete
                            </a>
                        </li>
                    </ul>
                    
                    <!-- Edit Modal -->
                    <div x-show='isOpen' 
                         x-transition:enter='transition ease-out duration-300'
                         x-transition:enter-start='opacity-0 transform scale-90'
                         x-transition:enter-end='opacity-100 transform scale-100'
                         x-transition:leave='transition ease-in duration-200'
                         x-transition:leave-start='opacity-100 transform scale-100'
                         x-transition:leave-end='opacity-0 transform scale-90'
                         class='fixed inset-0 z-50 overflow-y-auto backdrop-blur-xs bg-white/10 flex items-center justify-center p-4'
                         @click.self='isOpen = false'
                         style='display: none;'>
                        
                        <div class='bg-white rounded-lg shadow-xl max-w-md w-full' @click.stop>
                            <div class='p-6'>
                                <div class='flex justify-between items-center mb-4'>
                                    <h3 class='text-lg font-medium text-gray-900'>Edit Task</h3>
                                    <button @click='isOpen = false' class='text-gray-400 hover:text-gray-600'>
                                        <svg class='w-6 h-6' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12'></path>
                                        </svg>
                                    </button>
                                </div>
                                
                                <form action='" . route('admin.tasks.update', $data->id) . "' method='POST'>
                                    " . csrf_field() . "
                                    " . method_field('PUT') . "
                                    
                                    <div class='mb-4'>
                                        <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Task Name</label>
                                        <input type='text' 
                                               name='task_name' 
                                               value='{$taskName}' 
                                               class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500' 
                                               required>
                                    </div>
                                    
                                    <div class='mb-4'>
                                        <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Task Description</label>
                                        <textarea name='task_description' 
                                                  rows='3' 
                                                  class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500' 
                                                  required>{$taskDescription}</textarea>
                                    </div>
                                    
                                    <div class='mb-4'>
                                        <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Task Points</label>
                                        <input type='number' 
                                               name='task_points' 
                                               value='{$data->task_points}' 
                                               min='1' 
                                               class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500' 
                                               required>
                                    </div>
                                    
                                    <div class='mb-6'>
                                        <label class='block text-sm font-medium text-start text-gray-700 mb-2'>Status</label>
                                        <select name='status' class='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500' required>";
            
            // Add status options with proper enum handling
            foreach (\App\Enums\TxStatus::cases() as $status) {
                $selected = $status->value === $statusValue ? 'selected' : '';
                $statusLabel = ucfirst(str_replace('-', ' ', $status->value));
                $btn .= "<option value='{$status->value}' {$selected}>{$statusLabel}</option>";
            }
            
            $btn .= "
                                        </select>
                                    </div>
                                    
                                    <div class='flex justify-end space-x-3'>
                                        <button type='button' 
                                                @click='isOpen = false' 
                                                class='px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500'>
                                            Cancel
                                        </button>
                                        <button type='submit' 
                                                class='px-4 py-2 text-sm font-medium text-white bg-[#0E7D34] rounded-md hover:bg-[#0E7D34] focus:outline-none focus:ring-2 focus:ring-blue-500'>
                                            Update Task
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delete Confirmation Modal -->
                    <div x-show='showDeleteConfirm' 
                         x-transition:enter='transition ease-out duration-300'
                         x-transition:enter-start='opacity-0 transform scale-90'
                         x-transition:enter-end='opacity-100 transform scale-100'
                         x-transition:leave='transition ease-in duration-200'
                         x-transition:leave-start='opacity-100 transform scale-100'
                         x-transition:leave-end='opacity-0 transform scale-90'
                         class='fixed inset-0 z-50 overflow-y-auto backdrop-blur-xs bg-white/10 flex items-center justify-center p-4'
                         @click.self='showDeleteConfirm = false'
                         style='display: none;'>
                        
                        <div class='bg-white rounded-lg shadow-xl max-w-sm w-full' @click.stop>
                            <div class='p-6'>
                                <div class='flex items-center mb-4'>
                                    <div class='flex-shrink-0'>
                                        <svg class='w-6 h-6 text-red-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'></path>
                                        </svg>
                                    </div>
                                    <div class='ml-3'>
                                        <h3 class='text-lg font-medium text-gray-900'>Delete Task</h3>
                                        <p class='text-sm text-gray-500 mt-1'>Are you sure you want to delete \"{$taskName}\"? This action cannot be undone.</p>
                                    </div>
                                </div>
                                
                                <div class='flex justify-end space-x-3'>
                                    <button type='button' 
                                            @click='showDeleteConfirm = false' 
                                            class='px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500'>
                                        Cancel
                                    </button>
                                    <form action='" . route('admin.tasks.destroy', $data->id) . "' method='POST' class='inline'>
                                        " . csrf_field() . "
                                        " . method_field('DELETE') . "
                                        <button type='submit' 
                                                class='px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500'>
                                            Delete Task
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>";
            
            return $btn;
        })
        ->editColumn('created_at', function($data) {
            return $data->created_at->format('d M Y');
        })
        ->editColumn('status', function($data) {
            $statusValue = $data->status instanceof \App\Enums\TxStatus ? $data->status->value : $data->status;
            $statusClass = match($statusValue) {
                'active' => 'bg-green-100 text-green-700',
                'pending' => 'bg-yellow-100 text-yellow-700',
                'banned' => 'bg-red-100 text-red-700',
                'suspended' => 'bg-orange-100 text-orange-700',
                'deactivated' => 'bg-gray-100 text-gray-700',
                default => 'bg-gray-100 text-gray-700'
            };
            return '<span class="inline-block px-3 py-1 rounded-full text-xs font-semibold ' . $statusClass . '">' . ucfirst($statusValue) . '</span>';
        })
        ->rawColumns(['action', 'status'])
        ->addIndexColumn()
        ->setRowId('id');
}




    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<Task>
     */
    public function query(Task $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
                    ->setTableId('task-table')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    ->orderBy(1)
                    ->selectStyleSingle()
                    ->buttons([
                        Button::make('excel'),
                        Button::make('csv'),
                        Button::make('pdf'),
                        Button::make('print'),
                        Button::make('reset'),
                        Button::make('reload')
                    ]);
    }

    /**
     * Get the dataTable columns definition.
     */
      public function getColumns(): array
    {
        return [
            
            Column::make('id'),
            Column::make('task_name')->title('Task Name'),
            Column::make('task_description')->title('Description'),
            Column::make('task_points')->title('Point'),
            Column::make('status')->title('Status'),
            Column::make('created_at')->title('Date Added'),
            Column::computed('action')
                  ->searchable(false)
                  ->orderable(false)
                  ->width(60)
            ->addClass('text-center')
     
                ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'Task_' . date('YmdHis');
    }
}
