<?php

namespace App\DataTables\Admin;

use App\Models\User;
use App\Models\Group;
use App\Models\UserGroup;
use App\Models\GroupMember;
use Ya<PERSON>ra\DataTables\Html\Button;
use Ya<PERSON>ra\DataTables\Html\Column;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;

class UserGroupsDataTable extends DataTable
{

     protected $userId;

    public function __construct($userId = null)
    {
        $this->userId = $userId;
        parent::__construct();
    }
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder<UserGroup> $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('action', function ($data) {

                $btn = " <div class='col-span relative'>

                            <div x-data='{ isOpen: false }'>
                        

                        <!-- Dropdown menu -->
                            <ul class='py-2 text-sm text-start text-[#0E7D34]  dark:text-gray-200' aria-labelledby='ActionDropdownButton-" . $data->id . "'>

                                <li>
                                  <a href='" . route('admin.users.groups.show', ['user' => $data->user->id, 'group' => $data->group->id]) . "' class='block underline px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white'>View</a>
                                </li>

                             

                            </ul>
                        </div>
                        </div>
               ";

                return $btn;
            })
            
            ->editColumn('group_id', function($data) {
                return $data->group?->group_name ;
            })

            ->editColumn('created_at', function($data) {
                return  $data->group->created_at->format('d M Y');
            })

            ->editColumn('group_members', function($data) {
                return $data->groups ? $data->groups->count() : 0;
            })
            // ->editColumn('country_id', function($data) {
            //     return $data->country->name;
            // })

        
        ->rawColumns(['action'])
        ->addIndexColumn()
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<UserGroup>
     */
    public function query(GroupMember $model): QueryBuilder
    {
        return $model->newQuery()->where('user_id', $this->userId)->with('user')->orderBy('created_at', 'desc');
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
                    ->setTableId('usergroups-table')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    ->orderBy(1)
                    ->selectStyleSingle()
                    ->buttons([
                        Button::make('excel'),
                        Button::make('csv'),
                        Button::make('pdf'),
                        Button::make('print'),
                        Button::make('reset'),
                        Button::make('reload')
                    ]);
    }

    /**
     * Get the dataTable columns definition.
     */
   public function getColumns(): array
    {
        return [
            
            Column::make('id'),
            Column::make('group_id')->title('Group Name'),
            Column::make('created_at')->title('Date Joined'),
            Column::make('status')->title('Status'),
            Column::computed('action')
                  ->searchable(false)
                  ->orderable(false)
                  ->width(60)
            ->addClass('text-center')
     
                ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'UserGroups_' . date('YmdHis');
    }
}
