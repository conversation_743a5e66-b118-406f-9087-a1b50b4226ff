<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TrackUserActivityMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
      public function handle(Request $request, Closure $next): Response
   {
        // If user is authenticated, update last activity timestamp
        if (Auth::check()) {
            // Update the last activity timestamp
            session(['last_activity' => time()]);

            // Check if session has been inactive for too long (2 minutes)
            $inactiveTime = config('session.inactive_timeout', 2); // Default 2 minutes
            $lastActivity = session('last_activity', 0);

            if (time() - $lastActivity > $inactiveTime * 60) {
                // Session has been inactive for too long, log the user out
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();

                return redirect()->route('login')
                    ->with('message', 'Your session has expired due to inactivity. Please log in again.');
            }
        }

        return $next($request);
    }
}
