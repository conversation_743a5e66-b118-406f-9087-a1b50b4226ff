<?php

namespace App\Http\Middleware;

use Closure;
use App\Enums\IsAdmin;
use App\Enums\UserTypes;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */

    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check() && Auth::user()->user_type == UserTypes::Admin->value) {
            return $next($request);
        }
        return redirect('/')->with('error', 'Unauthorized access.');
    }
}
