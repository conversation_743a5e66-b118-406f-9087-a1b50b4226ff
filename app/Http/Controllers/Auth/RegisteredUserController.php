<?php

namespace App\Http\Controllers\Auth;

use App\Models\User;
use App\Enums\Gender;
use App\Enums\Status;

use App\Models\Country;
use App\Enums\UserTypes;
use App\Mail\WelcomeMail;
use Illuminate\View\View;
use App\Models\Verifytoken;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use App\Models\kycVerification;
use Illuminate\Validation\Rules;
use App\Models\IdentificationType;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Storage;
use App\Notifications\KycStatusNotification;


class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create()
    {

        $countries = Country::all();
        return view('auth.register', compact( 'countries'));
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    // public function store(Request $request)
    // {

    //     $request->validate([
    //         'first_name' => ['required', 'string', 'max:255'],
    //         'last_name' => ['required', 'string', 'max:255'],
    //         'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
    //         'country_id'=>['required', 'integer', 'exists:countries,id'],
    //         'password' => ['required', 'confirmed', Rules\Password::defaults()],
    //         // 'terms_conditions' => ['required'],
    //     ]);


    //     $user = User::create([
    //         'first_name' => $request->first_name,
    //         'last_name' => $request->last_name,
    //         'country_id' => $request->country_id,
    //         'email' => $request->email,
    //         'password' => Hash::make($request->password),
    //     ]);

    //     $validToken = str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
    //     $get_token = new Verifytoken();
    //     $get_token->token = $validToken;

    //     $get_token->email = $request->email;
    //     $get_token->save();

    //     $get_user_email = $request->email;
    //     $get_user_name = $request->first_name;
    //     Mail::to($request->email)->send(new WelcomeMail($get_user_email, $get_user_name, $validToken));

    //     event(new Registered($user));

    //     Auth::login($user);
    //     return redirect()->route('verification.notice');
    // }
    public function store(Request $request)
{
    $request->validate([
        'first_name' => ['required', 'string', 'max:255'],
        'last_name' => ['required', 'string', 'max:255'],
        'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
        'country_id'=>['required', 'integer', 'exists:countries,id'],
        'password' => ['required', 'confirmed', Rules\Password::defaults()],
        // 'terms_conditions' => ['required'],
    ]);

    $user = User::create([
        'first_name' => $request->first_name,
        'last_name' => $request->last_name,
        'country_id' => $request->country_id,
        'email' => $request->email,
        'password' => Hash::make($request->password),
    ]);

    $validToken = str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);

    // Get expiration time from config or use default (15 minutes)
    $expirationMinutes =  (int) config('auth.verification_token_expire', 5);

    $get_token = new Verifytoken();
    $get_token->token = $validToken;
    $get_token->email = $request->email;
    $get_token->expires_at = now()->addMinutes($expirationMinutes);
    $get_token->is_activated = 0; // Explicitly set as not activated
    $get_token->save();

    $get_user_email = $request->email;
    $get_user_name = $request->first_name;

    Mail::to($request->email)->send(new WelcomeMail($get_user_email, $get_user_name, $validToken));

    event(new Registered($user));

    Auth::login($user);

    return redirect()->route('verification.notice');
}


    public function registerInfo(Request $request):View
    {

        $pageTitle = 'Register';
        return view('auth.register-info', compact('pageTitle'));
    }

    public function storeInfo(Request $request)
    {
        $request->validate([
            'gender' => [new Enum(Gender::class)],
            'address' => ['required', 'string', 'max:255'],
            'phone'=> ['required', 'numeric' ],
            'data_of_birth' => ['required', 'date'],
        ]);

        $user = Auth::user();
        // $kyc = new kycVerification();

        $user->gender = $request->gender;
        $user->address = $request->address;
        $user->phone = $request->phone;
        $user->data_of_birth = $request->data_of_birth;
        $user->save();

        // $user->notify(new KycStatusNotification($kyc));

        return redirect()->route('kyc.verification');

    }

     public function KycVericiation(Request $request)
    {

        $data['pageTitle'] = 'Kyc Verification';
        $identification = IdentificationType::where('status', Status::Active->value)->get();
        $data['identification'] = $identification;
        return view('auth.kyc_verification', $data);
    }

public function kycUpload(Request $request)
{


    $request->validate([
        'meansofid' => ['required', 'integer', 'exists:identification_types,id'],
        'means_of_verification' => 'required|max:3072|mimes:jpg,png,jpeg,pdf', // 3MB = 3072KB
        'proof_address' => 'required|max:3072|mimes:jpg,png,jpeg,pdf',
    ]);

    try {
        $governmentIdPath = null;
        $proofOfAddressPath = null;

        // Handle government ID upload
        if ($request->hasFile('means_of_verification')) {
            $governmentIdPath = $this->uploadFile($request->file('means_of_verification'), 'kyc/means_of_verification');
        }

        // Handle proof of address upload
        if ($request->hasFile('proof_address')) {
            $proofOfAddressPath = $this->uploadFile($request->file('proof_address'), 'kyc/proof_address');
        }

        // Get current user
        $user = Auth::user();

        // Save to database
        $upload = kycVerification::updateOrCreate([
            'user_id' => Auth::user()->id,
        ], [
            'identification_type_id' => $request->meansofid,
            'means_of_verification' => $governmentIdPath,
            'proof_of_address' => $proofOfAddressPath,
        ]);




        if ($upload) {
            return response()->json([
                'success' => true,
                'message' => 'KYC documents uploaded successfully',
                'redirect' => route('kyc.success')
            ]);
        }


        return response()->json([
            'success' => false,
            'message' => 'Could not save KYC documents'
        ], 422);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Upload failed: ' . $e->getMessage()
        ], 500);
    }
}





// public function uploadKyc(Request $request){
//       $request->validate([
//         'means_of_verification' => 'required|file',
//         'proof_address' => 'required|file',
//     ]);

//     $request->file('means_of_verification')->store('kyc');
//     $request->file('proof_address')->store('kyc');

//     return response()->json(['message' => 'Upload successful'], 200);
// }

// public function uploadKyc(Request $request)
// {
//     $request->validate([
//         'means_of_verification' => 'required|file',
//         'proof_address' => 'required|file',
//     ]);

//     if (!$request->hasFile('means_of_verification') || !$request->hasFile('proof_address')) {
//         return response()->json(['message' => 'Files not received'], 400);
//     }

//     $gov = $request->file('means_of_verification');
//     $addr = $request->file('proof_address');

//     // dd($gov, $addr);

//     $govPath = $gov->store('kyc', 'public');
//     $addrPath = $addr->store('kyc', 'public');



//     return response()->json([
//         'message' => 'Upload successful',
//         'paths' => [
//             'means_of_verification' => $govPath,
//             'proof_address' => $addrPath,
//         ]
//     ], 200);
// }



// public function uploadKyc(Request $request)
// {
//     // 1. Validate inputs
//     $request->validate([
//         'meansofid' => ['required', 'integer', 'exists:identification_types,id'],
//         'means_of_verification' => 'required|file',
//         'proof_address' => 'required|file',
//     ]);



//     // 3. Store files
//     $govPath = $request->file('means_of_verification')->store('kyc', 'public');
//     $addrPath = $request->file('proof_address')->store('kyc', 'public');

//     // 4. Save to DB
//    try {
//     KycVerification::create([
//         'user_id' => Auth::id(),
//         'identification_type_id' => $request->meansofid,
//         'means_of_verification' => $govPath,
//         'proof_address' => $addrPath,
//     ]);
// } catch (\Exception $e) {
//     return response()->json(['error' => $e->getMessage()], 500);
// }


//     // 5. Respond
//     return response()->json([
//         'message' => 'Upload successful',
//         'paths' => [
//             'means_of_verification' => $govPath,
//             'proof_address' => $addrPath,
//         ]
//     ], 200);
// }


public function uploadKyc(Request $request)
{
    $request->validate([
        'meansofid' => ['required', 'integer', 'exists:identification_types,id'],
        'means_of_verification' => 'required|file',
        'proof_address' => 'required|file',
    ]);

    $user = Auth::user();

    // Store files and get paths and original names
    $govFile = $request->file('means_of_verification');
    $addrFile = $request->file('proof_address');

    $govPath = $govFile->store('kyc', 'public');
    $addrPath = $addrFile->store('kyc', 'public');

    // Sanitize and keep original filenames
    $govOriginalName = Str::slug(pathinfo($govFile->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $govFile->getClientOriginalExtension();
    $addrOriginalName = Str::slug(pathinfo($addrFile->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $addrFile->getClientOriginalExtension();

    $govPath = $govFile->storeAs('kyc', $govOriginalName, 'public');
    $addrPath = $addrFile->storeAs('kyc', $addrOriginalName, 'public');


    try {
        $kyc = KycVerification::create([
            'user_id' => Auth::id(),
            'identification_type_id' => $request->meansofid,
            'means_of_verification' => $govPath,
            'proof_address' => $addrPath,
            // Optional: store original file names too
            'gov_file_name' => $govOriginalName,
           'addr_file_name' => $addrOriginalName,
        ]);
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()], 500);
    }
        $user->notify(new KycStatusNotification($kyc));

    return response()->json([
        'message' => 'Upload successful',
        'paths' => [
            'means_of_verification' => [
                'path' => $govPath,
                'original_name' => $govOriginalName,
            ],
            'proof_address' => [
                'path' => $addrPath,
                'original_name' => $addrOriginalName,
            ],
        ]
    ], 200);
}






private function uploadFile($file, $directory)
{
    $dirPath = "public/uploads/{$directory}/";
    $filename = time() . '-' . str_replace(' ', '-', $file->getClientOriginalName());

    $path = public_path('/storage/' . $dirPath);
    if (!file_exists($path)) {
        Storage::makeDirectory($dirPath);
    }

    $file->storeAs($dirPath, $filename);
    return $filename;
}


    public function SelfieVericiation(Request $request)
    {

        $data['pageTitle'] = 'Selfie Verification';
        return view('auth.selfie_verification', $data);
    }

    public function Success(Request $request)
    {
        $data['pageTitle'] = 'Success';
        return view('auth.success', $data);
    }
}
