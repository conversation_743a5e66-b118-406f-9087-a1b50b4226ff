<?php

namespace App\Http\Controllers\Auth;

use Carbon\Carbon;
use App\Mail\WelcomeMail;
use Illuminate\View\View;
use App\Models\Verifytoken;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Password;

class EmailVerificationPromptController extends Controller
{
    /**
     * Display the email verification prompt.
     */
    public function __invoke(Request $request):View
    {

        return  view('auth.verify-email');
    }

    public function __invokeverifyForgetpassword(Request $request):View
    {
        return  view('auth.verify-forgetpassword');
    }

    //     public function verifyOtp(Request $request)
    // {
    //     $request->validate([
    //         'otp' => 'required|digits:6',
    //         'email' => 'required|email' // This should be a hidden field in your form
    //     ]);

    //     $token = Verifytoken::where('email', $request->email)
    //                         ->where('token', $request->otp)
    //                         ->first();

    //     if (!$token || $token->created_at->addMinutes(30) < now()) {
    //         return back()->withErrors(['otp' => 'Invalid or expired OTP']);
    //     }

    //     // OTP is valid - proceed to password reset
    //     $token->delete(); // Remove used OTP

    //     // Create password reset token (if using Laravel's built-in)
    //     $status = Password::sendResetLink(
    //         ['email' => $request->email]
    //     );

    //     // Or redirect directly to password reset form
    //     return redirect()->route('password.reset', [
    //         'token' => Password::createToken(User::where('email', $request->email)->first()),
    //         'email' => $request->email
    //     ]);
    // }


//     public function forgetpasswordActivation(Request $request)
//     {

//          // Combine OTP digits
//         $get_token = $request->input('token', []);
//         $enteredToken = implode('', $get_token);

//         $get_token = $request->token;
//         $token = Verifytoken::where('token', $enteredToken)->first();

//           $user = User::where('email', $token->email)->first();
//         if ($get_token) {
//             $token->is_activated = 1;
//             $token->save();
//             $user->email_verified_at = now();
//             $user->is_activated = 1;
//             $user->save();
//             // Delete the token

//     // Log the user in
//             // return redirect()->route('password.reset', ['token' => $token])
//             return redirect()->route('password.reset', ['token' => $token->token, 'email' => $token->email]);

// ;

//     }


// }

public function forgetpasswordActivation(Request $request)
{
    // Combine OTP digits
    $get_token = $request->input('token', []);
    $enteredToken = implode('', $get_token);

    $token = Verifytoken::where('token', $enteredToken)->first();
    if (!$token) {
        return back()->withErrors(['token' => 'Invalid verification code']);
    }

    if ($token->created_at->diffInMinutes(Carbon::now()) > 1) {
        return back()->withErrors(['token' => 'OTP has expired. Please request a new one.']);
    }

    $user = User::where('email', $token->email)->first();
    if ($user) {
        $token->is_activated = 1;
        $token->save();
        $user->email_verified_at = now();
        $user->is_activated = 1;
        $user->save();

        // Generate a proper password reset token
        $resetToken = Password::createToken($user);

        // The token is automatically saved to the password_reset_tokens table
        // by the createToken method, so no additional saving is needed

        return redirect()->route('password.reset', [
            'token' => $resetToken,
            'email' => $token->email
        ]);
    }

    return back()->withErrors(['email' => 'User not found']);
}


    public function userActivation(Request $request)
        {
            // Combine OTP digits
            $get_token = $request->input('token', []);
            $enteredToken = implode('', $get_token);

            $get_token = $request->token;
            $token = Verifytoken::where('token', $enteredToken)->first();

            if (!$token) {
                return redirect()->route('verification.notice')->with('error', 'Invalid token provided.');
            }

            // Check if token is expired
            if ($this->isTokenExpired($token)) {
                return redirect()->route('verification.notice')->with('error', 'Token has expired. Please request a new one.');
            }

            $user = User::where('email', $token->email)->first();

            if ($get_token && $user) {
                $token->is_activated = 1;
                $token->save();
                $user->email_verified_at = now();
                $user->is_activated = 1;
                $user->save();

                // Delete the token
                $token->delete();

                // Log the user in
                Auth::login($user);
                return redirect()->route('register.info');
            } else {
                return redirect()->route('verification.notice');
            }
        }


 /**
     * Resend OTP token
     */
    public function resendOtp(Request $request)
{
    $request->validate([
        'email' => 'required|email|exists:users,email'
    ]);

    $email = $request->email;
    $user = User::where('email', $email)->first();

    if (!$user) {
        return response()->json([
            'success' => false,
            'message' => 'User not found.'
        ], 404);
    }

    if ($user->email_verified_at) {
        return response()->json([
            'success' => false,
            'message' => 'Email is already verified.'
        ], 400);
    }

    // Delete existing tokens for this email
    Verifytoken::where('email', $email)->delete();

    // Generate new OTP token
    $newToken = $this->generateOtpToken();

    // Get expiration time from config or use default (5 minutes)
    $expirationMinutes = (int) config('auth.verification_token_expire', 5);

    // Create new verification token
    Verifytoken::create([
        'email' => $email,
        'token' => $newToken,
        'is_activated' => 0,
        'created_at' => now(),
        'expires_at' => now()->addMinutes($expirationMinutes)
    ]);

    // Send verification email
    try {
        Mail::to($user->email)->send(new WelcomeMail($user->email, $user->first_name, $newToken));

        Log::info('OTP email sent successfully', [
            'email' => $user->email,
            'token' => $newToken
        ]);

    } catch (\Exception $e) {
        Log::error('Failed to send OTP email: ' . $e->getMessage());

        return response()->json([
            'success' => false,
            'message' => 'Failed to send verification email. Please try again.'
        ], 500);
    }

    return response()->json([
        'success' => true,
        'message' => 'OTP sent successfully.'
    ], 200);

}


    /**
     * Check if token is expired
     */
    private function isTokenExpired($token)
    {
        if (!$token->expires_at) {
            // If no expiration date is set, consider tokens older than 3 minutes as expired
            return $token->created_at->addMinutes(1)->isPast();
        }

        return Carbon::parse($token->expires_at)->isPast();
    }

    /**
     * Generate OTP token
     */
    private function generateOtpToken()
    {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Send verification email
     */
    // private function sendVerificationEmail($user, $token)
    // {
    //     // You'll need to create a mailable class for this
    //     // For now, this is a placeholder - replace with your actual email sending logic
    //     Mail::send('emails.verification', [
    //         'user' => $user,
    //         'token' => $token
    //     ], function ($message) use ($user) {
    //         $message->to($user->email)
    //                 ->subject('Email Verification Code');
    //     });
    // }

    /**
     * Clean up expired tokens
     */
    public function cleanupExpiredTokens()
    {
        // Get expiration time from config or use default (15 minutes)
        $expirationMinutes = (int) config('auth.verification_token_expire', 5);

        $expiredTokens = Verifytoken::where('expires_at', '<', now())
            ->orWhere(function ($query) {
                $query->whereNull('expires_at')
                      ->where('created_at', '<', now()->subMinutes(5));
            });

        $deletedCount = $expiredTokens->count();
        $expiredTokens->delete();

        return response()->json([
            'success' => true,
            'message' => "Cleaned up {$deletedCount} expired tokens."
        ]);
    }




}

