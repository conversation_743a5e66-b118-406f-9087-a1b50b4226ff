<?php

namespace App\Http\Controllers\Auth;

use App\Models\User;
use App\Mail\WelcomeMail;
use Illuminate\View\View;
use App\Models\Verifytoken;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Password;

class PasswordResetLinkController extends Controller
{
    /**
     * Display the password reset link request view.
     */
    public function create(): View
    {
        $data['pageTitle']="Forgot Password";
        return view('auth.forgot-password');
    }

    /**
     * Handle an incoming password reset link request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    // public function store(Request $request): RedirectResponse
    // {
    //     $request->validate([
    //         'email' => ['required', 'email'],
    //     ]);

    //     // We will send the password reset link to this user. Once we have attempted
    //     // to send the link, we will examine the response then see the message we
    //     // need to show to the user. Finally, we'll send out a proper response.
    //     $status = Password::sendResetLink(
    //         $request->only('email')
    //     );

    //     return $status == Password::RESET_LINK_SENT
    //                 ? back()->with('status', __($status))
    //                 : back()->withInput($request->only('email'))
    //                         ->withErrors(['email' => __($status)]);
    // }


   public function forgetStore(Request $request): RedirectResponse
{
    $request->validate([
        'email' => ['required', 'email', 'exists:users,email'],
    ]);

    // Generate unique OTP
    do {
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    } while (Verifytoken::where('token', $otp)->exists());

    // Store or update the OTP
    Verifytoken::updateOrCreate(
        ['email' => $request->email],
        ['token' => $otp, 'created_at' => now()]
    );



    try {
        $user = User::where('email', $request->email)->first();

        Mail::to($request->email)->send(new WelcomeMail(
            $request->email,
            $user->first_name ?? '',
            $otp
        ));

                // dd($request->all());


        // Redirect to OTP verification page with the email
        return redirect()->route('forgetpassword.verification')
                        ->with(['email' => $request->email]);
        //   return redirect()->route('verification.notice')
        //                 ->with(['email' => $request->email]);



    } catch (\Exception $e) {
        return back()->withInput()
                    ->withErrors(['email' => 'Failed to send OTP. Please try again.']);
    }
}



}
