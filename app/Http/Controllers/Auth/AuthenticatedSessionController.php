<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        // dd(ok);
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    // {
    //     $request->authenticate();

    //     $request->session()->regenerate();

    //     return redirect()->intended(auth()->user()->getRedirectRoute());
    // }
        {
                $request->authenticate();

                // Regenerate the session to prevent session fixation attacks
                $request->session()->regenerate();

                // If "remember me" is checked, extend the session lifetime
                if ($request->boolean('remember')) {
                    // Even with "remember me", we'll keep the session short (10 minutes)
                    config(['session.lifetime' => 10]);
                } else {
                    // For regular sessions, use the very short 2-minute timeout
                    config(['session.lifetime' => 2]);
                }

                // Store last activity timestamp in the session
                session(['last_activity' => time()]);

                return redirect()->intended(auth()->user()->getRedirectRoute());
            }


    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
