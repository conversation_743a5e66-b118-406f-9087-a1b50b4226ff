<?php

namespace App\Http\Controllers\Admin;

use Auth;
use App\Models\City;
use App\Models\User;
use App\Models\Group;
use App\Models\State;
use App\Models\Country;
use App\Enums\UserTypes;
use App\Enums\GroupStatus;
use App\Models\GroupMember;
use App\Models\Transaction;
use App\Models\Contribution;
use App\Models\GroupMessage;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\DataTables\Admin\UserGroupsDataTable;
use App\DataTables\Admin\UserManagementDataTable;
use App\DataTables\Admin\UserTransactionsDataTable;

class UserManagementController extends Controller
{
    //

    public function viewUsers(UserManagementDataTable $dataTable)
    {
        // This method will return the DataTable view for users
        return $dataTable->render('admin.users.index');
    }

    public function showUser($id)
    {
        $countries = Country::all();
        $states = State::all();
        $cities = City::all();
        $user = User::findOrFail($id);
        return view('admin.users.show', compact('user', 'countries', 'states', 'cities'));
    }

    public function viewUserGroups($id, UserGroupsDataTable $dataTable)
    {
        
        $countries = Country::all();
        $states = State::all();
        $cities = City::all();
        $user = User::findOrFail($id);
        $groups = $user->groups;

        return $dataTable->render('admin.users.user-mgt.groups', compact('user', 'groups', 'countries', 'states', 'cities'));
    }

    public function viewUserTransactions($id)
    {
        $countries = Country::all();
        $states = State::all();
        $cities = City::all();

        // Get the specific user by ID, not the authenticated user
        $user = User::findOrFail($id);
        $groups = $user->groups;

        // Get user's transactions
        $transactions = Transaction::where('user_id', $user->id)->with(['user', 'bank'])->orderBy('created_at', 'desc')->get();

        // Get user's contributions
        $contributions = Contribution::where('user_id', $user->id)
            ->with(['user', 'group'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Create DataTable instance with user ID
        $dataTable = new UserTransactionsDataTable($id);

        return $dataTable->render('admin.users.user-mgt.transactions', compact('user', 'groups', 'countries', 'states', 'cities', 'transactions', 'contributions'));
    }

   public function viewActiveGroup($user, $group)
    {
        $user = Auth::user();
        // $group = $user->groups()->where('group_id', $group)->first();
        $group = Group::where('id', $group)->first();
        // $groupMembers = GroupMember::where('group_id', $group->id)->with('user')->get();
        // $groupMembers = GroupMember::all();
       $messages = GroupMessage::forGroup($group->id)->with('user')->orderBy('created_at', 'asc')->limit(50)->get();
       $contributions = Contribution::where('group_id', $group->id)->with('user')->orderBy('created_at', 'desc')->get();

       // Separate pending requests from active members
       $pendingRequests = GroupMember::where('group_id', $group->id)
           ->where('status', \App\Enums\GroupStatus::Pending->value)->orderBy('created_at', 'asc')
           ->with('user')
           ->get();

       $groupMembers = GroupMember::where('group_id', $group->id)
    ->where('status', 'active')
    ->whereHas('user', fn($q) => $q->where('user_type', 'user')) // only users
    ->with('user')
    ->get();

$admin = GroupMember::where('group_id', $group->id)
    ->where('status', 'active')
    ->whereHas('user', fn($q) => $q->where('user_type', 'admin'))
    ->with('user')
    ->first();

    $payoutLists = GroupMember::where('group_id', $group->id)
    ->where('status', 'active')
    ->with('user')
    ->orderBy('created_at', 'asc')
    ->get();





        return view('admin.users.user-mgt.active-group', compact('user', 'group','groupMembers', 'messages', 'contributions', 'pendingRequests', 'admin', 'payoutLists'));
    }

 

 public function sendMessage(Request $request, $user, $group)
{
    try {
        $request->validate([
            'message' => 'required|string|max:1000'
        ]);

        $message = GroupMessage::create([
            'group_id' => $group,
            'user_id' => Auth::id(),
            'message' => $request->message,
            'type' => 'chat'
        ]);

        return response()->json([
            'success' => true,
            'message' => $message->load('user')
        ]);
        
    } catch (\Exception $e) {
        \Log::error('Message send error: ' . $e->getMessage());
        
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
}

public function getMessages($user, $group)
{
    try {
        $messages = GroupMessage::where('group_id', $group)
                               ->with('user')
                               ->orderBy('created_at', 'asc')
                               ->limit(50)
                               ->get();

        return response()->json($messages);
        
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
}


  

   


    public function requestToJoinGroup($groupId)
{
    $user = Auth::user();

    $exists = GroupMember::where('group_id', $groupId)
        ->where('user_id', $user->id)
        ->first();

    if (!$exists) {
        GroupMember::create([
            'user_id' => $user->id,
            'group_id' => $groupId,
            'status' => 'pending',
        ]);
    }

    return back()->with('success', 'Request sent successfully');
}

// Approve member
    public function approve($id)
    {
        $member = GroupMember::findOrFail($id);
        $member->status = 'active';
        $member->save();

        return back()->with('success', 'User approved.');
    }

// Decline member
public function decline($id)
{
    $member = GroupMember::findOrFail($id);

    // Option 1: Soft delete or remove
    $member->delete();

    // Option 2: Mark as declined (optional)
    // $member->status = 'declined';
    // $member->save();

    return back()->with('success', 'Request declined.');
}




}



