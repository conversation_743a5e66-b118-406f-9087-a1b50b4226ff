<?php

namespace App\Http\Controllers\Admin;

use App\Models\Task;
use App\Models\Reward;
use App\Enums\TxStatus;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Http\Controllers\Controller;
use App\DataTables\Admin\TaskDataTable;
use App\DataTables\Admin\RewardsDataTable;

class RewardsController extends Controller
{
    //

    public function viewRewards( TaskDataTable $dataTable)
    {
        return $dataTable->render('admin.rewards.index');
    }
    

    public function storeTasks(Request $request)
    {
        $request->validate([
            'task_name' => 'required|string|max:255',
            'task_description' => 'required|string',
            'task_points' => 'required|integer|min:1',
            'status' => ['required', Rule::in(TxStatus::values())],
        ]);

        Task::create([
            'task_name' => $request->task_name,
            'task_description' => $request->task_description,
            'task_points' => $request->task_points,
            'status' => $request->status,
        ]);

        return redirect()->back()->with('success', 'Task created successfully');
    }
}
