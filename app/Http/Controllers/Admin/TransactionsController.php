<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\ContributionsDataTable;
use App\DataTables\Admin\FailedRequestsDataTable;
use App\DataTables\Admin\TransactionsDataTable;
use App\DataTables\Admin\PayoutRequestsDataTable;
use App\Http\Controllers\Controller;
use App\Models\PayoutRequest;
use Illuminate\Http\Request;

class TransactionsController extends Controller
{
    //

    public function viewContributions(ContributionsDataTable $dataTable)
    {
        return $dataTable->render('admin.transactions.contributions');
    }


    public function viewPayouts( PayoutRequestsDataTable $dataTable)
    {
        return $dataTable->render('admin.transactions.payout-request');
    }

    public function viewFailedPayouts(FailedRequestsDataTable $dataTable)
    {
        return $dataTable->render('admin.transactions.failed-request');
    }
    
}
