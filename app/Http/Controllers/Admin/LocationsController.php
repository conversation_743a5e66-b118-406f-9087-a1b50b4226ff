<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\City;
use App\Enums\Status;
use App\Models\State;
use App\Enums\IsAdmin;
use App\Models\Country;
use Illuminate\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rules\Enum;
use Yajra\DataTables\Facades\DataTables;
use App\DataTables\Admin\CitiesDataTable;
use App\DataTables\Admin\StatesDataTable;
use Illuminate\Support\Facades\Validator;
use App\DataTables\Admin\CountriesDataTable;
use App\DataTables\Admin\LocationsDataTable;


class LocationsController extends Controller
{

    /**
     * Display country table
     * @param Request $request
     *
     * @return mixed View result
     */

      public function viewCountries(CountriesDataTable $dataTable)
      {
        $data['user'] = Auth::user();
         $data['countries'] = Country::all();
        $data['states'] = State::all();
        $data['cities'] = City::all();
        return $dataTable->render('admin.locations.countries', $data);
      }

      public function viewStates(StatesDataTable $dataTable)
      {
        $data['user'] = Auth::user();
         $data['countries'] = Country::all();
        $data['states'] = State::all();
        $data['cities'] = City::all();
        return $dataTable->render('admin.locations.states', $data);
      }

      public function viewCities(CitiesDataTable $dataTable)
      {
        $data['user'] = Auth::user();
         $data['countries'] = Country::all();
        $data['states'] = State::all();
        $data['cities'] = City::all();
        return $dataTable->render('admin.locations.cities', $data);
      }


          public function locationManagement(Request $request)
    {
        $type = $request->get('type', 'countries');
        
        if ($request->ajax()) {
            $dataTable = new LocationsDataTable($type);
            return $dataTable->render('admin.settings.locations');
        }
        
        return view('admin.settings.locations', compact('type'));
    }

    public function getStatesByCountry($countryId): JsonResponse
{
    try {
        $states = State::where('country_id', $countryId)
                      ->where('status', Status::Active)
                      ->select('id', 'name')
                      ->orderBy('name')
                      ->get();

        if ($states->isEmpty()) {
            return response()->json([
                'status' => 204,
                'message' => 'No states found for this country',
                'data' => []
            ]);
        }

        return response()->json([
            'status' => 200,
            'message' => 'States retrieved successfully',
            'data' => $states
        ]);
    } catch (Exception $e) {
        Log::error('Error fetching states: ' . $e->getMessage());
        
        return response()->json([
            'status' => 500,
            'message' => 'Error fetching states',
            'data' => []
        ]);
    }
}

public function getCitiesByState($stateId): JsonResponse
{
    try {
        $cities = City::where('state_id', $stateId)
                     ->where('status', Status::Active)
                     ->select('id', 'name')
                     ->orderBy('name')
                     ->get();

        if ($cities->isEmpty()) {
            return response()->json([
                'status' => 204,
                'message' => 'No cities found for this state',
                'data' => []
            ]);
        }

        return response()->json([
            'status' => 200,
            'message' => 'Cities retrieved successfully',
            'data' => $cities
        ]);
    } catch (Exception $e) {
        Log::error('Error fetching cities: ' . $e->getMessage());
        
        return response()->json([
            'status' => 500,
            'message' => 'Error fetching cities',
            'data' => []
        ]);
    }
}




    /**
     * Toggle country status
     */
    // public function toggleCountry(Country $country)
    // {
    //     try {
    //         $country->update([
    //             'status' => $country->status == '1' ? '0' : '1'
    //         ]);
            
    //         $status = $country->status == '1' ? 'activated' : 'deactivated';
            
    //         if (request()->ajax()) {
    //             return response()->json([
    //                 'success' => true,
    //                 'message' => "Country {$country->name} has been {$status} successfully!"
    //             ]);
    //         }
            
    //         return redirect()->back()->with('success', "Country {$country->name} has been {$status} successfully!");
            
    //     } catch (\Exception $e) {
    //         Log::error('Failed to toggle country status', [
    //             'country_id' => $country->id,
    //             'error' => $e->getMessage()
    //         ]);
            
    //         if (request()->ajax()) {
    //             return response()->json([
    //                 'success' => false,
    //                 'message' => 'Failed to update country status.'
    //             ], 500);
    //         }
            
    //         return redirect()->back()->with('error', 'Failed to update country status.');
    //     }
    // }

    /**
     * Toggle state status
     */
    public function toggleState(State $state)
    {
        try {
            $state->update([
                'status' => $state->status == '1' ? '0' : '1'
            ]);
            
            $status = $state->status == '1' ? 'activated' : 'deactivated';
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => "State {$state->name} has been {$status} successfully!"
                ]);
            }
            
            return redirect()->back()->with('success', "State {$state->name} has been {$status} successfully!");
            
        } catch (\Exception $e) {
            Log::error('Failed to toggle state status', [
                'state_id' => $state->id,
                'error' => $e->getMessage()
            ]);
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update state status.'
                ], 500);
            }
            
            return redirect()->back()->with('error', 'Failed to update state status.');
        }
    }

    /**
     * Toggle city status
     */
    public function toggleCity(City $city)
    {
        try {
            $city->update([
                'status' => $city->status == '1' ? '0' : '1'
            ]);
            
            $status = $city->status == '1' ? 'activated' : 'deactivated';
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => "City {$city->name} has been {$status} successfully!"
                ]);
            }
            
            return redirect()->back()->with('success', "City {$city->name} has been {$status} successfully!");
            
        } catch (\Exception $e) {
            Log::error('Failed to toggle city status', [
                'city_id' => $city->id,
                'error' => $e->getMessage()
            ]);
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update city status.'
                ], 500);
            }
            
            return redirect()->back()->with('error', 'Failed to update city status.');
        }
    }


public function toggleCountry(Country $country)
{
    try {
        // Toggle using enum values
        $newStatus = $country->status === Status::Active ? Status::Inactive : Status::Active;
        
        $country->update([
            'status' => $newStatus
        ]);
        
        $statusText = $country->status === Status::Active ? 'activated' : 'deactivated';
        
        return redirect()->back()->with('success', "Country {$country->name} has been {$statusText} successfully!");
        
    } catch (\Exception $e) {
        \Log::error('Failed to toggle country status', [
            'country_id' => $country->id,
            'error' => $e->getMessage()
        ]);
        
        return redirect()->back()->with('error', 'Failed to update country status.');
    }
}





    

    public function index(Request $request) {


        if ($request->ajax()) {

            return Datatables::of(Country::query())
                    ->addIndexColumn()
                    ->editColumn('created_at', '{{date("Y-m-d",strtotime($created_at))}}')
                    // ->editColumn('updated_at', '{{date("Y-m-d",strtotime($updated_at))}}')


                    ->editColumn('status', function( $data) {

                        $checked = $data->status->value=='1'?'checked':'';
                      return new HTMLString('<label class="inline-flex items-center mb-5 cursor-pointer" for="defaultToggle-'.$data->id.'" >
                    <input  id="defaultToggle-'.$data->id.'" data-url="'.route('location.country.update.status',$data->id).'" type="checkbox"  role="switch" '.$checked.' value="1" class="sr-only peer updateStatus">
                    <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:w-5 after:h-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>');


                    })
                    ->addColumn('action', function($data){

                            $btn = "<button data-modal-target='loadModal' data-modal-toggle='loadModal' data-title='Edit ".$data->name."' data-id='".$data->id."' data-url='".route('location.country.edit',$data)."' class='block mt-2 mr-2 text-white bg-blue-700 hover:bg-blue-800 openModal focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm p-2 inline text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800' type='button'>
                                        <svg class='w-4 h-4' fill='currentColor' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'><path d='M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z'></path><path fill-rule='evenodd' d='M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z' clip-rule='evenodd'></path></svg>
                </button><a href='".route('location.state.list',$data->id)."' class='text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm p-2 inline text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800'>States</a>

                                  <form class='inline-flex' id='deleteItem' action='".route('location.country.destroy',['country'=>$data])."' method='POST' x-data='{
                                        confirmDelete(event){
                                            event.preventDefault();
                                             var choice = confirm(` Are you sure you want to delete {$data->name} ? `);

                                            if (choice) {
                                                document.getElementById(`deleteItem`).submit();
                                            }}
                                        }
                                     '>

                    <input type='hidden' name='_token' value='".csrf_token()."'/>
                    <input type='hidden' name='_method' value='DELETE' />

                    <button  @click='confirmDelete' class='inline-flex items-center delete mt-2 p-2 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900 '>
                            <svg class='w-4 h-4' fill='currentColor' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'><path fill-rule='evenodd' d='M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z' clip-rule='evenodd'></path></svg>

                  </button>

                </form>
               ";

                            return $btn;
                    })
                    ->rawColumns(['action'])
                    ->make(true);
        }

        $data['pageTitle'] = "All Countries";
        return view('admin.location.index',$data);
    }


    /**
     * Create a new country entry
     * @param string $name country name
     * @param string $code country code
     * @param enum $status country status YES or NO
     *
     * @return RedirectResponse
     *
     */

     public function createCountry(Request $request):RedirectResponse
     {

        $validator =Validator::make($request->all(),[
            'name' => 'required|max:255',
            'code' => 'required|max:3',
            'status' => 'nullable',new Enum(Status::class),

        ]);
        if ($validator->fails()) {
            return redirect()->back('#loadModal')
        ->withErrors($validator)
        ->withInput()
        ->with('error', 'Opps !! Error occured, Validation failed, kindly check your inputs make they are not empty');


        }

        $status = $request->has('status') ? $request->get('status') :'0';
        Country::create([
            'code'=>$request->code,
            'name'=>$request->name,
            'user_id'=>Auth::user()->id,
            'status'=>$status
        ]);

        return redirect()->back()->with('success','Country Added Successfully');

    }


    /**
     * Edit country
     * @param string $id country identifier
     *
     * @return View
     */


    public function edit(string $id):view
    {

        $data['country'] = Country::whereId($id)->first();

        return view('admin.location.modal.edit', $data);

    }

    /**
     * Delete Country from database
    * @param Model Country object
    *
    * @return RedirectResponse
    */


    public function deleteCountry(Country $country):RedirectResponse
    {

        if(Auth::user()->isAdmin->value==IsAdmin::Yes->value){


            // $country = Country::whereId($id)->first();
            if($country->delete()){
                return redirect()->back()->with('success','Country deleted Successfully');

            }
        }

        return redirect()->back()->with('success','Error deleting Country');
    }


    



    /**
     * update country
    * @param string name country name
    * @param string code country code
    * @param enum   status Yes / No status
    *
    * @return RedirectResponse
    */

    public function updateCountry(string $id, Request $request):RedirectResponse
    {
        $validator =Validator::make($request->all(),[
            'name' => 'required|max:255',
            'code' => 'required|max:3',
            'status' => 'nullable',new Enum(Status::class),

        ]);
        if ($validator->fails()) {
            return redirect()->back('#loadModal')
        ->withErrors($validator)
        ->withInput()
        ->with('error', 'Opps !! Error occured, Validation failed, kindly check your inputs make they are not empty');


        }

        $status = $request->has('status') ? Status::Active->value :Status::Inactive->value;

        $update= Country::whereId($id)->update(['code'=>$request->code,'name'=>$request->name,'status'=>$status]);

        return redirect()->back()->with('success','Country update Successfully');
    }


    /**
     * Update country status
     *
     * @param Request $request
     * @param int $status
     *
     * @return JsonResponse
     */
    public function updateCountryStatus(string $id,Request $request):JsonResponse
    {


        $validator =Validator::make($request->all(),[

            'status' => new Enum(Status::class),
        ]);

        if ($validator->fails()) {
            return response()->json(['status' =>400, 'message' => $validator->errors()->first()],  400);
        }

        $update = Country::whereId($id)->update(['status'=>$request->status]);

        if($update){
            return response()->json(['status' =>200, 'message' => 'Status updated successful',],  200);
        }else{
            return response()->json(['status' =>204, 'message' => 'Error updating status'],  204);
        }

    }


    /**
     * get country as json
     *
     * @return JsonResponse
     */
    public function getCountryJson(): JsonResponse
    {

        $country = Country::where('status',Status::Active->value)->get();

        if($country){
            return response()->json(['status' =>200, 'message' => 'success','data'=>$country],  200);
        }else{
            return response()->json(['status' =>204, 'message' => 'No Data'],  204);
        }


    }


    /**
     * get state by country as json
     *
     * @param int $country_id country identifier
     *
     *
     * @return JsonResponse
     */

    public function getStateByCountryJson(int $country_id): JsonResponse
    {

        $state = State::where(['country_id'=>$country_id,'status'=>Status::Active->value])->get(['id','name']);

        if($state){
            return response()->json(['status' =>200, 'message' => 'success','data'=>$state],  200);
        }else{
            return response()->json(['status' =>204, 'message' => 'No Data'],  204);
        }


    }

    /**
     * get city by state as json
     *
     * @param int $state_id state identifier
     *
     *
     * @return JsonResponse
     */

    public function getCitiesByStateJson(int $state_id): JsonResponse
    {

        $cities = City::where(['state_id'=>$state_id,'status'=>Status::Active->value])->get(['id','name']);

        if($cities){
            return response()->json(['status' =>200, 'message' => 'success','data'=>$cities],  200);
        }else{
            return response()->json(['status' =>204, 'message' => 'No Data'],  204);
        }


    }


       /**
     * Display country table
     * @param Request $request
     *
     * @return mixed View result
     */

     public function StateIndex(Request $request, Country $country) {

        if ($request->ajax()) {

            return Datatables::of(State::query()->where('country_id',$country->id))
                    ->addIndexColumn()
                    ->editColumn('created_at', '{{date("Y-m-d",strtotime($created_at))}}')
                    // ->editColumn('updated_at', '{{date("Y-m-d",strtotime($updated_at))}}')

                    ->editColumn('country_id', function( $data) {

                        return $data->country->name;
                    })
                    ->editColumn('status', function( $data) {

                        $checked = $data->status->value=='1'?'checked':'';
                      return new HTMLString('<label class="inline-flex items-center mb-5 cursor-pointer" for="defaultToggle-'.$data->id.'" >
                    <input  id="defaultToggle-'.$data->id.'" data-url="'.route('location.state.update.status',$data->id).'" type="checkbox"  role="switch" '.$checked.' value="1" class="sr-only peer updateStatus">
                    <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:w-5 after:h-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>');


                    })
                    ->addColumn('action', function($data){

                            $btn = "<button data-modal-target='loadModal' data-modal-toggle='loadModal' data-title='Edit ".$data->name."' data-id='".$data->id."' data-url='".route('location.state.edit',$data)."' class='block mt-2 mr-2 text-white bg-blue-700 hover:bg-blue-800 openModal focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm p-2 inline text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800' type='button'>
                                        <svg class='w-4 h-4' fill='currentColor' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'><path d='M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z'></path><path fill-rule='evenodd' d='M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z' clip-rule='evenodd'></path></svg>
                </button><a href='".route('location.city.list',$data->id)."' class='text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm p-2 inline text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800'>Cities</a>

                                  <form class='inline-flex' id='deleteItem' action='".route('location.state.destroy',['state'=>$data])."' method='POST' x-data='{
                                        confirmDelete(event){
                                            event.preventDefault();
                                             var choice = confirm(` Are you sure you want to delete {$data->name} ? `);

                                            if (choice) {
                                                document.getElementById(`deleteItem`).submit();
                                            }}
                                        }
                                     '>

                    <input type='hidden' name='_token' value='".csrf_token()."'/>
                    <input type='hidden' name='_method' value='DELETE' />

                    <button  @click='confirmDelete' class='inline-flex items-center delete mt-2 p-2 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900 '>
                            <svg class='w-4 h-4' fill='currentColor' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'><path fill-rule='evenodd' d='M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z' clip-rule='evenodd'></path></svg>

                  </button>

                </form>
               ";

                            return $btn;
                    })
                    ->rawColumns(['action'])
                    ->make(true);
        }

        $data['pageTitle'] = "List State in ".$country->name;
        $data['country'] = $country;
        return view('admin.location.state.index',$data);
    }

        /**
     * Create a new state entry
     * @param string $name state name
     * @param string $country_id country id
     * @param string $user_id user id
     * @param enum $status country status YES or NO
     *
     * @return RedirectResponse
     *
     */

     public function createState(Request $request):RedirectResponse
     {

        $validator =Validator::make($request->all(),[
            'name' => 'required|max:255',
            'country' => 'required|exists:countries,id',
            'status' => 'nullable',new Enum(Status::class),

        ]);
        if ($validator->fails()) {
            return redirect()->back('#loadModal')
        ->withErrors($validator)
        ->withInput()
        ->with('error', 'Opps !! Error occured, Validation failed, kindly check your inputs make they are not empty');


        }

        $status = $request->has('status') ? $request->get('status') :'0';
        State::create([
            'country_id'=>$request->country,
            'name'=>$request->name,
            'user_id'=>Auth::user()->id,
            'status'=>$status
        ]);

        return redirect()->back()->with('success','Country Added Successfully');

    }


    /**
     * Edit State
     * @param string $id State identifier
     *
     * @return View
     */


    public function editState(string $id):view
    {

        $data['state'] = State::whereId($id)->first();

        return view('admin.location.state.modal.edit', $data);

    }

    /**
     * Delete State from database
    * @param Model State object
    *
    * @return RedirectResponse
    */


    public function deleteState(State $state):RedirectResponse
    {

        if(Auth::user()->isAdmin->value==IsAdmin::Yes->value){


            // $country = Country::whereId($id)->first();
            if($state->delete()){
                return redirect()->back()->with('success','State deleted Successfully');

            }
        }

        return redirect()->back()->with('success','Error deleting State');
    }



    /**
     * update State
    * @param string name state name
    * @param string country country identifier
    * @param enum   status Yes / No status
    *
    * @return RedirectResponse
    */

    public function updateState(string $id, Request $request):RedirectResponse
    {
        $validator =Validator::make($request->all(),[
            'name' => 'required|max:255',
            'status' => 'nullable',new Enum(Status::class),

        ]);
        if ($validator->fails()) {
            return redirect()->back('#loadModal')
        ->withErrors($validator)
        ->withInput()
        ->with('error', 'Opps !! Error occured, Validation failed, kindly check your inputs make they are not empty');


        }

        $status = $request->has('status') ? Status::Active->value :Status::Inactive->value;

        $update= State::whereId($id)->update(['name'=>$request->name,'status'=>$status]);

        return redirect()->back()->with('success','State update Successfully');
    }


    /**
     * Update State status
     *
     * @param Request $request
     * @param int $status
     *
     * @return JsonResponse
     */
    public function updateStateStatus(string $id,Request $request):JsonResponse
    {


        $validator =Validator::make($request->all(),[

            'status' => new Enum(Status::class),
        ]);

        if ($validator->fails()) {
            return response()->json(['status' =>400, 'message' => $validator->errors()->first()],  400);
        }

        $update = State::whereId($id)->update(['status'=>$request->status]);

        if($update){
            return response()->json(['status' =>200, 'message' => 'Status updated successful',],  200);
        }else{
            return response()->json(['status' =>204, 'message' => 'Error updating status'],  204);
        }

    }



       /**
     * Display Sities table
     * @param Request $request
     *
     * @return mixed View result
     */

     public function CityIndex(Request $request, State $state) {

        if ($request->ajax()) {

            return Datatables::of(City::query()->where('state_id',$state->id))
                    ->addIndexColumn()
                    ->editColumn('created_at', '{{date("Y-m-d",strtotime($created_at))}}')
                    // ->editColumn('updated_at', '{{date("Y-m-d",strtotime($updated_at))}}')

                    ->editColumn('state_id', function( $data) {

                        return $data->state->name;
                    })
                    ->editColumn('status', function( $data) {

                        $checked = $data->status->value=='1'?'checked':'';
                      return new HTMLString('<label class="inline-flex items-center mb-5 cursor-pointer" for="defaultToggle-'.$data->id.'" >
                    <input  id="defaultToggle-'.$data->id.'" data-url="'.route('location.city.update.status',$data->id).'" type="checkbox"  role="switch" '.$checked.' value="1" class="sr-only peer updateStatus">
                    <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:w-5 after:h-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>');


                    })
                    ->addColumn('action', function($data){

                            $btn = "<button data-modal-target='loadModal' data-modal-toggle='loadModal' data-title='Edit ".$data->name."' data-id='".$data->id."' data-url='".route('location.city.edit',$data)."' class='block mt-2 mr-2 text-white bg-blue-700 hover:bg-blue-800 openModal focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm p-2 inline text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800' type='button'>
                                        <svg class='w-4 h-4' fill='currentColor' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'><path d='M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z'></path><path fill-rule='evenodd' d='M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z' clip-rule='evenodd'></path></svg>
                </button>

                                  <form class='inline-flex' id='deleteItem' action='".route('location.city.destroy',['city'=>$data])."' method='POST' x-data='{
                                        confirmDelete(event){
                                            event.preventDefault();
                                             var choice = confirm(` Are you sure you want to delete {$data->name} ? `);

                                            if (choice) {
                                                document.getElementById(`deleteItem`).submit();
                                            }}
                                        }
                                     '>

                    <input type='hidden' name='_token' value='".csrf_token()."'/>
                    <input type='hidden' name='_method' value='DELETE' />

                    <button  @click='confirmDelete' class='inline-flex items-center delete mt-2 p-2 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900 '>
                            <svg class='w-4 h-4' fill='currentColor' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'><path fill-rule='evenodd' d='M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z' clip-rule='evenodd'></path></svg>

                  </button>

                </form>
               ";

                            return $btn;
                    })
                    ->rawColumns(['action'])
                    ->make(true);
        }



        $data['pageTitle'] = "List Cities in ".$state->name;
        $data['state'] = $state;
        return view('admin.location.city.index',$data);
    }

        /**
     * Create a new city entry
     * @param string $name city name
     * @param string $state_id state id
     * @param string $user_id user id
     * @param enum $status  status YES or NO
     *
     * @return RedirectResponse
     *
     */

     public function createCity(Request $request):RedirectResponse
     {

        $validator =Validator::make($request->all(),[
            'name' => 'required|max:255',
            'state' => 'required|exists:states,id',
            'status' => 'nullable',new Enum(Status::class),

        ]);
        if ($validator->fails()) {
            return redirect()->back('#loadModal')
        ->withErrors($validator)
        ->withInput()
        ->with('error', 'Opps !! Error occured, Validation failed, kindly check your inputs make they are not empty');


        }

        $status = $request->has('status') ? $request->get('status') :'0';
        City::create([
            'state_id'=>$request->state,
            'name'=>$request->name,
            'user_id'=>Auth::user()->id,
            'status'=>$status
        ]);

        return redirect()->back()->with('success','City Added Successfully');

    }


    /**
     * Edit City
     * @param string $id State identifier
     *
     * @return View
     */


    public function editCity(string $id):view
    {

        $data['city'] = City::whereId($id)->first();

        return view('admin.location.city.modal.edit', $data);

    }

    /**
     * Delete city from database
    * @param Model city object
    *
    * @return RedirectResponse
    */


    public function deleteCity(City $city):RedirectResponse
    {

        if(Auth::user()->isAdmin->value==IsAdmin::Yes->value){


            // $country = Country::whereId($id)->first();
            if($city->delete()){
                return redirect()->back()->with('success','City deleted Successfully');

            }
        }

        return redirect()->back()->with('success','Error deleting City');
    }



    /**
     * update city
    * @param string name state name
    * @param string id city identifier
    * @param enum   status Yes / No status
    *
    * @return RedirectResponse
    */

    public function updateCity(string $id, Request $request):RedirectResponse
    {
        $validator =Validator::make($request->all(),[
            'name' => 'required|max:255',
            'status' => 'nullable',new Enum(Status::class),

        ]);
        if ($validator->fails()) {
            return redirect()->back('#loadModal')
        ->withErrors($validator)
        ->withInput()
        ->with('error', 'Opps !! Error occured, Validation failed, kindly check your inputs make they are not empty');


        }

        $status = $request->has('status') ? Status::Active->value :Status::Inactive->value;

        $update= City::whereId($id)->update(['name'=>$request->name,'status'=>$status]);

        return redirect()->back()->with('success','City update Successfully');
    }


    /**
     * Update city status
     *
     * @param Request $request
     * @param int $status
     *
     * @return JsonResponse
     */
    public function updateCityStatus(string $id,Request $request):JsonResponse
    {


        $validator =Validator::make($request->all(),[

            'status' => new Enum(Status::class),
        ]);

        if ($validator->fails()) {
            return response()->json(['status' =>400, 'message' => $validator->errors()->first()],  400);
        }

        $update = City::whereId($id)->update(['status'=>$request->status]);

        if($update){
            return response()->json(['status' =>200, 'message' => 'Status updated successful',],  200);
        }else{
            return response()->json(['status' =>204, 'message' => 'Error updating status'],  204);
        }

    }

}
