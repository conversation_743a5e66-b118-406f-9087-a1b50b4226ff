<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use App\Models\Transaction;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Group;

class DashboardController extends Controller
{
    //
    public function index()
    {
        $user = User::all();
        $transaction = Transaction::all();
        $group = Group::all();

    return view('admin.dashboard', compact('user', 'transaction', 'group'));

    }
}
