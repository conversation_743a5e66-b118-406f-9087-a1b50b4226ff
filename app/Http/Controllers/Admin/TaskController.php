<?php

namespace App\Http\Controllers\Admin;

use App\Models\Task;
use App\Enums\TxStatus;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\DataTables\Admin\TaskDataTable;

class TaskController extends Controller
{
    //
     public function viewTask( TaskDataTable $dataTable)
    {
        return $dataTable->render('admin.tasks.index');
    }
    

    public function storeTask(Request $request)
    {
        $request->validate([
            'task_name' => 'required|string|max:255',
            'task_description' => 'required|string',
            'task_points' => 'required|integer|min:1',
            'status' => ['required', Rule::in(array_column(TxStatus::cases(), 'value'))],
    
        ]);

        Task::create([
            'task_name' => $request->task_name,
            'task_description' => $request->task_description,
            'task_points' => $request->task_points,
            'status' => $request->status,
        ]);

        return redirect()->back()->with('success', 'Task created successfully');
    }

    public function editTask(Task $task)
    {
        return view('admin.tasks.edit', compact('task'));
    }

    // public function updateTask(Request $request, Task $task)
    // {
    //     $request->validate([
    //         'task_name' => 'required|string|max:255',
    //         'task_description' => 'required|string',
    //         'task_points' => 'required|integer|min:1',
    //         'status' => ['required', Rule::in(array_column(TxStatus::cases(), 'value'))],
    //     ]);

    //     $task->update([
    //         'task_name' => $request->task_name,
    //         'task_description' => $request->task_description,
    //         'task_points' => $request->task_points,
    //         'status' => $request->status,
    //     ]);

    //     return redirect()->route('admin.tasks.index')->with('success', 'Task updated successfully');
    // }

    public function updateTask(Request $request, Task $task)
{
    // Add debugging
    Log::info('Update task called', [
        'task_id' => $task->id,
        'request_data' => $request->all()
    ]);

    $request->validate([
        'task_name' => 'required|string|max:255',
        'task_description' => 'required|string',
        'task_points' => 'required|integer|min:1',
        'status' => ['required', Rule::in(array_column(TxStatus::cases(), 'value'))],
    ]);

    $updated = $task->update([
        'task_name' => $request->task_name,
        'task_description' => $request->task_description,
        'task_points' => $request->task_points,
        'status' => $request->status,
    ]);

    Log::info('Task update result', ['updated' => $updated]);

    if ($request->ajax()) {
        return response()->json(['success' => true, 'message' => 'Task updated successfully']);
    }

    return redirect()->route('admin.tasks.index')->with('success', 'Task updated successfully');
}


    public function destroyTask(Task $task)
    {
        $task->delete();

        return redirect()->back()->with('success', 'Task deleted successfully');
    }
}
