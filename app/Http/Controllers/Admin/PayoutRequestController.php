<?php

namespace App\Http\Controllers\Admin;

use App\Enums\TxStatus;
use Illuminate\Http\Request;
use App\Models\PayoutRequest;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;

class PayoutRequestController extends Controller
{
    //

    public function approvePayouts(PayoutRequest $payoutRequest)
{
    $payoutRequest->update([
        'status' => TxStatus::Approved,
        'processed_at' => now(),
        'processed_by' => Auth::id(),
    ]);

    return redirect()->back()->with('success', 'Payout request approved successfully');
}

public function rejectPayouts(PayoutRequest $payoutRequest)
{
    $payoutRequest->update([
        'status' => TxStatus::Rejected,
        'processed_at' => now(),
        'processed_by' => Auth::id(),
    ]);

    return redirect()->back()->with('success', 'Payout request rejected successfully');
}

    public function retryPayouts($id)
    {
        $payoutRequest = PayoutRequest::findOrFail($id);
        
        // Update status back to pending for retry
        $payoutRequest->update([
            'status' => TxStatus::Pending
        ]);
        
        return redirect()->back()->with('success', 'Payout request has been set for retry.');
    }


}
