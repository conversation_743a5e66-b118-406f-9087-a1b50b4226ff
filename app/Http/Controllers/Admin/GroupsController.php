<?php

namespace App\Http\Controllers\Admin;

use App\Models\Group;
use App\Enums\Currency;
use App\Enums\Frequency;
use App\Enums\GroupStatus;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Http\Controllers\Controller;
use App\DataTables\Admin\GroupsDataTable;
use Str;

class GroupsController extends Controller
{
    //
    public function viewGroups(GroupsDataTable $dataTable)
    {
        // This method will return the DataTable view for groups
        return $dataTable->render('admin.groups.index');
    }

    public function createGroups()
    {
        return view('admin.groups.create');
    }

   public function storeGroups(Request $request)
{
    $request->validate([
        'group_name' => 'required|string|max:255',
        'amount' => 'required|numeric|min:0',
        'frequency' => ['required', Rule::enum(Frequency::class)],
        'max_number_of_member' => 'required|integer|min:1',
        'orderOfPayment' => 'required|string',
        'group_rules' => 'nullable|string',
        'status' => ['required', Rule::enum(GroupStatus::class)],
        'last_activity_date' => 'nullable|date',
        'currency' => ['required', Rule::enum(Currency::class)],
    ]);

    // generate link
    // $generatedLink = url('groups/' . Str::slug($request->group_name) . '-' . Str::random(6));
     $generatedLink = Str::slug($request->group_name) . '-' . Str::random(6);
    $fullUrl = url('groups/' . $generatedLink);

    // store group
    Group::create([
        'group_name' => $request->group_name,
        'amount' => $request->amount,
        'frequency' => $request->frequency,
        'number_of_members' => 0,
        'max_number_of_member' => $request->max_number_of_member,
        'status' => $request->status,
        'group_link' => $generatedLink,
        'orderOfPayment' => $request->orderOfPayment,
        'group_rules' => $request->group_rules,
        'currency' => $request->currency,
        'last_activity_date' => now(),
        'payment_order' => 1
    ]);

    // return with group URL
    // return redirect()->route('admin.groups.index')->with('group_created', true)->with('group_url', $generatedLink);
        return redirect()->back()->with('group_created', true)->with('group_url', $generatedLink);

    
}

    
}
