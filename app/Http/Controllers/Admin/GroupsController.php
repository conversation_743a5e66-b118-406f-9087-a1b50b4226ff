<?php

namespace App\Http\Controllers\Admin;

use Str;
use App\Models\User;
use App\Models\Group;
use App\Enums\Currency;
use App\Enums\Frequency;
use App\Enums\GroupStatus;
use App\Models\GroupMember;
use App\Models\Contribution;
use App\Models\GroupMessage;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\DataTables\Admin\GroupsDataTable;

class GroupsController extends Controller
{
    //
    public function viewGroups(GroupsDataTable $dataTable, Group $group)
    {
        // This method will return the DataTable view for groups
        return $dataTable->render('admin.groups.index', compact('group'));
    }

    public function createGroups()
    {
        return view('admin.groups.create');
    }

    public function storeGroups(Request $request)
    {
        $request->validate([
            'group_name' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'frequency' => ['required', Rule::enum(Frequency::class)],
            'max_number_of_member' => 'required|integer|min:1',
            'orderOfPayment' => 'required|string',
            'group_rules' => 'nullable|string',
            'status' => ['required', Rule::enum(GroupStatus::class)],
            'last_activity_date' => 'nullable|date',
            'currency' => ['required', Rule::enum(Currency::class)],
        ]);

        // Generate link slug
        $generatedLink = Str::slug($request->group_name) . '-' . Str::random(6);
        
        // Create the full URL
        $fullUrl = url('groups/' . $generatedLink);

        // Store group
        $group = Group::create([
            'group_name' => $request->group_name,
            'amount' => $request->amount,
            'frequency' => $request->frequency,
            'number_of_members' => 0,
            'max_number_of_member' => $request->max_number_of_member,
            'status' => $request->status,
            'group_link' => $generatedLink, // Store just the slug in database
            'orderOfPayment' => $request->orderOfPayment,
            'group_rules' => $request->group_rules,
            'currency' => $request->currency,
            'last_activity_date' => now(),
            'payment_order' => 1
        ]);

        // Store the FULL URL in session, not just the slug
        return redirect()->back()->with('group_created', true)->with('group_url', $fullUrl); // Store full URL here
    }

    
    // 


    public function editGroup(Group $group)
    {
        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'group' => $group
            ]);
        }
        
        return view('admin.groups.edit', compact('group'));
    }

    public function suspendGroup($id)
{
    $group = Group::findOrFail($id);
    $group->update(['status' => GroupStatus::Suspended->value]); // Adjust based on your enum values
    
    return redirect()->back()->with('success', 'Group suspended successfully!');
}


//     public function editGroup($id)
// {
//     $group = Group::findOrFail($id);
//     return view('admin.groups.partials.edit-form', compact('group'));
// }


    // public function updateGroups(Request $request, Group $group)
    // {
    //     $request->validate([
    //         'group_name' => 'required|string|max:255',
    //         'amount' => 'required|numeric|min:0',
    //         'frequency' => ['required', Rule::enum(Frequency::class)],
    //         'max_number_of_member' => 'required|integer|min:1',
    //         'orderOfPayment' => 'required|string',
    //         'group_rules' => 'nullable|string',
    //         'status' => ['required', Rule::enum(GroupStatus::class)],
    //         'currency' => ['required', Rule::enum(Currency::class)],
    //     ]);

    //     $group->update([
    //         'group_name' => $request->group_name,
    //         'amount' => $request->amount,
    //         'frequency' => $request->frequency,
    //         'max_number_of_member' => $request->max_number_of_member,
    //         'orderOfPayment' => $request->orderOfPayment,
    //         'group_rules' => $request->group_rules,
    //         'status' => $request->status,
    //         'currency' => $request->currency,
    //     ]);

    //     if (request()->ajax()) {
    //         return response()->json([
    //             'success' => true,
    //             'message' => 'Group updated successfully!'
    //         ]);
    //     }

    //     return redirect()->back()->with('success', 'Group updated successfully!');
    // }

    public function updateGroups(Request $request, $id)
{
    $group = Group::findOrFail($id);
    
    $request->validate([
        'group_name' => 'required|string|max:255',
        'amount' => 'required|numeric|min:0',
        'frequency' => ['required', Rule::enum(Frequency::class)],
        'max_number_of_member' => 'required|integer|min:1',
        'orderOfPayment' => 'required|string',
        'group_rules' => 'nullable|string',
        'status' => ['required', Rule::enum(GroupStatus::class)],
        'currency' => ['required', Rule::enum(Currency::class)],
    ]);

    $group->update([
        'group_name' => $request->group_name,
        'amount' => $request->amount,
        'frequency' => $request->frequency,
        'max_number_of_member' => $request->max_number_of_member,
        'orderOfPayment' => $request->orderOfPayment,
        'group_rules' => $request->group_rules,
        'status' => $request->status,
        'currency' => $request->currency,
    ]);

    if (request()->ajax()) {
        return response()->json([
            'success' => true,
            'message' => 'Group updated successfully!'
        ]);
    }

    return redirect()->back()->with('success', 'Group updated successfully!');
}

   public function showGroup($id)
    {
   

        $group = Group::findOrFail($id);
        $groupMembers = GroupMember::where('group_id', $group->id)->where('status', 'active')->with('user')->get();

        $messages = GroupMessage::forGroup($group->id)->with('user')->orderBy('created_at', 'asc')->limit(50)->get();
        $contributions = Contribution::where('group_id', $group->id)->with('user')->orderBy('created_at', 'desc')->get();

        // Separate pending requests from active members
       $pendingRequests = GroupMember::where('group_id', $group->id)->where('status', \App\Enums\GroupStatus::Pending->value)->orderBy('created_at', 'asc')
           ->with('user')->get();

       $groupMembers = GroupMember::where('group_id', $group->id)
    ->where('status', 'active')->whereHas('user', fn($q) => $q->where('user_type', 'user')) 
    ->with('user')
    ->get();

     $admin = GroupMember::where('group_id', $group->id)
    ->where('status', 'active')->whereHas('user', fn($q) => $q->where('user_type', 'admin'))
    ->with('user')
    ->first();

    $payoutLists = GroupMember::where('group_id', $group->id)
    ->where('status', 'active')->with('user')->orderBy('created_at', 'asc')
    ->get();

    $user = Auth::user();

        return view('admin.groups.show', compact('group', 'groupMembers', 'messages', 'contributions', 'pendingRequests', 'admin', 'payoutLists', 'user'));        
    }

public function deleteGroup(Group $group)
{
    $group->delete();

    return redirect()->back()->with('success', 'Group deleted successfully');
}


   




   



    
}
