<?php

namespace App\Http\Controllers\Admin;

use Str;
use Exception;
use App\Models\User;
use App\Models\Group;
use App\Enums\Currency;
use App\Enums\Frequency;
use App\Enums\GroupStatus;
use App\Models\GroupAdmin;
use App\Models\GroupMember;
use App\Models\Contribution;
use App\Models\GroupMessage;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\DataTables\Admin\GroupsDataTable;

class GroupsController extends Controller
{
    //
    public function viewGroups(GroupsDataTable $dataTable, Group $group)
    {
        // This method will return the DataTable view for groups
        return $dataTable->render('admin.groups.index', compact('group'));
    }

    public function createGroups()
    {
        return view('admin.groups.create');
    }

    public function storeGroups(Request $request)
    {
        $request->validate([
            'group_name' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'frequency' => ['required', Rule::enum(Frequency::class)],
            'max_number_of_member' => 'required|integer|min:1',
            'orderOfPayment' => 'required|string',
            'group_rules' => 'nullable|string',
            'status' => ['required', Rule::enum(GroupStatus::class)],
            'last_activity_date' => 'nullable|date',
            'currency' => ['required', Rule::enum(Currency::class)],
        ]);

        // Generate link slug
        $generatedLink = Str::slug($request->group_name) . '-' . Str::random(6);
        
        // Create the full URL
        $fullUrl = url('groups/' . $generatedLink);

        // Store group
        $group = Group::create([
            'group_name' => $request->group_name,
            'amount' => $request->amount,
            'frequency' => $request->frequency,
            'number_of_members' => 0,
            'max_number_of_member' => $request->max_number_of_member,
            'status' => $request->status,
            'group_link' => $generatedLink, // Store just the slug in database
            'orderOfPayment' => $request->orderOfPayment,
            'group_rules' => $request->group_rules,
            'currency' => $request->currency,
            'last_activity_date' => now(),
            'payment_order' => 1
        ]);

        // Store the FULL URL in session, not just the slug
        return redirect()->back()->with('group_created', true)->with('group_url', $fullUrl); // Store full URL here
    }

    
    // 


    public function editGroup(Group $group)
    {
        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'group' => $group
            ]);
        }
        
        return view('admin.groups.edit', compact('group'));
    }

    public function suspendGroup($id)
{
    $group = Group::findOrFail($id);
    $group->update(['status' => GroupStatus::Suspended->value]); // Adjust based on your enum values
    
    return redirect()->back()->with('success', 'Group suspended successfully!');
}

public function suspendGroupMember($groupId, $memberId)
{
    $group = Group::findOrFail($groupId);

    // Check if the member belongs to the group
    if (!$group->members()->where('users.id', $memberId)->exists()) {
        return redirect()->back()->with('error', 'User is not a member of the group.');
    }

    // Suspend the member in the pivot table
    $group->members()->updateExistingPivot($memberId, [
        'status' => GroupStatus::Suspended->value // or just 'suspended'
    ]);

    return redirect()->back()->with('success', 'Member suspended from group successfully!');
}



//     public function editGroup($id)
// {
//     $group = Group::findOrFail($id);
//     return view('admin.groups.partials.edit-form', compact('group'));
// }


    // public function updateGroups(Request $request, Group $group)
    // {
    //     $request->validate([
    //         'group_name' => 'required|string|max:255',
    //         'amount' => 'required|numeric|min:0',
    //         'frequency' => ['required', Rule::enum(Frequency::class)],
    //         'max_number_of_member' => 'required|integer|min:1',
    //         'orderOfPayment' => 'required|string',
    //         'group_rules' => 'nullable|string',
    //         'status' => ['required', Rule::enum(GroupStatus::class)],
    //         'currency' => ['required', Rule::enum(Currency::class)],
    //     ]);

    //     $group->update([
    //         'group_name' => $request->group_name,
    //         'amount' => $request->amount,
    //         'frequency' => $request->frequency,
    //         'max_number_of_member' => $request->max_number_of_member,
    //         'orderOfPayment' => $request->orderOfPayment,
    //         'group_rules' => $request->group_rules,
    //         'status' => $request->status,
    //         'currency' => $request->currency,
    //     ]);

    //     if (request()->ajax()) {
    //         return response()->json([
    //             'success' => true,
    //             'message' => 'Group updated successfully!'
    //         ]);
    //     }

    //     return redirect()->back()->with('success', 'Group updated successfully!');
    // }

    public function updateGroups(Request $request, $id)
{
    try {
        $group = Group::findOrFail($id);

        // Log the incoming request data
        \Log::info('Group update request', [
            'group_id' => $id,
            'request_data' => $request->all()
        ]);

        $validatedData = $request->validate([
            'group_name' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'frequency' => ['required', Rule::enum(Frequency::class)],
            'max_number_of_member' => 'required|integer|min:1',
            'orderOfPayment' => 'required|string',
            'group_rules' => 'nullable|string',
            'status' => ['required', Rule::enum(GroupStatus::class)],
            'currency' => ['required', Rule::enum(Currency::class)],
        ]);

        // Log validated data
        \Log::info('Validated data', $validatedData);

        $updated = $group->update([
            'group_name' => $request->group_name,
            'amount' => $request->amount,
            'frequency' => $request->frequency,
            'max_number_of_member' => $request->max_number_of_member,
            'orderOfPayment' => $request->orderOfPayment,
            'group_rules' => $request->group_rules,
            'status' => $request->status,
            'currency' => $request->currency,
        ]);

        // Log update result
        \Log::info('Group update result', [
            'updated' => $updated,
            'group_after_update' => $group->fresh()->toArray()
        ]);

        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Group updated successfully!'
            ]);
        }

        return redirect()->back()->with('success', 'Group updated successfully!');

    } catch (\Illuminate\Validation\ValidationException $e) {
        \Log::error('Validation failed', ['errors' => $e->errors()]);

        if (request()->ajax()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        }

        return redirect()->back()->withErrors($e->errors())->withInput();

    } catch (\Exception $e) {
        \Log::error('Group update failed', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        if (request()->ajax()) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update group: ' . $e->getMessage()
            ], 500);
        }

        return redirect()->back()->with('error', 'Failed to update group: ' . $e->getMessage());
    }
}

//    public function showGroup($id)
//     {
   

//         $group = Group::findOrFail($id);
//         $groupMembers = GroupMember::where('group_id', $group->id)->where('status', 'active')->with('user')->get();

//         $messages = GroupMessage::forGroup($group->id)->with('user')->orderBy('created_at', 'asc')->limit(50)->get();
//         $contributions = Contribution::where('group_id', $group->id)->with('user')->orderBy('created_at', 'desc')->get();

//         // Separate pending requests from active members
//        $pendingRequests = GroupMember::where('group_id', $group->id)->where('status', \App\Enums\GroupStatus::Pending->value)->orderBy('created_at', 'asc')
//            ->with('user')->get();

//        $groupMembers = GroupMember::where('group_id', $group->id)
//     ->where('status', 'active')->whereHas('user', fn($q) => $q->where('user_type', 'user')) 
//     ->with('user')
//     ->get();

//      $admin = GroupMember::where('group_id', $group->id)
//     ->where('status', 'active')->whereHas('user', fn($q) => $q->where('user_type', 'admin'))
//     ->with('user')
//     ->first();

//     $payoutLists = GroupMember::where('group_id', $group->id)
//     ->where('status', 'active')->with('user')->orderBy('created_at', 'asc')
//     ->get();

//     $user = Auth::user();

//         return view('admin.groups.show', compact('group', 'groupMembers', 'messages', 'contributions', 'pendingRequests', 'admin', 'payoutLists', 'user'));        
//     }


      public function showGroup($id)
    {
        $user = Auth::user();
        $group = Group::where('id', $id)->first();
       $messages = GroupMessage::forGroup($group->id)->with('user')->orderBy('created_at', 'asc')->limit(50)->get();
       $contributions = Contribution::where('group_id', $group->id)->with('user')->orderBy('created_at', 'desc')->get();

       // Separate pending requests from active members
       $pendingRequests = GroupMember::where('group_id', $group->id)->where('status', \App\Enums\GroupStatus::Pending->value)->orderBy('created_at', 'asc')
           ->with('user')->get();

       $groupMembers = GroupMember::where('group_id', $group->id)->where('status', 'active')->whereHas('user', fn($q) => $q->where('user_type', 'user')) // only users
       ->with('user')->get();

    $admin = GroupMember::where('group_id', $group->id)
    ->where('status', 'active')
    ->whereHas('user', fn($q) => $q->where('user_type', 'admin'))
    ->with('user')
    ->first();

    $payoutLists = GroupMember::where('group_id', $group->id)
    ->where('status', 'active')
    ->with('user')
    ->orderBy('created_at', 'asc')
    ->get();


        return view('admin.groups.show', compact('user', 'group','groupMembers', 'messages', 'contributions', 'pendingRequests', 'admin', 'payoutLists'));
    }


// public function toggleGroupAdmin(Request $request)
// {
//     $userId = $request->user_id;
//     $groupId = $request->group_id;

//     $existing = GroupAdmin::where('user_id', $userId)
//                           ->where('group_id', $groupId)
//                           ->first();

//     if ($existing) {
//         // Toggle OFF (remove admin)
//         $existing->delete();
//         return response()->json(['status' => 'removed']);
//     } else {
//         // Toggle ON (make admin)
//         GroupAdmin::create([
//             'user_id' => $userId,
//             'group_id' => $groupId,
//         ]);
//         return response()->json(['status' => 'added']);
//     }
// }

public function toggleGroupAdmin(Group $group, User $user)
{
    try {
        $isAdmin = GroupAdmin::where('group_id', $group->id)
                             ->where('user_id', $user->id)
                             ->exists();

        if ($isAdmin) {
            // Remove admin rights
            GroupAdmin::where('group_id', $group->id)
                      ->where('user_id', $user->id)
                      ->delete();

            return response()->json([
                'success' => true,
                'message' => 'Admin rights revoked successfully',
                'is_admin' => false
            ]);
        } else {
            // Grant admin rights
            GroupAdmin::create([
                'group_id' => $group->id,
                'user_id' => $user->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Admin rights granted successfully',
                'is_admin' => true
            ]);
        }
    } catch (Exception $e) {
        Log::error('Toggle group admin error: ' . $e->getMessage());
        return response()->json([
            'success' => false,
            'message' => 'Failed to toggle admin rights'
        ], 500);
    }
}

public function deleteGroup(Group $group)
{
    $group->delete();

    return redirect()->back()->with('success', 'Group deleted successfully');
}

public function toggleMemberAdmin(Group $group, GroupMember $member)
{
    try {
        // Check if the member belongs to this group
        if ($member->group_id !== $group->id) {
            return response()->json(['error' => 'Member does not belong to this group'], 400);
        }

        // Get the member's user
        $user = $member->user;

        // Toggle admin status
        if ($user->user_type === 'admin') {
            // Remove admin privileges - change to regular user
            $user->update(['user_type' => 'user']);
            $message = 'User admin privileges removed successfully';
        } else {
            // Grant admin privileges
            $user->update(['user_type' => 'admin']);
            $message = 'User granted admin privileges successfully';
        }

        return response()->json([
            'success' => true,
            'message' => $message,
            'is_admin' => $user->user_type === 'admin'
        ]);

    } catch (\Exception $e) {
        \Log::error('Toggle admin error: ' . $e->getMessage());
        return response()->json(['error' => 'Failed to toggle admin status'], 500);
    }
}







   



    
}
