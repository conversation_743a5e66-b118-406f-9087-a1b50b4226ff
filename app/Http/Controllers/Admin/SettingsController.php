<?php

namespace App\Http\Controllers\Admin;

use Exception;

use App\Models\Media;
use App\Mail\TestMail;
use App\Models\Setting;
use App\Models\Settings;
use Illuminate\Http\Request;
use App\Services\UploadService;
use Illuminate\Validation\Rule;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;


class SettingsController extends Controller
{


    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data['pageTitle'] = 'General Settings';


        return view('admin.settings.general',$data);
    }


    public function updateGeneral(Request $request){
        $validated= $request->validate([
            'sitename' => 'required|string',
            'keywords' => 'nullable|string',
            'description' => 'nullable|string',
            'phone' => 'nullable|string',
            'email' => 'nullable|string',
            'office_address' => 'nullable|string',

            ]);

        foreach ($validated as $key => $value) {
            Setting::updateOrCreate(['name'=>$key], ['value'=>$value]);

            if($key=='sitename'){


                changeEnvironmentVariable('APP_NAME', $value);
            }
       }

       Cache::forget('get_settings');

       return redirect(route('admin.settings.general'))->with('success','General settings updated');
    }


    public function updateGanalytics(Request $request){
        $validated= $request->validate([
            'google_analytics_enable' => 'nullable',
            'google_analytics_id' => 'nullable|string',
            'facebook_pixel_enable' => 'nullable',
            'facebook_pixel_id' => 'nullable|string',
            'google_map_api_enable' => 'nullable',
            'google_map_api_key' => 'nullable|string',

            ]);


            $validated['google_analytics_enable'] = $request->has('google_analytics_enable')?"1":"0";
            $validated['facebook_pixel_enable'] = $request->has('facebook_pixel_enable')?"1":"0";
            $validated['google_map_api_enable'] = $request->has('google_map_api_enable')?"1":"0";

            foreach ($validated as $key => $value) {
                Setting::updateOrCreate(['name'=>$key], ['value'=>$value]);

           }

           Cache::forget('get_settings');

           return redirect(route('admin.settings.general'))->with('success','Global Script & Analytics settings updated');
    }


    public function updateSocial(Request $request)
    {
        $validated= $request->validate([
            'facebook' => 'required|url',
            'instagram' => 'required|url',
            'twitter' => 'required|url',
            'tiktok' => 'required|url',
            'youtube' => 'required|url',

            ]);

            foreach ($validated as $key => $value) {
                Setting::updateOrCreate(['name'=>$key], ['value'=>$value]);

           }

           Cache::forget('get_settings');

           return redirect(route('admin.settings.general'))->with('success','Global Script & Analytics settings updated');
    }

    // public function uploadLogo(Request $request,UploadService $uploadService){

    //     $validator =Validator::make($request->all(),[
    //         'logo' => 'required',
    //         // 'file' => 'required|image|size:1024',
    //         'file'=>'required|max:1024|mimes:jpg,png,jpeg',
    //     ]);

    //     if ($validator->fails()) {
    //         return response()->json(['status' =>400, 'message' => $validator->errors()->first()],  400);
    //     }

    //     if($request->has('file')){
    //         $uploadedfile= $uploadService->fileUploadThumb($request->file('file'), "public/uploads/");

    //         // if(!empty($uploadedfile)){
    //         //     $uploadedfile['user_id']=Auth::user()->id;

    //         //     //save to media library
    //         //     $media=Media::create($uploadedfile);

    //         //     // insert into settings
    //         //     Setting::updateOrCreate(['name'=>$request->logo], ['value'=>$media->id]);

    //         //     Cache::forget('get_settings');
    //         //     return response()->json(['status' =>200, 'message' => "Upload successful"],  200);
    //         // }
    //     }else{
    //         return response()->json(['status' =>400, 'message' => "No file selected"],  400);
    //     }

    // }

    public function updateScripts(Request $request) {

        foreach ($request->except('_token') as $key => $value) {
            Setting::updateOrCreate(['name'=>$key], ['value'=>$value]);

       }

       Cache::forget('get_settings');

       return redirect(route('admin.settings.general'))->with('success','Global Script & Analytics settings updated');
    }


    public function updatePaystackSetting(Request $request){


        $validated= $request->validate([
            'PAYSTACK_ENABLE'=> 'boolean',
            'PAYSTACK_TEST_PUBLIC_KEY' => 'nullable|string',
            'PAYSTACK_TEST_SECRET_KEY' => 'nullable|string',
            'PAYSTACK_LIVE_PUBLIC_KEY' => 'nullable|string',
            'PAYSTACK_LIVE_SECRET_KEY' => 'nullable|string',
            'MERCHANT_EMAIL' => 'nullable|email',
            'PAYSTACK_MODE' => Rule::in(['test','live']),

            ]);


            $validated['PAYSTACK_ENABLE'] = $request->has('PAYSTACK_ENABLE')??"";
            foreach($validated as $key => $value){
                changeEnvironmentVariable($key, $value);
            }

            return redirect(route('admin.settings.payment.gateway'))->with('success','Paystack Pyament gateway updated');

    }
    /**
     * Show the form for creating a new resource.
     */
    public function paymentGateway()
    {

        $data['pageTitle'] = 'Payment Gateway Settings';
        return view('admin.settings.payment_gateway',$data);
    }


    /**
     * Show the form for creating a new resource.
     */
    public function emailSettings()
    {
        $data['pageTitle'] = 'Email Settings';

        return view('admin.settings.email-setting',$data);
    }
    public function updateSmtp(Request $request){


        $validated= $request->validate([
            'MAIL_MAILER'=> 'required|string',
            'MAIL_HOST' => 'required|string',
            'MAIL_PORT' => 'required|integer',
            'MAIL_USERNAME' => 'required|string',
            'MAIL_PASSWORD' => 'required|string',
            'MAIL_ENCRYPTION' => 'required|string',
            'MAIL_FROM_ADDRESS' => 'required|string',


            ]);


            $validated['PAYSTACK_ENABLE'] = $request->has('PAYSTACK_ENABLE')??"";
            foreach($validated as $key => $value){
                changeEnvironmentVariable($key, $value);
            }

            return redirect(route('email.setting'))->with('success','Email Settings updated');

    }


    /**
     * Store a newly created resource in storage.
     */
    // public function smtpTest(Request $request)
    // {
    //     $validator =Validator::make($request->all(),[
    //         'email' => 'required|email:rfc,dns',
    //     ],[
    //         'email.required' => 'The test Email address is required',
    //         'email.email' => 'The test Email address is must be a valid email address.',
    //     ]);

    //     if ($validator->fails()) {
    //         return response()->json(['status' =>400, 'message' => $validator->errors()->first()],  400);
    //     }
    //     try {

    //         Mail::to($request->email)->send(new TestMail());
    //       return response()->json(['status' =>200, 'message' =>"Email Sent successful"],  200);
    //     } catch (Exception $th) {
    //         return response()->json(['status' =>400, 'message' => $th->getMessage()],  400);
    //     }



    // }

    /**
     * Display the specified resource.
     */
    public function show(Setting $settings)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Setting $settings)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Setting $settings)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Setting $settings)
    {
        //
    }

}
