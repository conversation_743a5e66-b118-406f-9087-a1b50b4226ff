<?php

namespace App\Http\Controllers\Admin;

use Exception;

use App\Models\City;
use App\Models\User;
use App\Models\Media;
use App\Models\State;
use App\Mail\TestMail;
use App\Models\Country;
use App\Models\Setting;
use App\Models\Settings;
use Illuminate\Http\Request;
use App\Services\UploadService;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use App\DataTables\Admin\LocationsDataTable;


class SettingsController extends Controller
{


    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data['pageTitle'] = 'General Settings';
        $data['user'] = Auth::user();
        $data['countries'] = Country::all();
        // $data['states'] = State::all();
        // $data['cities'] = City::all();
        // $data['settings'] = Settings::all();

        // Only load states for the user's country if they have one selected
    if ($data['user']->country_id) {
        $data['states'] = State::where('country_id', $data['user']->country_id)->get();
    } else {
        $data['states'] = collect(); // Empty collection
    }
    
    // Only load cities for the user's state if they have one selected
    if ($data['user']->state_id) {
        $data['cities'] = City::where('state_id', $data['user']->state_id)->get();
    } else {
        $data['cities'] = collect(); // Empty collection
    }


        return view('admin.settings.general',$data);
    }

   





    public function updateGeneral(Request $request){
        $validated= $request->validate([
            'sitename' => 'required|string',
            'keywords' => 'nullable|string',
            'description' => 'nullable|string',
            'phone' => 'nullable|string',
            'email' => 'nullable|string',
            'office_address' => 'nullable|string',

            ]);

     

        foreach ($validated as $key => $value) {
            Setting::updateOrCreate(['name'=>$key], ['value'=>$value]);

            if($key=='sitename'){


                changeEnvironmentVariable('APP_NAME', $value);
            }
       }

       Cache::forget('get_settings');

       return redirect(route('admin.settings.general'))->with('success','General settings updated');
    }

public function updateProfile(Request $request)
{
    $user = Auth::user();
    
    $validated = $request->validate([
        'first_name' => 'required|string|max:255',
        'last_name' => 'required|string|max:255',
        'email' => [
            'nullable',
            'email',
            'max:255',
            Rule::unique('users', 'email')->ignore($user->id)
        ],
        'other_names' => 'nullable|string|max:255',
        'phone' => 'nullable|string|max:20',
        'address' => 'nullable|string|max:500',
        'country_id' => 'nullable|exists:countries,id',
        'state_id' => 'nullable|exists:states,id',
        'city_id' => 'nullable|exists:cities,id',
        'data_of_birth' => 'nullable|date|before:today',
        'gender' => 'nullable|in:male,female,other',
    ], [
        'first_name.required' => 'First name is required.',
        'last_name.required' => 'Last name is required.',
        'email.email' => 'Please enter a valid email address.',
        'email.unique' => 'This email address is already taken.',
        'data_of_birth.before' => 'Date of birth must be before today.',
    ]);

              // Load notification preferences and 2FA settings
    $notificationPreferences = $user->getNotificationPreferences();
    $twoFactorAuth = $user->getTwoFactorAuth();

    try {
        // Check if email is being changed (only if email is provided)
        if (isset($validated['email']) && !empty($validated['email']) && $user->email !== $validated['email']) {
            $validated['email_verified_at'] = null;
        }

        // Remove null and empty values to avoid database issues
        $validated = array_filter($validated, function($value) {
            return $value !== null && $value !== '';
        });

        $user->update($validated);
        
        return redirect()->route('admin.setting.general')->with('success', 'Profile updated successfully!');
        
    } catch (\Exception $e) {
        \Log::error('Admin profile update failed', [
            'user_id' => $user->id,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'data' => $validated
        ]);
        
        return redirect()->back()
            ->with('error', 'Failed to update profile. Please try again.')
            ->withInput();
    }
}



    //  public function updateImage( User $user, UploadService $uploadService, Request $request,)
    // {
    //     $request->validate([
    //     'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
    //     ]);


    //      if ($request->hasFile('profile_image') && $request->file('profile_image')->isValid()) {
    //     // Upload the file using the service
    //     $uploadedFile = $uploadService->fileUpload($request->file('profile_image'), "uploads/");
    //     if ($uploadedFile) {
    //         $user->update([
    //             'profile_image' => $uploadedFile['name'],   ]);
    //        // $update['profile_image'] = $uploadedFile['name'];
    //     }
    // }

    //     return redirect()->back()->with('success', 'Profile image updated successfully.');

    // }

public function updateImage(UploadService $uploadService, Request $request)
{
    // Get the authenticated user
    $user = auth()->user();
    
    // Check if user is authenticated
    if (!$user) {
        return redirect()->route('login')->with('error', 'Please login first.');
    }

    $request->validate([
        'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
    ]);

    \Log::info('Update image called', [
        'user_id' => $user->id,
        'has_file' => $request->hasFile('profile_image'),
        'file_valid' => $request->hasFile('profile_image') ? $request->file('profile_image')->isValid() : false
    ]);

    if ($request->hasFile('profile_image') && $request->file('profile_image')->isValid()) {
        // Delete old profile image if it exists
        if ($user->profile_image) {
            // Check multiple possible locations for the old image
            $possiblePaths = [
                storage_path('app/public/uploads/' . $user->profile_image),
                public_path('storage/uploads/' . $user->profile_image)
            ];
            
            foreach ($possiblePaths as $oldImagePath) {
                if (file_exists($oldImagePath)) {
                    unlink($oldImagePath);
                    \Log::info('Old profile image deleted', ['path' => $oldImagePath]);
                    break;
                }
            }
        }

        // Upload the file using the service
        $uploadedFile = $uploadService->fileUpload($request->file('profile_image'), "uploads/");
        
        \Log::info('Upload result', ['uploaded_file' => $uploadedFile]);

        if ($uploadedFile && ($uploadedFile['success'] ?? true)) {
            $originalProfileImage = $user->profile_image;
            
            // Use the processed filename
            $newFileName = $uploadedFile['filename'] ?? $uploadedFile['name'];
            
            $updateResult = $user->update([
                'profile_image' => $newFileName,
            ]);
            
            // Refresh the user model to get updated data
            $user->refresh();
            
            \Log::info('Database update result', [
                'update_success' => $updateResult,
                'user_profile_image_before' => $originalProfileImage,
                'user_profile_image_after' => $user->profile_image,
                'stored_filename' => $newFileName,
                'file_location' => $uploadedFile['storage_path'] ?? $uploadedFile['path'] ?? 'unknown'
            ]);
        } else {
            \Log::error('File upload failed', ['upload_result' => $uploadedFile]);
            return redirect()->back()->with('error', 'Failed to upload profile image.');
        }
    }

    return redirect()->back()->with('success', 'Profile image updated successfully.');
}






    public function updateGanalytics(Request $request){
        $validated= $request->validate([
            'google_analytics_enable' => 'nullable',
            'google_analytics_id' => 'nullable|string',
            'facebook_pixel_enable' => 'nullable',
            'facebook_pixel_id' => 'nullable|string',
            'google_map_api_enable' => 'nullable',
            'google_map_api_key' => 'nullable|string',

            ]);


            $validated['google_analytics_enable'] = $request->has('google_analytics_enable')?"1":"0";
            $validated['facebook_pixel_enable'] = $request->has('facebook_pixel_enable')?"1":"0";
            $validated['google_map_api_enable'] = $request->has('google_map_api_enable')?"1":"0";

            foreach ($validated as $key => $value) {
                Setting::updateOrCreate(['name'=>$key], ['value'=>$value]);

           }

           Cache::forget('get_settings');

           return redirect(route('admin.settings.general'))->with('success','Global Script & Analytics settings updated');
    }


    public function updateSocial(Request $request)
    {
        $validated= $request->validate([
            'facebook' => 'required|url',
            'instagram' => 'required|url',
            'twitter' => 'required|url',
            'tiktok' => 'required|url',
            'youtube' => 'required|url',

            ]);

            foreach ($validated as $key => $value) {
                Setting::updateOrCreate(['name'=>$key], ['value'=>$value]);

           }

           Cache::forget('get_settings');

           return redirect(route('admin.settings.general'))->with('success','Global Script & Analytics settings updated');
    }

    // public function uploadLogo(Request $request,UploadService $uploadService){

    //     $validator =Validator::make($request->all(),[
    //         'logo' => 'required',
    //         // 'file' => 'required|image|size:1024',
    //         'file'=>'required|max:1024|mimes:jpg,png,jpeg',
    //     ]);

    //     if ($validator->fails()) {
    //         return response()->json(['status' =>400, 'message' => $validator->errors()->first()],  400);
    //     }

    //     if($request->has('file')){
    //         $uploadedfile= $uploadService->fileUploadThumb($request->file('file'), "public/uploads/");

    //         // if(!empty($uploadedfile)){
    //         //     $uploadedfile['user_id']=Auth::user()->id;

    //         //     //save to media library
    //         //     $media=Media::create($uploadedfile);

    //         //     // insert into settings
    //         //     Setting::updateOrCreate(['name'=>$request->logo], ['value'=>$media->id]);

    //         //     Cache::forget('get_settings');
    //         //     return response()->json(['status' =>200, 'message' => "Upload successful"],  200);
    //         // }
    //     }else{
    //         return response()->json(['status' =>400, 'message' => "No file selected"],  400);
    //     }

    // }

    public function updateScripts(Request $request) {

        foreach ($request->except('_token') as $key => $value) {
            Setting::updateOrCreate(['name'=>$key], ['value'=>$value]);

       }

       Cache::forget('get_settings');

       return redirect(route('admin.settings.general'))->with('success','Global Script & Analytics settings updated');
    }


    public function updatePaystackSetting(Request $request){


        $validated= $request->validate([
            'PAYSTACK_ENABLE'=> 'boolean',
            'PAYSTACK_TEST_PUBLIC_KEY' => 'nullable|string',
            'PAYSTACK_TEST_SECRET_KEY' => 'nullable|string',
            'PAYSTACK_LIVE_PUBLIC_KEY' => 'nullable|string',
            'PAYSTACK_LIVE_SECRET_KEY' => 'nullable|string',
            'MERCHANT_EMAIL' => 'nullable|email',
            'PAYSTACK_MODE' => Rule::in(['test','live']),

            ]);


            $validated['PAYSTACK_ENABLE'] = $request->has('PAYSTACK_ENABLE')??"";
            foreach($validated as $key => $value){
                changeEnvironmentVariable($key, $value);
            }

            return redirect(route('admin.settings.payment.gateway'))->with('success','Paystack Pyament gateway updated');

    }
    /**
     * Show the form for creating a new resource.
     */
    public function paymentGateway()
    {

        $data['pageTitle'] = 'Payment Gateway Settings';
        return view('admin.settings.payment_gateway',$data);
    }


    /**
     * Show the form for creating a new resource.
     */
    public function emailSettings()
    {
        $data['pageTitle'] = 'Email Settings';

        return view('admin.settings.email-setting',$data);
    }
    public function updateSmtp(Request $request){


        $validated= $request->validate([
            'MAIL_MAILER'=> 'required|string',
            'MAIL_HOST' => 'required|string',
            'MAIL_PORT' => 'required|integer',
            'MAIL_USERNAME' => 'required|string',
            'MAIL_PASSWORD' => 'required|string',
            'MAIL_ENCRYPTION' => 'required|string',
            'MAIL_FROM_ADDRESS' => 'required|string',


            ]);


            $validated['PAYSTACK_ENABLE'] = $request->has('PAYSTACK_ENABLE')??"";
            foreach($validated as $key => $value){
                changeEnvironmentVariable($key, $value);
            }

            return redirect(route('email.setting'))->with('success','Email Settings updated');

    }


    /**
     * Store a newly created resource in storage.
     */
    // public function smtpTest(Request $request)
    // {
    //     $validator =Validator::make($request->all(),[
    //         'email' => 'required|email:rfc,dns',
    //     ],[
    //         'email.required' => 'The test Email address is required',
    //         'email.email' => 'The test Email address is must be a valid email address.',
    //     ]);

    //     if ($validator->fails()) {
    //         return response()->json(['status' =>400, 'message' => $validator->errors()->first()],  400);
    //     }
    //     try {

    //         Mail::to($request->email)->send(new TestMail());
    //       return response()->json(['status' =>200, 'message' =>"Email Sent successful"],  200);
    //     } catch (Exception $th) {
    //         return response()->json(['status' =>400, 'message' => $th->getMessage()],  400);
    //     }



    // }

    /**
     * Display the specified resource.
     */
    public function show(Setting $settings)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Setting $settings)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Setting $settings)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Setting $settings)
    {
        //
    }



    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = Auth::user();

        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->withErrors(['current_password' => 'Current password is incorrect.']);
        }

        try {
            $user->update([
                'password' => Hash::make($request->password)
            ]);

            return redirect()->back()->with('success', 'Password updated successfully!');
        } catch (Exception $e) {
            Log::error('Password update error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update password. Please try again.');
        }
    }

    // public function updateNotifications(Request $request)
    // {
    //     $user = Auth::user();
        
    //     $request->validate([
    //         'email_notifications' => 'boolean',
    //         'sms_notifications' => 'boolean',
    //         'push_notifications' => 'boolean',
    //         'device_login_alerts' => 'boolean',
    //         'account_activity_notifications' => 'boolean',
    //         'transaction_notifications' => 'boolean',
    //         'system_notifications' => 'boolean',
    //         'marketing_notifications' => 'boolean',
    //         'email_frequency' => 'in:immediate,daily,weekly,never',
    //     ]);

    //     try {
    //         $user->update([
    //             'email_notifications' => $request->has('email_notifications'),
    //             'sms_notifications' => $request->has('sms_notifications'),
    //             'push_notifications' => $request->has('push_notifications'),
    //             'device_login_alerts' => $request->has('device_login_alerts'),
    //             'account_activity_notifications' => $request->has('account_activity_notifications'),
    //             'transaction_notifications' => $request->has('transaction_notifications'),
    //             'system_notifications' => $request->has('system_notifications'),
    //             'marketing_notifications' => $request->has('marketing_notifications'),
    //             'email_frequency' => $request->email_frequency ?? 'immediate',
    //         ]);

    //         return redirect()->back()->with('success', 'Notification preferences updated successfully!');
    //     } catch (Exception $e) {
    //         Log::error('Notification update error: ' . $e->getMessage());
    //         return redirect()->back()->with('error', 'Failed to update notification preferences. Please try again.');
    //     }
    // }

    // public function update2FA(Request $request)
    // {
    //     $user = Auth::user();
        
    //     $request->validate([
    //         'sms_2fa' => 'boolean',
    //         'app_2fa' => 'boolean',
    //         'phone_2fa' => 'nullable|string|max:20',
    //     ]);

    //     try {
    //         $user->update([
    //             'sms_2fa' => $request->has('sms_2fa'),
    //             'app_2fa' => $request->has('app_2fa'),
    //             'phone_2fa' => $request->phone_2fa,
    //         ]);

    //         return redirect()->back()->with('success', 'Two-factor authentication settings updated successfully!');
    //     } catch (Exception $e) {
    //         Log::error('2FA update error: ' . $e->getMessage());
    //         return redirect()->back()->with('error', 'Failed to update 2FA settings. Please try again.');
    //     }
    // }

    // public function generateBackupCodes(Request $request)
    // {
    //     try {
    //         $user = Auth::user();
            
    //         // Generate 6 backup codes
    //         $codes = [];
    //         for ($i = 0; $i < 6; $i++) {
    //             $codes[] = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 4)) . '-' .
    //                       strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 4)) . '-' .
    //                       strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 4));
    //         }
            
    //         // Store encrypted backup codes
    //         $user->update([
    //             'backup_codes' => encrypt(json_encode($codes))
    //         ]);

    //         return response()->json([
    //             'success' => true,
    //             'codes' => $codes,
    //             'message' => 'Backup codes generated successfully!'
    //         ]);
    //     } catch (Exception $e) {
    //         Log::error('Backup codes generation error: ' . $e->getMessage());
    //         return response()->json([
    //             'success' => false,
    //             'message' => 'Failed to generate backup codes.'
    //         ], 500);
    //     }
    // }

            public function general()
        {
            $user = Auth::user();
            $countries = Country::where('status', 'active')->orderBy('name')->get();
            $states = collect();
            $cities = collect();
            $userBank = $user->userBanks()->first();
            
            // Load notification preferences and 2FA settings
            $notificationPreferences = $user->getNotificationPreferences();
            $twoFactorAuth = $user->getTwoFactorAuth();
            
            // Load states and cities if user has them selected
            if ($user->country_id) {
                $states = State::where('country_id', $user->country_id)
                            ->where('status', 'active')
                            ->orderBy('name')
                            ->get();
            }
            
            if ($user->state_id) {
                $cities = City::where('state_id', $user->state_id)
                            ->where('status', 'active')
                            ->orderBy('name')
                            ->get();
            }

            return view('admin.settings.general', compact(
                'user', 'countries', 'states', 'cities', 'userBank', 
                'notificationPreferences', 'twoFactorAuth'
            ));
        }

        public function updateNotifications(Request $request)
        {
            $user = Auth::user();
            
            $request->validate([
                'email_notifications' => 'boolean',
                'sms_notifications' => 'boolean',
                'push_notifications' => 'boolean',
                'device_login_alerts' => 'boolean',
                'account_activity_notifications' => 'boolean',
                'transaction_notifications' => 'boolean',
                'system_notifications' => 'boolean',
                'marketing_notifications' => 'boolean',
                'email_frequency' => 'in:immediate,daily,weekly,never',
            ]);

            try {
                $preferences = $user->getNotificationPreferences();
                
                $preferences->update([
                    'email_notifications' => $request->has('email_notifications'),
                    'sms_notifications' => $request->has('sms_notifications'),
                    'push_notifications' => $request->has('push_notifications'),
                    'device_login_alerts' => $request->has('device_login_alerts'),
                    'account_activity_notifications' => $request->has('account_activity_notifications'),
                    'transaction_notifications' => $request->has('transaction_notifications'),
                    'system_notifications' => $request->has('system_notifications'),
                    'marketing_notifications' => $request->has('marketing_notifications'),
                    'email_frequency' => $request->email_frequency ?? 'immediate',
                ]);

                return redirect()->back()->with('success', 'Notification preferences updated successfully!');
            } catch (Exception $e) {
                Log::error('Notification update error: ' . $e->getMessage());
                return redirect()->back()->with('error', 'Failed to update notification preferences. Please try again.');
            }
        }

        public function update2FA(Request $request)
        {
            $user = Auth::user();
            
            $request->validate([
                'sms_2fa' => 'boolean',
                'app_2fa' => 'boolean',
                'phone_2fa' => 'nullable|string|max:20',
            ]);

            try {
                $twoFactorAuth = $user->getTwoFactorAuth();
                
                $twoFactorAuth->update([
                    'sms_2fa' => $request->has('sms_2fa'),
                    'app_2fa' => $request->has('app_2fa'),
                    'phone_2fa' => $request->phone_2fa,
                ]);

                return redirect()->back()->with('success', 'Two-factor authentication settings updated successfully!');
            } catch (Exception $e) {
                Log::error('2FA update error: ' . $e->getMessage());
                return redirect()->back()->with('error', 'Failed to update 2FA settings. Please try again.');
            }
        }

        public function generateBackupCodes(Request $request)
        {
            try {
                $user = Auth::user();
                $twoFactorAuth = $user->getTwoFactorAuth();
                
                $codes = $twoFactorAuth->generateBackupCodes();

                return response()->json([
                    'success' => true,
                    'codes' => $codes,
                    'message' => 'Backup codes generated successfully!'
                ]);
            } catch (Exception $e) {
                Log::error('Backup codes generation error: ' . $e->getMessage());
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate backup codes.'
                ], 500);
            }
        }

        // public function updatePassword(Request $request)
        // {
        //     $user = Auth::user();
            
        //     $request->validate([
        //         'current_password' => 'required',
        //         'new_password' => 'required|string|min:8|confirmed',
        //     ]);
    
        //     if (!Hash::check($request->current_password, $user->password)) {
        //         return redirect()->back()->with('error', 'Current password is incorrect.');
        //     }
    
        //     $user->update([
        //         'password' => Hash::make($request->new_password),
        //     ]);
    
        //     return redirect()->back()->with('success', 'Password updated successfully!');
        // }



}
