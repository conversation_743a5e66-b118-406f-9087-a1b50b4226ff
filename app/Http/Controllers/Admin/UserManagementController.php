<?php

namespace App\Http\Controllers\Admin;

use Auth;
use App\Models\City;
use App\Models\Group;
use App\Models\State;
use App\Models\Country;
use App\Models\GroupMember;
use App\Models\GroupMessage;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\DataTables\Admin\UserGroupsDataTable;
use App\DataTables\Admin\UserManagementDataTable;
use App\Models\Contribution;

class UserManagementController extends Controller
{
    //

    public function viewUsers(UserManagementDataTable $dataTable)
    {
        // This method will return the DataTable view for users
        return $dataTable->render('admin.users.index');
    }

    public function showUser($id)
    {
        $countries = Country::all();
        $states = State::all();
        $cities = City::all();
        $user = Auth::user();
        return view('admin.users.show', compact('user', 'countries', 'states', 'cities'));
    }

    public function viewUserGroups($id, UserGroupsDataTable $dataTable)
    {
        
        $countries = Country::all();
        $states = State::all();
        $cities = City::all();
        $user = Auth::user();
        $groups = $user->groups;

        return $dataTable->render('admin.users.user-mgt.groups', compact('user', 'groups', 'countries', 'states', 'cities'));
    }

    public function viewUserTransactions($id)
    {
        
         $countries = Country::all();
        $states = State::all();
        $cities = City::all();
        $user = Auth::user();
        $groups = $user->groups; 
        return view('admin.users.user-mgt.transactions', compact('user', 'groups', 'countries', 'states', 'cities'));
    }

    public function viewActiveGroup($user, $group)
    {
        $user = Auth::user();
        // $group = $user->groups()->where('group_id', $group)->first();
        $group = Group::where('id', $group)->first();
        $groupMembers = GroupMember::where('group_id', $group->id)->with('user')->get();
        // $groupMembers = GroupMember::all();
       $messages = GroupMessage::forGroup($group->id)->with('user')->orderBy('created_at', 'asc')->limit(50)->get();
       $contributions = Contribution::where('group_id', $group->id)->with('user')->orderBy('created_at', 'desc')->get();


        // dd(
        //       $groupMembers
        // );

        // $admin = $groupMembers->firstWhere('user.user_type', 'admin');


        return view('admin.users.user-mgt.active-group', compact('user', 'group','groupMembers', 'messages', 'contributions'));
    }

 

 public function sendMessage(Request $request, $user, $group)
{
    try {
        $request->validate([
            'message' => 'required|string|max:1000'
        ]);

        $message = GroupMessage::create([
            'group_id' => $group,
            'user_id' => Auth::id(),
            'message' => $request->message,
            'type' => 'chat'
        ]);

        return response()->json([
            'success' => true,
            'message' => $message->load('user')
        ]);
        
    } catch (\Exception $e) {
        \Log::error('Message send error: ' . $e->getMessage());
        
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
}

public function getMessages($user, $group)
{
    try {
        $messages = GroupMessage::where('group_id', $group)
                               ->with('user')
                               ->orderBy('created_at', 'asc')
                               ->limit(50)
                               ->get();

        return response()->json($messages);
        
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
}


   
}
