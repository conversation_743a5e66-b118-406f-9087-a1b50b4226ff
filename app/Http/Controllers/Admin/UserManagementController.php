<?php

namespace App\Http\Controllers\Admin;

use Auth;
use App\Models\City;
use App\Models\Group;
use App\Models\State;
use App\Models\Country;
use App\Enums\GroupStatus;
use App\Models\GroupMember;
use App\Models\Contribution;
use App\Models\GroupMessage;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\DataTables\Admin\UserGroupsDataTable;
use App\DataTables\Admin\UserManagementDataTable;
use App\Enums\UserTypes;

class UserManagementController extends Controller
{
    //

    public function viewUsers(UserManagementDataTable $dataTable)
    {
        // This method will return the DataTable view for users
        return $dataTable->render('admin.users.index');
    }

    public function showUser($id)
    {
        $countries = Country::all();
        $states = State::all();
        $cities = City::all();
        $user = Auth::user();
        return view('admin.users.show', compact('user', 'countries', 'states', 'cities'));
    }

    public function viewUserGroups($id, UserGroupsDataTable $dataTable)
    {
        
        $countries = Country::all();
        $states = State::all();
        $cities = City::all();
        $user = Auth::user();
        $groups = $user->groups;

        return $dataTable->render('admin.users.user-mgt.groups', compact('user', 'groups', 'countries', 'states', 'cities'));
    }

    public function viewUserTransactions($id)
    {
        
         $countries = Country::all();
        $states = State::all();
        $cities = City::all();
        $user = Auth::user();
        $groups = $user->groups; 
        return view('admin.users.user-mgt.transactions', compact('user', 'groups', 'countries', 'states', 'cities'));
    }

    public function viewActiveGroup($user, $group)
    {
        $user = Auth::user();
        // $group = $user->groups()->where('group_id', $group)->first();
        $group = Group::where('id', $group)->first();
        // $groupMembers = GroupMember::where('group_id', $group->id)->where('status', 'active')->with('user')->get();
        // $groupMembers = GroupMember::all();
       $messages = GroupMessage::forGroup($group->id)->with('user')->orderBy('created_at', 'asc')->limit(50)->get();
       $contributions = Contribution::where('group_id', $group->id)->with('user')->orderBy('created_at', 'desc')->get();

       // Separate pending requests from active members
    //   $pendingRequests = GroupMember::where('group_id', $group->id)->where('status', GroupStatus::Pending->value)->with('user', UserTypes::User->value)->orderBy('created_at', 'asc');

         $pendingRequests = GroupMember::where('group_id', $group->id)->where('status', GroupStatus::Pending->value)->whereHas('user', function ($query) {$query->where('user_type', UserTypes::User->value); })
    ->with('user')->orderBy('created_at', 'asc')->get();


    

        $groupMembers = GroupMember::where('group_id', $group->id)->where('status', 'active')->whereHas('user', fn($q) => $q->where('user_type', 'user'))->with('user')->get();

        $admin = GroupMember::where('group_id', $group->id)->where('status', 'active')->whereHas('user', fn($q) => $q->where('user_type', 'admin'))->with('user')->first();



        return view('admin.users.user-mgt.active-group', compact('user', 'group','groupMembers', 'messages', 'contributions', 'pendingRequests', 'admin'));
    }

 

 public function sendMessage(Request $request, $user, $group)
{
    try {
        $request->validate([
            'message' => 'required|string|max:1000'
        ]);

        $message = GroupMessage::create([
            'group_id' => $group,
            'user_id' => Auth::id(),
            'message' => $request->message,
            'type' => 'chat'
        ]);

        return response()->json([
            'success' => true,
            'message' => $message->load('user')
        ]);
        
    } catch (\Exception $e) {
        \Log::error('Message send error: ' . $e->getMessage());
        
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
}

public function getMessages($user, $group)
{
    try {
        $messages = GroupMessage::where('group_id', $group)
                               ->with('user')
                               ->orderBy('created_at', 'asc')
                               ->limit(50)
                               ->get();

        return response()->json($messages);
        
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
}


    public function approveGroupMember($user, $group, $memberId)
    {
        try {
            $groupMember = GroupMember::findOrFail($memberId);

            // Verify the member belongs to the specified group
            if ($groupMember->group_id != $group) {
                return response()->json(['success' => false, 'message' => 'Invalid group member.'], 400);
            }

            // Update status to active
            $groupMember->update([
                'status' => \App\Enums\GroupStatus::Active->value,
                'joined_at' => now()
            ]);

            // Update group member count
            $groupModel = Group::find($group);
            if ($groupModel) {
                $activeMembers = GroupMember::where('group_id', $group)
                    ->where('status', \App\Enums\GroupStatus::Active->value)
                    ->count();
                $groupModel->update(['number_of_numbers' => $activeMembers]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Member approved successfully.',
                'member' => $groupMember->load('user')
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to approve member.'], 500);
        }
    }

    public function declineGroupMember($user, $group, $memberId)
    {
        try {
            $groupMember = GroupMember::findOrFail($memberId);

            // Verify the member belongs to the specified group
            if ($groupMember->group_id != $group) {
                return response()->json(['success' => false, 'message' => 'Invalid group member.'], 400);
            }

            // Delete the membership request (or you could set status to 'declined' if you want to keep records)
            $groupMember->delete();

            return response()->json([
                'success' => true,
                'message' => 'Member request declined successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to decline member.'], 500);
        }
    }


    public function requestToJoinGroup($groupId)
{
    $user = Auth::user();

    $exists = GroupMember::where('group_id', $groupId)
        ->where('user_id', $user->id)
        ->first();

    if (!$exists) {
        GroupMember::create([
            'user_id' => $user->id,
            'group_id' => $groupId,
            'status' => 'pending',
        ]);
    }

    return back()->with('success', 'Request sent successfully');
}

// Approve member
    public function approve($id)
    {
        $member = GroupMember::findOrFail($id);
        $member->status = 'active';
        $member->save();

        return back()->with('success', 'User approved.');
    }

// Decline member
public function decline($id)
{
    $member = GroupMember::findOrFail($id);

    // Option 1: Soft delete or remove
    $member->delete();

    // Option 2: Mark as declined (optional)
    // $member->status = 'declined';
    // $member->save();

    return back()->with('success', 'Request declined.');
}




}



   
