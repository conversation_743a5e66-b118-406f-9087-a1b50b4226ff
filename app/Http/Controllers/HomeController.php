<?php

namespace App\Http\Controllers;

use App\Enums\Status;
use Illuminate\Http\Request;
use App\Models\IdentificationType;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        return view('home');
    }


      public function getIdentification(Request $request)
    {

        $idtype = IdentificationType::where('status', Status::Active->value)->get();

        if ($idtype) {
            return response()->json(['status' => 200, 'message' => 'success', 'data' => $idtype],  200);
        } else {
            return response()->json(['status' => 204, 'message' => 'No Data'],  204);
        }
    }
}
