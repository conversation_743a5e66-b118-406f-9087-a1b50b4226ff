<?php

namespace App\Http\Controllers\User;

use App\Models\City;
use App\Models\State;
use App\Models\Country;
use Illuminate\View\View;
use Illuminate\Http\Request;
use App\Services\UploadService;
use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\ProfileUpdateRequest;
use App\Http\Requests\Auth\UpdatePasswordRequest;

class UserProfileController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function settings(Request $request): View
    {
        // $users = User::where(["id"=>Auth::user()->id])->get();
        $user = Auth::user();
        $countries = Country::all();
        $states = State::all();
        $cities = City::all();
        return view('user.dashboard.setting.profile' , compact('user', 'countries', 'states', 'cities'));
    }

    public function update(User $user, ProfileUpdateRequest $request): RedirectResponse
    {

        $request->user()->fill($request->validated());

        if ($request->user()->isDirty('email')) {
            $request->user()->email_verified_at = null;
        }

        $request->user()->save();

        return Redirect::route('user.profile.settings', $user)->with('status', 'profile-updated');
    }


    public function updateProfileImage( User $user, UploadService $uploadService, Request $request,)
    {
        $request->validate([
        'profileimages' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

         if ($request->hasFile('profileimages') && $request->file('profileimages')->isValid()) {
        // Upload the file using the service
        $uploadedFile = $uploadService->fileUpload($request->file('profileimages'), "uploads/");
        if ($uploadedFile) {
            $user->update([
                'profileimages' => $uploadedFile['name'],   ]);
           // $update['profile_image'] = $uploadedFile['name'];
        }
    }

        return Redirect::route('user.profile.settings')->with('status', 'profile-updated');

    }


     /**
     * Update Password
     * change user password
     *
     * @param string $new_password new password
     * @param string $current_password current password
     * @param string $password_confirm Confirm password
     * @return Redirect
     *
     */

    public function UpdatePwd(UpdatePasswordRequest $request): RedirectResponse
    {

        // get user information from request

        $user =$request->user();

        //check if current password matches password in database
        if(password_verify($request->current_password,$user->password)){

            $user->password =  Hash::make($request->password);
            $user->save();

            //logout user
            Auth::logout();

            // invalidate session
           $request->session()->invalidate();
           $request->session()->regenerateToken();

           // redirect to login page
            return Redirect::route('user.profile.settings')->with('success', 'profile updated successfully');

        }else{
            return Redirect::route('user.profile.settings')->with('error', "Current password did not match");
        }


    }


    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validateWithBag('userDeletion', [
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }
}
