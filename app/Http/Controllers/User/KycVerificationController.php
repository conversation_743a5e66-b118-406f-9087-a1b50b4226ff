<?php

namespace App\Http\Controllers\User;

use App\Enums\TxStatus;
use Illuminate\Http\Request;
use App\Models\kycVerification;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Notifications\KycStatusNotification;

class KycVerificationController extends Controller
{
    //

    // public function updateStatus(Request $request, $id)
    // {

    //             $request->validate([
    //             'status' => 'required|in:' . implode(',', [
    //                 TxStatus::Pending->value,
    //                 TxStatus::Completed->value,
    //                 TxStatus::Cancelled->value,
    //                 TxStatus::Rejected->value
    //             ]),
    //             'description' => 'nullable|string|max:255',
    //         ]);

    //     $kyc = kycVerification::findOrFail($id);
    //     $oldStatus =  $kyc->status;

    //     $kyc->status = $request->status;
    //     $kyc->description = $request->description;
    //     $kyc->save();

    //     $user = $kyc->user;

    //     if ($oldStatus != $request->status && $user) {
    //         $user->notify(new KycStatusNotification($kyc));
    //     }

    //     return response()->json([
    //     'success' => true,
    //     'message' => 'Kyc verification status uploaded successfully'


    //     ]);
    // }

public function updateStatus(Request $request, $id)
{
    try {
        Log::info('KYC status update requested', [
            'kyc_id' => $id,
            'requested_status' => $request->status
        ]);

        $request->validate([
            'status' => 'required|in:' . implode(',', [
                TxStatus::Pending->value,
                TxStatus::Completed->value,
                TxStatus::Cancelled->value,
                TxStatus::Rejected->value
            ]),
            'description' => 'nullable|string|max:255',
        ]);

        $kyc = kycVerification::findOrFail($id);
        $oldStatus = $kyc->status;

        Log::info('KYC record found', [
            'kyc_id' => $kyc->id,
            'old_status' => $oldStatus,
            'user_id' => $kyc->user_id
        ]);

        $kyc->status = $request->status;
        $kyc->description = $request->description;
        $kyc->save();

        $user = $kyc->user;

        if (!$user) {
            Log::error('User not found for KYC', [
                'kyc_id' => $kyc->id,
                'user_id' => $kyc->user_id
            ]);
            return response()->json([
                'success' => false,
                'message' => 'User not found for this KYC record'
            ], 404);
        }

        if ($oldStatus != $request->status) {
            Log::info('Sending notification to user', [
                'user_id' => $user->id,
                'kyc_id' => $kyc->id,
                'old_status' => $oldStatus,
                'new_status' => $request->status
            ]);

            try {
                $user->notify(new KycStatusNotification($kyc));

                Log::info('Notification sent successfully', [
                    'user_id' => $user->id,
                    'kyc_id' => $kyc->id
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to send notification', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'KYC status updated but notification failed: ' . $e->getMessage()
                ], 500);
            }
        } else {
            Log::info('Status unchanged, no notification sent', [
                'kyc_id' => $kyc->id,
                'status' => $oldStatus
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'KYC verification status updated successfully'
        ]);
    } catch (\Exception $e) {
        Log::error('Error updating KYC status', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        return response()->json([
            'success' => false,
            'message' => 'Error updating KYC status: ' . $e->getMessage()
        ], 500);
    }
}
}
