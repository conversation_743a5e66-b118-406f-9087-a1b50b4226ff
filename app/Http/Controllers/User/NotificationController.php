<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Models\kycVerification;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    //

     public function markAsRead($id)
    {
        $notification = Auth::user()->notifications()->findOrFail($id);
        $notification->markAsRead();

        // If it's a KYC notification, redirect to KYC details
        if (isset($notification->data['kyc_id'])) {
            return redirect()->route('kyc.details', $notification->data['kyc_id']);
        }

        return redirect()->back();
    }

       public function markAllAsRead()
    {
        Auth::user()->unreadNotifications->markAsRead();
        return redirect()->back();
    }

    public function show($id)
{
    $kyc = kycVerification::findOrFail($id);
    return view('user.kyc.details', compact('kyc'));
}




        public function checkNewNotifications()
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['hasNewNotifications' => false]);
        }

        $count = $user->unreadNotifications()->count();

        return response()->json([
            'hasNewNotifications' => $count > 0,
            'count' => $count,
            'notifications' => $user->unreadNotifications()->take(5)->get()->map(function ($n) {
                return [
                    'id' => $n->id,
                    'type' => class_basename($n->type),
                    'data' => $n->data,
                    'created_at' => $n->created_at->diffForHumans()
                ];
            })
        ]);
    }
}
