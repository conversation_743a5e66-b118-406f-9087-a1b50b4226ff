<?php

namespace App\Http\Requests;

use App\Models\User;
use App\Enums\Gender;
use App\Enums\UserTypes;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Foundation\Http\FormRequest;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'other_names' => ['required', 'string', 'max:100'],
            'address' => ['required', 'string', 'max:100'],
            // 'country_id' => ['required'],
            // 'state_id' => ['required'],
            // 'city_id' => ['required'],
            'profileimages' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'gender' => new Enum(Gender::class),
            'phone' => ['required', 'numeric'],
            'data_of_birth' => ['required', 'date','date_format:Y-m-d', 'before:'.Carbon::now()->subYears(11)->format('Y-m-d').'', 'after:'.Carbon::now()->subYears(70)->format('Y-m-d')],
            'gender' => new Enum(Gender::class),
            'role' =>['nullable',new Enum(UserTypes::class)],
        ];
    }
}
