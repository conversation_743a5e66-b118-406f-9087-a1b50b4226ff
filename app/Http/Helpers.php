<?php


// use App\Models\Media;
use App\Models\Media;
use App\Models\Country;
use App\Models\Setting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;



if (!function_exists('getBaseURL')) {
    function getBaseURL()
    {
       // $root = '//' . $_SERVER['HTTP_HOST'];
      //  $root .= str_replace(basename($_SERVER['SCRIPT_NAME']), '', $_SERVER['SCRIPT_NAME']);

        return url('/');
    }
}


if (!function_exists('getFileBaseURL')) {
    function getFileBaseURL()
    {
        if (env('FILESYSTEM_DRIVER') == 's3') {
            return env('AWS_URL') . '/';
        } else {
            return getBaseURL() . '/public/';
        }
    }
}

if (!function_exists('option_dropdown')) {
function option_dropdown($categories, $level = 1,$selected='') {
    $result = '';
    foreach ($categories as $category) {
        echo  "<option value='".$category->id."'";
        echo ($category->id==$selected) ? 'selected':'';
        echo ">". str_repeat("-", $level) . $category->name . "</option>";
        // if(!empty($category->children)){
        //     option_dropdown($category->children, ++$level,$selected='');
        // }
    }
}
}

/**
 * MiniCart Settings
 *
 * <AUTHOR> Tin <<EMAIL>>
 * @return mixed array $setting
 *
 */

if (!function_exists('get_setting')) {
    function get_setting($key, $default = null, $lang = false)
    {

        $settings = Cache::remember('get_settings', 86400, function () {
            return Setting::all();
        });

        $setting = $settings->where('name', $key)->first();


        return $setting == null ? $default : $setting->value;
    }
}


if (!function_exists('upload_url')) {
    /**
     * Generate an asset path for the application.
     *
     * @param string $path
     * @param bool|null $secure
     * @return bool|fasle $thumb
     * @return string
     */
    function upload_url($filename, bool $secure = false, bool $thumb = false)
    {
        if (env('FILESYSTEM_DRIVER') == 's3') {
            if($thumb){
                return Storage::disk('s3')->url('uploads/thumb/'.$filename);
            }else{
                return Storage::disk('s3')->url('uploads/'.$filename);
            }

        } else {
            if($thumb){
                return app('url')->asset('storage/uploads/thumb/' . $filename);
            }else{
                return app('url')->asset('storage/uploads/' . $filename);
            }

        }
    }
}


if (!function_exists('formatBytes')) {
    function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        // Uncomment one of the following alternatives
        $bytes /= pow(1024, $pow);
        // $bytes /= (1 << (10 * $pow));

        return round($bytes, $precision) . ' ' . $units[$pow];
    }
}

if(!function_exists('getLogo')){
    function getLogo(string $logo="mainlogo_white"){


       $logo=Media::whereId(get_setting($logo))->first();
     //return (object) ['large'=>upload_url('default-user.jpg',true,true),'thumb'=>upload_url('default-user.jpg',true,true)];


       return (object) ['large'=>upload_url($logo?->filename),'thumb'=>upload_url($logo?->filename,true,true)];
    }
}




if(!function_exists('getMedia')){
    function getMedia(int $id){
        return (object) ['large'=>upload_url('default-user.jpg',true,true),'thumb'=>upload_url('default-user.jpg',true,true)];


    //     if($id>0){
    //   //  $media=Media::whereId($id)->first();
    //     return (object) ['large'=>upload_url($media?->filename),'thumb'=>upload_url($media?->filename,true,true)];
    //     }else{
    //         return (object) ['large'=>upload_url('default-user.jpg',true,true),'thumb'=>upload_url('default-user.jpg',true,true)];
    //     }
    }
}
    /**
     * Get countries
     * @return array|object
     */
if(!function_exists('get_countries')){

    function get_countries($id=null, $status = null,){
        $countries = Cache::remember('get_countries', 86400, function () {
            return Country::all();
        });

        if($id!=null){
            $countries = $countries->where('id', $id)->first();
        }
        if($status!=null){
            $countries = $countries->where('status', $id)->first();
        }



        return $countries == null ? null : $countries;
    }
}
/**
     * overWrite the Env File values.
     * @param  String type
     * @param  String value
     * @return \Illuminate\Http\Response
     */
if (!function_exists('changeEnvironmentVariable')) {
function changeEnvironmentVariable($key,$value)
{
    $path = base_path('.env');

    if(is_bool(env($key)))
    {
        $old = env($key)? 'true' : 'false';
    }
    elseif(env($key)===null){
        $old = 'null';
    }
    else{
        $old = env($key);
    }


    $count=0;
     //   $path = base_path('.env');
        if (file_exists($path)) {

            $value = '"'.trim($value).'"';

            if(is_numeric(strpos(file_get_contents($path), $key)) && strpos(file_get_contents($path), $key) >= 0){

               $update= str_replace($key.'="'.env($key).'"', "$key=".$value, file_get_contents($path),$count);


                if ($count <= 0) {
                    $update= str_replace("$key=".$old, "$key=".$value, file_get_contents($path));
                }

                file_put_contents($path,$update);
            }
            else{

                file_put_contents($path, file_get_contents($path)."\r\n".$key.'='.$value);
            }
        }

}


}
/**
 * Invoice Number
 * @param string $string | default 1
 * @param int $str_len | default 9
 * @param string $prefix | default STORM
 *
 */

if(!function_exists('inoviceNumber')){
    function inoviceNumber($string='1', $str_len = 9, $prefix = 'STORM')
    {
        $txno = str_pad($string, $str_len, '0', STR_PAD_LEFT);
        $txno = $prefix . $txno;
        return $txno;

    }
}

/**
 * String wrap both sides of a string with a character.
 * @param string $string
 * @param string $char
 * @return string
 *
 */
if (!function_exists('str_wrap')) {
function str_wrap($string = '', $char = '"')
{
    return str_pad($string, strlen($string) + 2, $char, STR_PAD_BOTH);
}
}

/**
* Get Next auto increment
* @param string $table Name
* @reutn Get next auto increment
*
*/

if(!function_exists('nextAutoIdentifier')){
    function nextAutoIdentifier(string $table){
        $dbtx = DB::select("SHOW TABLE STATUS LIKE '{$table}'");
      // return $dbtx[0];
        $nextID = $dbtx[0]->Auto_increment;

        return $nextID;
    }
}

if(!function_exists('getServerInfo')){


    function getServerInfo(){
        return [
            'PHP Version' => phpversion(),
            'Server OS' => php_uname()??'Unknown',
            'Server Name' => $_SERVER['SERVER_NAME'],
            'Server Software' => $_SERVER['SERVER_SOFTWARE'],
            'Database Info' => DB::selectOne('SELECT VERSION() as version')->version,
            'CPU Load' =>getCpuLoadPercentage(),
            'Storage Info' =>".Used Space:".getDiskSize()['used']." Free Space:".getDiskSize()['free']."Total Space:".getDiskSize()['size'],
            'Server IP' => $_SERVER['SERVER_ADDR']??'127.0.0.1',
        ];
    }

}

// Convert bytes to a more readable format (e.g., GB)
if(!function_exists('formatSize')){
function formatSize($bytes) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    $power = $bytes > 0 ? floor(log($bytes, 1024)) : 0;
    return number_format($bytes / pow(1024, $power), 2, '.', ',') . ' ' . $units[$power];
}

}
    /**
     * Return harddisk infos.
     *
     * @param sring $path Drive or path
     * @return array Disk info
     */
    if(!function_exists('getDiskSize')){
    function getDiskSize($path = '/')
    {

         if (PHP_OS == 'WINNT') {
            $path = "C:";
            $disk = disk_total_space($path);
            $free = disk_free_space($path);
            $used = $disk - $free;
            return [
                'size' => formatSize($disk),
                'free' => formatSize($free),
                'used' => formatSize($used)
            ];
         }else{

            $disk = disk_total_space($path);
            $free = disk_free_space($path);
            $used = $disk - $free;
            return [
                'size' => formatSize($disk),
                'free' => formatSize($free),
                'used' => formatSize($used)
            ];
         }
        // $result = array();
        // $result['size'] = 0;
        // $result['free'] = 0;
        // $result['used'] = 0;
        // if (PHP_OS == 'WINNT') {

        //     $lines = null;
        //     exec('wmic logicaldisk get FreeSpace^,Name^,Size /Value', $lines);
        //     foreach ($lines as $index => $line) {
        //         if ($line != "Name=$path") {
        //             continue;
        //         }
        //         $result['free'] = explode('=', $lines[$index - 1])[1];
        //         $result['size'] = explode('=', $lines[$index + 1])[1];
        //         $result['used'] = $result['size'] - $result['free'];
        //         break;
        //     }
        // } else {
        //     $lines = null;
        //     exec(sprintf('df /P %s', $path), $lines);
        //     foreach ($lines as $index => $line) {
        //         if ($index != 1) {
        //             continue;
        //         }
        //         $values = preg_split('/\s{1,}/', $line);
        //         $result['size'] = $values[1] * 1024;
        //         $result['free'] = $values[3] * 1024;
        //         $result['used'] = $values[2] * 1024;
        //         break;
        //     }
        // }
        // return $result;
    }
}
    /**
     * Get CPU Load Percentage.
     *
     * @return float load percentage
     */

     if(!function_exists('getCpuLoadPercentage')){
     function getCpuLoadPercentage()
    {
        $result = -1;
        $lines = null;
        if (PHP_OS == 'WINNT') {
            $matches = null;
            exec('wmic.exe CPU get loadpercentage /Value', $lines);
            if (preg_match('/^LoadPercentage\=(\d+)$/', $lines[2], $matches)) {
                $result = $matches[1];
            }
        } else {
            // https://github.com/Leo-G/DevopsWiki/wiki/How-Linux-CPU-Usage-Time-and-Percentage-is-calculated
            //$tests = array();
            //$tests[] = 'cpu  3194489 5224 881924 305421192 603380 76 52143 106209 0 0';
            //$tests[] = 'cpu  3194490 5224 881925 305422568 603380 76 52143 106209 0 0';
            $checks = array();
            foreach (array(0, 1) as $i) {
                $cmd = '/proc/stat';
                #$cmd = 'grep \'cpu \' /proc/stat <(sleep 1 && grep \'cpu \' /proc/stat) | awk -v RS="" \'{print ($13-$2+$15-$4)*100/($13-$2+$15-$4+$16-$5) "%"}\'';
                #exec($cmd, $lines);
                $lines = array();
                $fh = fopen($cmd, 'r');
                while ($line = fgets($fh)) {
                    $lines[] = $line;
                }
                fclose($fh);
                //$lines = array($tests[$i]);
                foreach ($lines as $line) {
                    $ma = array();
                    if (!preg_match('/^cpu  (\d+) (\d+) (\d+) (\d+) (\d+) (\d+) (\d+) (\d+) (\d+) (\d+)$/', $line, $ma)) {
                        continue;
                    }
                    /**
                     * The meanings of the columns are as follows, from left to right:
                      1st column : user = normal processes executing in user mode
                      2nd column : nice = niced processes executing in user mode
                      3rd column : system = processes executing in kernel mode
                      4th column : idle = twiddling thumbs
                      5th column : iowait = waiting for I/O to complete
                      6th column : irq = servicing interrupts
                      7th column : softirq = servicing softirqs
                      8th column:
                      9th column:
                      Calculation:
                      sum up all the columns in the 1st line "cpu" :
                      ( user + nice + system + idle + iowait + irq + softirq )
                      this will yield 100% of CPU time
                      calculate the average percentage of total 'idle' out of 100% of CPU time :
                      ( user + nice + system + idle + iowait + irq + softirq ) = 100%
                      ( idle ) = X %
                      TOTAL USER = %user + %nice
                      TOTAL CPU = %user + %nice + %system
                      TOTAL IDLE = %iowait + %steal + %idle
                     */
                    $total = $ma[1] + $ma[2] + $ma[3] + $ma[4] + $ma[5] + $ma[6] + $ma[7] + $ma[8] + $ma[9];
                    //$totalCpu = $ma[1] + $ma[2] + $ma[3];
                    //$result = (100 / $total) * $totalCpu;
                    $ma['total'] = $total;
                    $checks[] = $ma;
                    break;
                }
                if ($i == 0) {
                    // Wait before checking again.
                    sleep(1);
                }
            }
            // Idle - prev idle
            $diffIdle = $checks[1][4] - $checks[0][4];
            // Total - prev total
            $diffTotal = $checks[1]['total'] - $checks[0]['total'];
            // Usage in %
            $diffUsage = (1000 * ($diffTotal - $diffIdle) / $diffTotal + 5) / 10;
            $result = $diffUsage;
        }
        return (float) $result;
    }
}

