<?php

namespace App\Models;


use App\Enums\TxStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class kycVerification extends Model
{
    use HasFactory;

    protected $guarded = ['id'];
    protected $casts = [
        'status' => TxStatus::class,
    ];

    protected $fillable=[
        'identification_type_id',
        'means_of_verification',
        'proof_address',
        'user_id',

    ];

    public function getMeansOfVerificationAttribute()
    {
     return asset('/storage/uploads/kyc/'.$this->attributes['means_of_verification']) ;
    }
    public function user(){
        return $this->belongsTo(User::class);
    }

    public function identificationType (){
        return $this->belongsTo(IdentificationType::class);
    }
}
