<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserNotificationPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'email_notifications',
        'sms_notifications',
        'push_notifications',
        'device_login_alerts',
        'account_activity_notifications',
        'transaction_notifications',
        'system_notifications',
        'marketing_notifications',
        'email_frequency',
    ];

    protected $casts = [
        'email_notifications' => 'boolean',
        'sms_notifications' => 'boolean',
        'push_notifications' => 'boolean',
        'device_login_alerts' => 'boolean',
        'account_activity_notifications' => 'boolean',
        'transaction_notifications' => 'boolean',
        'system_notifications' => 'boolean',
        'marketing_notifications' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get default notification preferences
     */
    public static function getDefaults()
    {
        return [
            'email_notifications' => true,
            'sms_notifications' => false,
            'push_notifications' => true,
            'device_login_alerts' => true,
            'account_activity_notifications' => true,
            'transaction_notifications' => true,
            'system_notifications' => false,
            'marketing_notifications' => false,
            'email_frequency' => 'immediate',
        ];
    }
}
