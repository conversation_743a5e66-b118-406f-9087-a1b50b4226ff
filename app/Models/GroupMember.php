<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GroupMember extends Model
{
    //

    protected $fillable = [
        'group_id',
        'user_id',
        'status', // e.g., 'active', 'pending', 'inactive'
        'joined_at', // Date when the member joined the group
    ];



    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function contributions()
{
    return $this->hasMany(Contribution::class, 'user_id', 'user_id')->where('group_id', $this->group_id);
}
}
