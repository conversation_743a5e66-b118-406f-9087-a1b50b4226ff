<?php

namespace App\Models;

use App\Enums\Status;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class IdentificationType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'status',
    ];

    protected static function boot()
{
    parent::boot();
    
    static::creating(function ($identityType) {
        $identityType->slug = Str::slug($identityType->name);
    });
}


    protected $guarded = ['id'];

    protected $casts = [
        'status' => Status::class,
    ];

    public function kyc(){
        return $this->hasMany(kycVerification::class);
    }

    
}
