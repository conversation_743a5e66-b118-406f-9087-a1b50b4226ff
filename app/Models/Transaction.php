<?php

namespace App\Models;

use App\Enums\CashType;
use App\Enums\TxStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Transaction extends Model
{
    use HasFactory;

    protected $guarded = ['id'];
    protected $casts = [

        'status' => TxStatus::class,
          'data' => 'array',

    ];
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function bank()
    {
        return $this->belongsTo(Bank::class);
    }


}
