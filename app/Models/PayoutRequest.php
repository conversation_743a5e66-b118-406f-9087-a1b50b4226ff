<?php

namespace App\Models;

use App\Enums\TxStatus;
use Illuminate\Database\Eloquent\Model;

class PayoutRequest extends Model
{
    //

    protected $fillable = [
        'user_id',
        'group_id',
        'payout_amount',
        'currency',
        'status',
        'requested_at',
    ];

    protected $casts = [
        'status' => TxStatus::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function members()
    {
        return $this->hasMany(GroupMember::class, 'group_id', 'user_id');
    }


        public static function currencySymbols()
    {
        return [
            'Naira' => '₦', // Naira
            'Yuan' => '¥', // <PERSON> (Chinese Yuan Renminbi)
            
        ];
    }


}
