<?php

namespace App\Models;

use App\Enums\TxStatus;
use Illuminate\Database\Eloquent\Model;

class Contribution extends Model
{
    //

    protected $casts = [

        'status' => TxStatus::class,

    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }   

    public function member()
    {
        return $this->belongsTo(GroupMember::class);
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }



}
