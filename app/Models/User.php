<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\UserTypes;
use App\Enums\UserStatus;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes;



    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
        protected $guarded = [];

        protected $casts = [
            'status' => UserStatus::class,
            'last_login' => 'datetime',
            'userr_type' => UserTypes::class,

        ];

    protected $fillable = [
       'first_name',
        'last_name',
        'other_names',
        'country_id',
        'state_id',
        'city_id',
        'phone',
        'username',
        'gender',
        'address',
        'data_of_birth',
        // 'profileimages',
        'profile_image',
        'email',
        'password',
        'status',
        'user_type',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function getRedirectRoute()
    {

        return match($this->user_type) {
            UserTypes::Admin->value => route('admin.dashboard'),
            UserTypes::User->value => route('user.dashboard'),

            };
    }

    public function kycVerification()
    {
        return $this->hasOne(kycVerification::class);
    }

    public function groups()
    {
        return $this->belongsToMany(Group::class, 'group_members', 'user_id', 'group_id')
            ->withPivot('status', 'created_at', 'updated_at');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function contributions()
    {
        return $this->hasMany(Contribution::class, 'user_id');
    }
    public function groupMembers()
    {
        return $this->hasMany(GroupMember::class, 'user_id');  
    }

    public function bankAccounts()
    {
        return $this->hasOne(BankAccount::class, 'user_id');
    }

    public function bank()
    {
        return $this->hasOne(Bank::class, 'id', 'bank_id');
    }

        /**
     * Get the user's notification preferences
     */
    public function notificationPreferences()
    {
        return $this->hasOne(UserNotificationPreference::class);
    }

      /**
     * Get the user's two-factor authentication settings
     */
    public function twoFactorAuth()
    {
        return $this->hasOne(UserTwoFactorAuth::class);
    }

        /**
     * Get or create notification preferences with defaults
     */
    public function getNotificationPreferences()
    {
        return $this->notificationPreferences()->firstOrCreate(
            ['user_id' => $this->id],
            UserNotificationPreference::getDefaults()
        );
    }

        /**
     * Get or create two-factor auth settings
     */
    public function getTwoFactorAuth()
    {
        return $this->twoFactorAuth()->firstOrCreate(['user_id' => $this->id]);
    }

        /**
     * Check if user has 2FA enabled
     */
    public function hasTwoFactorEnabled(): bool
    {
        return $this->twoFactorAuth?->isTwoFactorEnabled() ?? false;
    }

        public function groupAdmin()
    {
        return $this->hasMany(GroupAdmin::class);
    }

      public function isGroupAdmin()
    {
        return GroupAdmin::where('user_id', $this->id)->exists();
    }



}
