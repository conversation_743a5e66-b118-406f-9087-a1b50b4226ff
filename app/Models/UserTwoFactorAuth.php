<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class UserTwoFactorAuth extends Model
{
    use HasFactory;

    protected $table = 'user_two_factor_auth';

    protected $fillable = [
        'user_id',
        'sms_2fa',
        'app_2fa',
        'phone_2fa',
        'two_factor_secret',
        'backup_codes',
        'backup_codes_generated_at',
        'two_factor_confirmed',
        'two_factor_confirmed_at',
    ];

    protected $casts = [
        'sms_2fa' => 'boolean',
        'app_2fa' => 'boolean',
        'two_factor_confirmed' => 'boolean',
        'backup_codes_generated_at' => 'datetime',
        'two_factor_confirmed_at' => 'datetime',
    ];

    protected $hidden = [
        'two_factor_secret',
        'backup_codes',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the backup codes
     */
    protected function backupCodes(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? json_decode(decrypt($value), true) : null,
            set: fn ($value) => $value ? encrypt(json_encode($value)) : null,
        );
    }

    /**
     * Check if 2FA is enabled
     */
    public function isTwoFactorEnabled(): bool
    {
        return $this->sms_2fa || $this->app_2fa;
    }

    /**
     * Generate new backup codes
     */
    public function generateBackupCodes(): array
    {
        $codes = [];
        for ($i = 0; $i < 8; $i++) {
            $codes[] = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 4)) . '-' .
                      strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 4));
        }

        $this->update([
            'backup_codes' => $codes,
            'backup_codes_generated_at' => now(),
        ]);

        return $codes;
    }
}
