<?php

namespace App\Models;

use App\Enums\UserTypes;
use App\Enums\PageStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Page extends Model
{
    //
    use HasFactory;


    protected $guarded = ['id'];

    protected $casts = [

        'deletable' => UserTypes::class,
        'status' => PageStatus::class,

    ];
    public function user(){
        return $this->hasOne(User::class);
    }
}
