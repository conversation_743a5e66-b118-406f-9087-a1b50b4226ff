<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Group extends Model
{
    //
    protected $fillable = [
        'group_name',
        'description',
        'amount',
        'frequency', // Frequency of contributions
        'max_number_of_member', // Maximum allowed members
        'status', // Status of the group
        'currency',
        'last_activity_date', // Date of the last activity
        'group_rules',
        'orderOfPayment',
        'status',
        'group_link',
        
    ];

    protected $casts = [
        'status' => 'string',
        'frequency' => 'string', // Cast to string
    ];

        public static function currencySymbols()
    {
        return [
            'Naira' => '₦', // Naira
            'Yuan' => '¥', // <PERSON> (Chinese Yuan Renminbi)
            
        ];
    }


    // app/Models/Group.php

    // public function members()
    // {
    //     return $this->hasMany(GroupMember::class);
    // }

    public function members()
{
    return $this->belongsToMany(User::class, 'group_members')->withPivot('status');
}


    public function contributions()
    {
        return $this->hasMany(Contribution::class);
    }

    public function messages()
    {
        return $this->hasMany(GroupMessage::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

        public function admins()
    {
        return $this->belongsToMany(User::class, 'group_admins');
    }


}
