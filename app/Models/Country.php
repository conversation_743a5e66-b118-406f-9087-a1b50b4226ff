<?php

namespace App\Models;

use App\Enums\Status;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Country extends Model
{
    use HasFactory;
    protected $guarded = ['id'];

    protected $casts = [

        'status' => Status::class,

    ];
    public function user(){
        return $this->belongsTo(User::class);
    }


    public function state(){
        return $this->hasMany(State::class);
    }
    public function student(){
        return $this->belongsTo(Student::class);
    }

}
