<?php

namespace Database\Seeders;

use App\Models\User;
use App\Enums\IsAdmin;
use App\Enums\UserTypes;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::create([
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'user_type' => UserTypes::Admin->value,

            'password' => Hash::make('12345678'),
        ]);
    }
}






