<?php

namespace Database\Seeders;

use DB;
use Illuminate\Database\Seeder;

class CountriesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {


        DB::table('countries')->delete();

        \DB::table('countries')->insert(array(
            0 =>
            array(
                'id' => 1,
                'code' => 'AF',
                'name' => 'Afghanistan',

                'status' => '0',



            ),
            1 =>
            array(
                'id' => 2,
                'code' => 'AL',
                'name' => 'Albania',

                'status' => '0',



            ),
            2 =>
            array(
                'id' => 3,
                'code' => 'DZ',
                'name' => 'Algeria',

                'status' => '0',



            ),
            3 =>
            array(
                'id' => 4,
                'code' => 'AS',
                'name' => 'American Samoa',

                'status' => '0',



            ),
            4 =>
            array(
                'id' => 5,
                'code' => 'AD',
                'name' => 'Andorra',

                'status' => '0',



            ),
            5 =>
            array(
                'id' => 6,
                'code' => 'AO',
                'name' => 'Angola',

                'status' => '0',



            ),
            6 =>
            array(
                'id' => 7,
                'code' => 'AI',
                'name' => 'Anguilla',

                'status' => '0',



            ),
            7 =>
            array(
                'id' => 8,
                'code' => 'AQ',
                'name' => 'Antarctica',

                'status' => '0',



            ),
            8 =>
            array(
                'id' => 9,
                'code' => 'AG',
                'name' => 'Antigua And Barbuda',

                'status' => '0',



            ),
            9 =>
            array(
                'id' => 10,
                'code' => 'AR',
                'name' => 'Argentina',

                'status' => '0',

            ),
            10 =>
            array(
                'id' => 11,
                'code' => 'AM',
                'name' => 'Armenia',

                'status' => '0',


            ),
            11 =>
            array(
                'id' => 12,
                'code' => 'AW',
                'name' => 'Aruba',

                'status' => '0',

            ),
            12 =>
            array(
                'id' => 13,
                'code' => 'AU',
                'name' => 'Australia',
                'status' => '0',

            ),
            13 =>
            array(
                'id' => 14,
                'code' => 'AT',
                'name' => 'Austria',

                'status' => '0',



            ),
            14 =>
            array(
                'id' => 15,
                'code' => 'AZ',
                'name' => 'Azerbaijan',

                'status' => '0',



            ),
            15 =>
            array(
                'id' => 16,
                'code' => 'BS',
                'name' => 'Bahamas The',

                'status' => '0',



            ),
            16 =>
            array(
                'id' => 17,
                'code' => 'BH',
                'name' => 'Bahrain',

                'status' => '0',



            ),
            17 =>
            array(
                'id' => 18,
                'code' => 'BD',
                'name' => 'Bangladesh',

                'status' => '0',



            ),
            18 =>
            array(
                'id' => 19,
                'code' => 'BB',
                'name' => 'Barbados',

                'status' => '0',



            ),
            19 =>
            array(
                'id' => 20,
                'code' => 'BY',
                'name' => 'Belarus',

                'status' => '0',



            ),
            20 =>
            array(
                'id' => 21,
                'code' => 'BE',
                'name' => 'Belgium',

                'status' => '0',



            ),
            21 =>
            array(
                'id' => 22,
                'code' => 'BZ',
                'name' => 'Belize',

                'status' => '0',



            ),
            22 =>
            array(
                'id' => 23,
                'code' => 'BJ',
                'name' => 'Benin',

                'status' => '0',



            ),
            23 =>
            array(
                'id' => 24,
                'code' => 'BM',
                'name' => 'Bermuda',

                'status' => '0',



            ),
            24 =>
            array(
                'id' => 25,
                'code' => 'BT',
                'name' => 'Bhutan',
                'status' => '0',



            ),
            25 =>
            array(
                'id' => 26,
                'code' => 'BO',
                'name' => 'Bolivia',

                'status' => '0',



            ),
            26 =>
            array(
                'id' => 27,
                'code' => 'BA',
                'name' => 'Bosnia and Herzegovina',

                'status' => '0',



            ),
            27 =>
            array(
                'id' => 28,
                'code' => 'BW',
                'name' => 'Botswana',

                'status' => '0',



            ),
            28 =>
            array(
                'id' => 29,
                'code' => 'BV',
                'name' => 'Bouvet Island',

                'status' => '0',



            ),
            29 =>
            array(
                'id' => 30,
                'code' => 'BR',
                'name' => 'Brazil',

                'status' => '0',



            ),
            30 =>
            array(
                'id' => 31,
                'code' => 'IO',
                'name' => 'British Indian Ocean Territory',

                'status' => '0',



            ),
            31 =>
            array(
                'id' => 32,
                'code' => 'BN',
                'name' => 'Brunei',

                'status' => '0',



            ),
            32 =>
            array(
                'id' => 33,
                'code' => 'BG',
                'name' => 'Bulgaria',

                'status' => '0',



            ),
            33 =>
            array(
                'id' => 34,
                'code' => 'BF',
                'name' => 'Burkina Faso',

                'status' => '0',



            ),
            34 =>
            array(
                'id' => 35,
                'code' => 'BI',
                'name' => 'Burundi',

                'status' => '0',



            ),
            35 =>
            array(
                'id' => 36,
                'code' => 'KH',
                'name' => 'Cambodia',

                'status' => '0',



            ),
            36 =>
            array(
                'id' => 37,
                'code' => 'CM',
                'name' => 'Cameroon',

                'status' => '0',



            ),
            37 =>
            array(
                'id' => 38,
                'code' => 'CA',
                'name' => 'Canada',

                'status' => '0',



            ),
            38 =>
            array(
                'id' => 39,
                'code' => 'CV',
                'name' => 'Cape Verde',

                'status' => '0',



            ),
            39 =>
            array(
                'id' => 40,
                'code' => 'KY',
                'name' => 'Cayman Islands',

                'status' => '0',



            ),
            40 =>
            array(
                'id' => 41,
                'code' => 'CF',
                'name' => 'Central African Republic',

                'status' => '0',



            ),
            41 =>
            array(
                'id' => 42,
                'code' => 'TD',
                'name' => 'Chad',

                'status' => '0',



            ),
            42 =>
            array(
                'id' => 43,
                'code' => 'CL',
                'name' => 'Chile',

                'status' => '0',



            ),
            43 =>
            array(
                'id' => 44,
                'code' => 'CN',
                'name' => 'China',

                'status' => '0',



            ),
            44 =>
            array(
                'id' => 45,
                'code' => 'CX',
                'name' => 'Christmas Island',

                'status' => '0',



            ),
            45 =>
            array(
                'id' => 46,
                'code' => 'CC',
                'name' => 'Cocos (Keeling) Islands',

                'status' => '0',



            ),
            46 =>
            array(
                'id' => 47,
                'code' => 'CO',
                'name' => 'Colombia',

                'status' => '0',



            ),
            47 =>
            array(
                'id' => 48,
                'code' => 'KM',
                'name' => 'Comoros',

                'status' => '0',



            ),
            48 =>
            array(
                'id' => 49,
                'code' => 'CG',
                'name' => 'Republic Of The Congo',

                'status' => '0',



            ),
            49 =>
            array(
                'id' => 50,
                'code' => 'CD',
                'name' => 'Democratic Republic Of The Congo',

                'status' => '0',



            ),
            50 =>
            array(
                'id' => 51,
                'code' => 'CK',
                'name' => 'Cook Islands',

                'status' => '0',



            ),
            51 =>
            array(
                'id' => 52,
                'code' => 'CR',
                'name' => 'Costa Rica',

                'status' => '0',



            ),
            52 =>
            array(
                'id' => 53,
                'code' => 'CI',
                'name' => 'Cote D\'Ivoire (Ivory Coast)',

                'status' => '0',



            ),
            53 =>
            array(
                'id' => 54,
                'code' => 'HR',
                'name' => 'Croatia (Hrvatska)',

                'status' => '0',



            ),
            54 =>
            array(
                'id' => 55,
                'code' => 'CU',
                'name' => 'Cuba',

                'status' => '0',



            ),
            55 =>
            array(
                'id' => 56,
                'code' => 'CY',
                'name' => 'Cyprus',

                'status' => '0',



            ),
            56 =>
            array(
                'id' => 57,
                'code' => 'CZ',
                'name' => 'Czech Republic',

                'status' => '0',



            ),
            57 =>
            array(
                'id' => 58,
                'code' => 'DK',
                'name' => 'Denmark',

                'status' => '0',



            ),
            58 =>
            array(
                'id' => 59,
                'code' => 'DJ',
                'name' => 'Djibouti',

                'status' => '0',



            ),
            59 =>
            array(
                'id' => 60,
                'code' => 'DM',
                'name' => 'Dominica',

                'status' => '0',



            ),
            60 =>
            array(
                'id' => 61,
                'code' => 'DO',
                'name' => 'Dominican Republic',

                'status' => '0',



            ),
            61 =>
            array(
                'id' => 62,
                'code' => 'TP',
                'name' => 'East Timor',

                'status' => '0',



            ),
            62 =>
            array(
                'id' => 63,
                'code' => 'EC',
                'name' => 'Ecuador',

                'status' => '0',



            ),
            63 =>
            array(
                'id' => 64,
                'code' => 'EG',
                'name' => 'Egypt',

                'status' => '0',



            ),
            64 =>
            array(
                'id' => 65,
                'code' => 'SV',
                'name' => 'El Salvador',

                'status' => '0',



            ),
            65 =>
            array(
                'id' => 66,
                'code' => 'GQ',
                'name' => 'Equatorial Guinea',

                'status' => '0',



            ),
            66 =>
            array(
                'id' => 67,
                'code' => 'ER',
                'name' => 'Eritrea',

                'status' => '0',



            ),
            67 =>
            array(
                'id' => 68,
                'code' => 'EE',
                'name' => 'Estonia',

                'status' => '0',



            ),
            68 =>
            array(
                'id' => 69,
                'code' => 'ET',
                'name' => 'Ethiopia',

                'status' => '0',



            ),
            69 =>
            array(
                'id' => 70,
                'code' => 'XA',
                'name' => 'External Territories of Australia',

                'status' => '0',



            ),
            70 =>
            array(
                'id' => 71,
                'code' => 'FK',
                'name' => 'Falkland Islands',

                'status' => '0',



            ),
            71 =>
            array(
                'id' => 72,
                'code' => 'FO',
                'name' => 'Faroe Islands',

                'status' => '0',



            ),
            72 =>
            array(
                'id' => 73,
                'code' => 'FJ',
                'name' => 'Fiji Islands',

                'status' => '0',



            ),
            73 =>
            array(
                'id' => 74,
                'code' => 'FI',
                'name' => 'Finland',

                'status' => '0',



            ),
            74 =>
            array(
                'id' => 75,
                'code' => 'FR',
                'name' => 'France',

                'status' => '0',



            ),
            75 =>
            array(
                'id' => 76,
                'code' => 'GF',
                'name' => 'French Guiana',

                'status' => '0',



            ),
            76 =>
            array(
                'id' => 77,
                'code' => 'PF',
                'name' => 'French Polynesia',
                'status' => '0',



            ),
            77 =>
            array(
                'id' => 78,
                'code' => 'TF',
                'name' => 'French Southern Territories',

                'status' => '0',



            ),
            78 =>
            array(
                'id' => 79,
                'code' => 'GA',
                'name' => 'Gabon',

                'status' => '0',



            ),
            79 =>
            array(
                'id' => 80,
                'code' => 'GM',
                'name' => 'Gambia The',

                'status' => '0',



            ),
            80 =>
            array(
                'id' => 81,
                'code' => 'GE',
                'name' => 'Georgia',

                'status' => '0',



            ),
            81 =>
            array(
                'id' => 82,
                'code' => 'DE',
                'name' => 'Germany',

                'status' => '0',



            ),
            82 =>
            array(
                'id' => 83,
                'code' => 'GH',
                'name' => 'Ghana',

                'status' => '0',



            ),
            83 =>
            array(
                'id' => 84,
                'code' => 'GI',
                'name' => 'Gibraltar',

                'status' => '0',



            ),
            84 =>
            array(
                'id' => 85,
                'code' => 'GR',
                'name' => 'Greece',

                'status' => '0',



            ),
            85 =>
            array(
                'id' => 86,
                'code' => 'GL',
                'name' => 'Greenland',

                'status' => '0',



            ),
            86 =>
            array(
                'id' => 87,
                'code' => 'GD',
                'name' => 'Grenada',

                'status' => '0',



            ),
            87 =>
            array(
                'id' => 88,
                'code' => 'GP',
                'name' => 'Guadeloupe',

                'status' => '0',



            ),
            88 =>
            array(
                'id' => 89,
                'code' => 'GU',
                'name' => 'Guam',

                'status' => '0',



            ),
            89 =>
            array(
                'id' => 90,
                'code' => 'GT',
                'name' => 'Guatemala',

                'status' => '0',



            ),
            90 =>
            array(
                'id' => 91,
                'code' => 'XU',
                'name' => 'Guernsey and Alderney',

                'status' => '0',



            ),
            91 =>
            array(
                'id' => 92,
                'code' => 'GN',
                'name' => 'Guinea',

                'status' => '0',



            ),
            92 =>
            array(
                'id' => 93,
                'code' => 'GW',
                'name' => 'Guinea-Bissau',

                'status' => '0',



            ),
            93 =>
            array(
                'id' => 94,
                'code' => 'GY',
                'name' => 'Guyana',

                'status' => '0',



            ),
            94 =>
            array(
                'id' => 95,
                'code' => 'HT',
                'name' => 'Haiti',

                'status' => '0',



            ),
            95 =>
            array(
                'id' => 96,
                'code' => 'HM',
                'name' => 'Heard and McDonald Islands',

                'status' => '0',



            ),
            96 =>
            array(
                'id' => 97,
                'code' => 'HN',
                'name' => 'Honduras',

                'status' => '0',



            ),
            97 =>
            array(
                'id' => 98,
                'code' => 'HK',
                'name' => 'Hong Kong S.A.R.',

                'status' => '0',



            ),
            98 =>
            array(
                'id' => 99,
                'code' => 'HU',
                'name' => 'Hungary',

                'status' => '0',



            ),
            99 =>
            array(
                'id' => 100,
                'code' => 'IS',
                'name' => 'Iceland',

                'status' => '0',



            ),
            100 =>
            array(
                'id' => 101,
                'code' => 'IN',
                'name' => 'India',

                'status' => '0',



            ),
            101 =>
            array(
                'id' => 102,
                'code' => 'ID',
                'name' => 'Indonesia',

                'status' => '0',



            ),
            102 =>
            array(
                'id' => 103,
                'code' => 'IR',
                'name' => 'Iran',

                'status' => '0',



            ),
            103 =>
            array(
                'id' => 104,
                'code' => 'IQ',
                'name' => 'Iraq',

                'status' => '0',



            ),
            104 =>
            array(
                'id' => 105,
                'code' => 'IE',
                'name' => 'Ireland',

                'status' => '0',



            ),
            105 =>
            array(
                'id' => 106,
                'code' => 'IL',
                'name' => 'Israel',

                'status' => '0',



            ),
            106 =>
            array(
                'id' => 107,
                'code' => 'IT',
                'name' => 'Italy',

                'status' => '0',



            ),
            107 =>
            array(
                'id' => 108,
                'code' => 'JM',
                'name' => 'Jamaica',

                'status' => '0',



            ),
            108 =>
            array(
                'id' => 109,
                'code' => 'JP',
                'name' => 'Japan',

                'status' => '0',



            ),
            109 =>
            array(
                'id' => 110,
                'code' => 'XJ',
                'name' => 'Jersey',

                'status' => '0',



            ),
            110 =>
            array(
                'id' => 111,
                'code' => 'JO',
                'name' => 'Jordan',

                'status' => '0',



            ),
            111 =>
            array(
                'id' => 112,
                'code' => 'KZ',
                'name' => 'Kazakhstan',

                'status' => '0',



            ),
            112 =>
            array(
                'id' => 113,
                'code' => 'KE',
                'name' => 'Kenya',

                'status' => '0',



            ),
            113 =>
            array(
                'id' => 114,
                'code' => 'KI',
                'name' => 'Kiribati',

                'status' => '0',



            ),
            114 =>
            array(
                'id' => 115,
                'code' => 'KP',
                'name' => 'Korea North',

                'status' => '0',



            ),
            115 =>
            array(
                'id' => 116,
                'code' => 'KR',
                'name' => 'Korea South',

                'status' => '0',



            ),
            116 =>
            array(
                'id' => 117,
                'code' => 'KW',
                'name' => 'Kuwait',

                'status' => '0',



            ),
            117 =>
            array(
                'id' => 118,
                'code' => 'KG',
                'name' => 'Kyrgyzstan',

                'status' => '0',



            ),
            118 =>
            array(
                'id' => 119,
                'code' => 'LA',
                'name' => 'Laos',

                'status' => '0',



            ),
            119 =>
            array(
                'id' => 120,
                'code' => 'LV',
                'name' => 'Latvia',

                'status' => '0',



            ),
            120 =>
            array(
                'id' => 121,
                'code' => 'LB',
                'name' => 'Lebanon',

                'status' => '0',



            ),
            121 =>
            array(
                'id' => 122,
                'code' => 'LS',
                'name' => 'Lesotho',

                'status' => '0',



            ),
            122 =>
            array(
                'id' => 123,
                'code' => 'LR',
                'name' => 'Liberia',

                'status' => '0',



            ),
            123 =>
            array(
                'id' => 124,
                'code' => 'LY',
                'name' => 'Libya',

                'status' => '0',



            ),
            124 =>
            array(
                'id' => 125,
                'code' => 'LI',
                'name' => 'Liechtenstein',

                'status' => '0',



            ),
            125 =>
            array(
                'id' => 126,
                'code' => 'LT',
                'name' => 'Lithuania',

                'status' => '0',



            ),
            126 =>
            array(
                'id' => 127,
                'code' => 'LU',
                'name' => 'Luxembourg',

                'status' => '0',



            ),
            127 =>
            array(
                'id' => 128,
                'code' => 'MO',
                'name' => 'Macau S.A.R.',

                'status' => '0',



            ),
            128 =>
            array(
                'id' => 129,
                'code' => 'MK',
                'name' => 'Macedonia',

                'status' => '0',



            ),
            129 =>
            array(
                'id' => 130,
                'code' => 'MG',
                'name' => 'Madagascar',

                'status' => '0',



            ),
            130 =>
            array(
                'id' => 131,
                'code' => 'MW',
                'name' => 'Malawi',

                'status' => '0',



            ),
            131 =>
            array(
                'id' => 132,
                'code' => 'MY',
                'name' => 'Malaysia',

                'status' => '0',



            ),
            132 =>
            array(
                'id' => 133,
                'code' => 'MV',
                'name' => 'Maldives',

                'status' => '0',



            ),
            133 =>
            array(
                'id' => 134,
                'code' => 'ML',
                'name' => 'Mali',

                'status' => '0',



            ),
            134 =>
            array(
                'id' => 135,
                'code' => 'MT',
                'name' => 'Malta',

                'status' => '0',



            ),
            135 =>
            array(
                'id' => 136,
                'code' => 'XM',
                'name' => 'Man (Isle of)',

                'status' => '0',



            ),
            136 =>
            array(
                'id' => 137,
                'code' => 'MH',
                'name' => 'Marshall Islands',

                'status' => '0',



            ),
            137 =>
            array(
                'id' => 138,
                'code' => 'MQ',
                'name' => 'Martinique',

                'status' => '0',



            ),
            138 =>
            array(
                'id' => 139,
                'code' => 'MR',
                'name' => 'Mauritania',

                'status' => '0',



            ),
            139 =>
            array(
                'id' => 140,
                'code' => 'MU',
                'name' => 'Mauritius',

                'status' => '0',



            ),
            140 =>
            array(
                'id' => 141,
                'code' => 'YT',
                'name' => 'Mayotte',

                'status' => '0',



            ),
            141 =>
            array(
                'id' => 142,
                'code' => 'MX',
                'name' => 'Mexico',

                'status' => '0',



            ),
            142 =>
            array(
                'id' => 143,
                'code' => 'FM',
                'name' => 'Micronesia',

                'status' => '0',



            ),
            143 =>
            array(
                'id' => 144,
                'code' => 'MD',
                'name' => 'Moldova',

                'status' => '0',



            ),
            144 =>
            array(
                'id' => 145,
                'code' => 'MC',
                'name' => 'Monaco',

                'status' => '0',



            ),
            145 =>
            array(
                'id' => 146,
                'code' => 'MN',
                'name' => 'Mongolia',

                'status' => '0',



            ),
            146 =>
            array(
                'id' => 147,
                'code' => 'MS',
                'name' => 'Montserrat',

                'status' => '0',



            ),
            147 =>
            array(
                'id' => 148,
                'code' => 'MA',
                'name' => 'Morocco',

                'status' => '0',



            ),
            148 =>
            array(
                'id' => 149,
                'code' => 'MZ',
                'name' => 'Mozambique',

                'status' => '0',



            ),
            149 =>
            array(
                'id' => 150,
                'code' => 'MM',
                'name' => 'Myanmar',

                'status' => '0',



            ),
            150 =>
            array(
                'id' => 151,
                'code' => 'NA',
                'name' => 'Namibia',

                'status' => '0',



            ),
            151 =>
            array(
                'id' => 152,
                'code' => 'NR',
                'name' => 'Nauru',

                'status' => '0',



            ),
            152 =>
            array(
                'id' => 153,
                'code' => 'NP',
                'name' => 'Nepal',

                'status' => '0',



            ),
            153 =>
            array(
                'id' => 154,
                'code' => 'AN',
                'name' => 'Netherlands Antilles',

                'status' => '0',



            ),
            154 =>
            array(
                'id' => 155,
                'code' => 'NL',
                'name' => 'Netherlands The',

                'status' => '0',



            ),
            155 =>
            array(
                'id' => 156,
                'code' => 'NC',
                'name' => 'New Caledonia',

                'status' => '0',



            ),
            156 =>
            array(
                'id' => 157,
                'code' => 'NZ',
                'name' => 'New Zealand',

                'status' => '0',



            ),
            157 =>
            array(
                'id' => 158,
                'code' => 'NI',
                'name' => 'Nicaragua',

                'status' => '0',



            ),
            158 =>
            array(
                'id' => 159,
                'code' => 'NE',
                'name' => 'Niger',

                'status' => '0',



            ),
            159 =>
            array(
                'id' => 160,
                'code' => 'NG',
                'name' => 'Nigeria',

                'status' => '0',



            ),
            160 =>
            array(
                'id' => 161,
                'code' => 'NU',
                'name' => 'Niue',

                'status' => '0',



            ),
            161 =>
            array(
                'id' => 162,
                'code' => 'NF',
                'name' => 'Norfolk Island',

                'status' => '0',



            ),
            162 =>
            array(
                'id' => 163,
                'code' => 'MP',
                'name' => 'Northern Mariana Islands',

                'status' => '0',



            ),
            163 =>
            array(
                'id' => 164,
                'code' => 'NO',
                'name' => 'Norway',

                'status' => '0',



            ),
            164 =>
            array(
                'id' => 165,
                'code' => 'OM',
                'name' => 'Oman',

                'status' => '0',



            ),
            165 =>
            array(
                'id' => 166,
                'code' => 'PK',
                'name' => 'Pakistan',

                'status' => '0',



            ),
            166 =>
            array(
                'id' => 167,
                'code' => 'PW',
                'name' => 'Palau',

                'status' => '0',



            ),
            167 =>
            array(
                'id' => 168,
                'code' => 'PS',
                'name' => 'Palestinian Territory Occupied',

                'status' => '0',



            ),
            168 =>
            array(
                'id' => 169,
                'code' => 'PA',
                'name' => 'Panama',

                'status' => '0',



            ),
            169 =>
            array(
                'id' => 170,
                'code' => 'PG',
                'name' => 'Papua new Guinea',

                'status' => '0',



            ),
            170 =>
            array(
                'id' => 171,
                'code' => 'PY',
                'name' => 'Paraguay',

                'status' => '0',



            ),
            171 =>
            array(
                'id' => 172,
                'code' => 'PE',
                'name' => 'Peru',

                'status' => '0',



            ),
            172 =>
            array(
                'id' => 173,
                'code' => 'PH',
                'name' => 'Philippines',

                'status' => '0',



            ),
            173 =>
            array(
                'id' => 174,
                'code' => 'PN',
                'name' => 'Pitcairn Island',

                'status' => '0',



            ),
            174 =>
            array(
                'id' => 175,
                'code' => 'PL',
                'name' => 'Poland',

                'status' => '0',



            ),
            175 =>
            array(
                'id' => 176,
                'code' => 'PT',
                'name' => 'Portugal',

                'status' => '0',



            ),
            176 =>
            array(
                'id' => 177,
                'code' => 'PR',
                'name' => 'Puerto Rico',

                'status' => '0',



            ),
            177 =>
            array(
                'id' => 178,
                'code' => 'QA',
                'name' => 'Qatar',

                'status' => '0',



            ),
            178 =>
            array(
                'id' => 179,
                'code' => 'RE',
                'name' => 'Reunion',

                'status' => '0',



            ),
            179 =>
            array(
                'id' => 180,
                'code' => 'RO',
                'name' => 'Romania',

                'status' => '0',



            ),
            180 =>
            array(
                'id' => 181,
                'code' => 'RU',
                'name' => 'Russia',

                'status' => '0',



            ),
            181 =>
            array(
                'id' => 182,
                'code' => 'RW',
                'name' => 'Rwanda',

                'status' => '0',



            ),
            182 =>
            array(
                'id' => 183,
                'code' => 'SH',
                'name' => 'Saint Helena',

                'status' => '0',



            ),
            183 =>
            array(
                'id' => 184,
                'code' => 'KN',
                'name' => 'Saint Kitts And Nevis',

                'status' => '0',



            ),
            184 =>
            array(
                'id' => 185,
                'code' => 'LC',
                'name' => 'Saint Lucia',

                'status' => '0',



            ),
            185 =>
            array(
                'id' => 186,
                'code' => 'PM',
                'name' => 'Saint Pierre and Miquelon',

                'status' => '0',



            ),
            186 =>
            array(
                'id' => 187,
                'code' => 'VC',
                'name' => 'Saint Vincent And The Grenadines',

                'status' => '0',



            ),
            187 =>
            array(
                'id' => 188,
                'code' => 'WS',
                'name' => 'Samoa',

                'status' => '0',



            ),
            188 =>
            array(
                'id' => 189,
                'code' => 'SM',
                'name' => 'San Marino',

                'status' => '0',



            ),
            189 =>
            array(
                'id' => 190,
                'code' => 'ST',
                'name' => 'Sao Tome and Principe',

                'status' => '0',



            ),
            190 =>
            array(
                'id' => 191,
                'code' => 'SA',
                'name' => 'Saudi Arabia',

                'status' => '0',



            ),
            191 =>
            array(
                'id' => 192,
                'code' => 'SN',
                'name' => 'Senegal',

                'status' => '0',



            ),
            192 =>
            array(
                'id' => 193,
                'code' => 'RS',
                'name' => 'Serbia',

                'status' => '0',



            ),
            193 =>
            array(
                'id' => 194,
                'code' => 'SC',
                'name' => 'Seychelles',

                'status' => '0',



            ),
            194 =>
            array(
                'id' => 195,
                'code' => 'SL',
                'name' => 'Sierra Leone',

                'status' => '0',



            ),
            195 =>
            array(
                'id' => 196,
                'code' => 'SG',
                'name' => 'Singapore',

                'status' => '0',



            ),
            196 =>
            array(
                'id' => 197,
                'code' => 'SK',
                'name' => 'Slovakia',

                'status' => '0',



            ),
            197 =>
            array(
                'id' => 198,
                'code' => 'SI',
                'name' => 'Slovenia',

                'status' => '0',



            ),
            198 =>
            array(
                'id' => 199,
                'code' => 'XG',
                'name' => 'Smaller Territories of the UK',

                'status' => '0',



            ),
            199 =>
            array(
                'id' => 200,
                'code' => 'SB',
                'name' => 'Solomon Islands',

                'status' => '0',



            ),
            200 =>
            array(
                'id' => 201,
                'code' => 'SO',
                'name' => 'Somalia',

                'status' => '0',



            ),
            201 =>
            array(
                'id' => 202,
                'code' => 'ZA',
                'name' => 'South Africa',

                'status' => '0',



            ),
            202 =>
            array(
                'id' => 203,
                'code' => 'GS',
                'name' => 'South Georgia',

                'status' => '0',



            ),
            203 =>
            array(
                'id' => 204,
                'code' => 'SS',
                'name' => 'South Sudan',

                'status' => '0',



            ),
            204 =>
            array(
                'id' => 205,
                'code' => 'ES',
                'name' => 'Spain',

                'status' => '0',



            ),
            205 =>
            array(
                'id' => 206,
                'code' => 'LK',
                'name' => 'Sri Lanka',

                'status' => '0',



            ),
            206 =>
            array(
                'id' => 207,
                'code' => 'SD',
                'name' => 'Sudan',

                'status' => '0',



            ),
            207 =>
            array(
                'id' => 208,
                'code' => 'SR',
                'name' => 'Suriname',

                'status' => '0',



            ),
            208 =>
            array(
                'id' => 209,
                'code' => 'SJ',
                'name' => 'Svalbard And Jan Mayen Islands',

                'status' => '0',



            ),
            209 =>
            array(
                'id' => 210,
                'code' => 'SZ',
                'name' => 'Swaziland',

                'status' => '0',



            ),
            210 =>
            array(
                'id' => 211,
                'code' => 'SE',
                'name' => 'Sweden',

                'status' => '0',



            ),
            211 =>
            array(
                'id' => 212,
                'code' => 'CH',
                'name' => 'Switzerland',

                'status' => '0',



            ),
            212 =>
            array(
                'id' => 213,
                'code' => 'SY',
                'name' => 'Syria',

                'status' => '0',



            ),
            213 =>
            array(
                'id' => 214,
                'code' => 'TW',
                'name' => 'Taiwan',

                'status' => '0',



            ),
            214 =>
            array(
                'id' => 215,
                'code' => 'TJ',
                'name' => 'Tajikistan',

                'status' => '0',



            ),
            215 =>
            array(
                'id' => 216,
                'code' => 'TZ',
                'name' => 'Tanzania',

                'status' => '0',



            ),
            216 =>
            array(
                'id' => 217,
                'code' => 'TH',
                'name' => 'Thailand',

                'status' => '0',



            ),
            217 =>
            array(
                'id' => 218,
                'code' => 'TG',
                'name' => 'Togo',

                'status' => '0',



            ),
            218 =>
            array(
                'id' => 219,
                'code' => 'TK',
                'name' => 'Tokelau',

                'status' => '0',



            ),
            219 =>
            array(
                'id' => 220,
                'code' => 'TO',
                'name' => 'Tonga',

                'status' => '0',



            ),
            220 =>
            array(
                'id' => 221,
                'code' => 'TT',
                'name' => 'Trinidad And Tobago',

                'status' => '0',



            ),
            221 =>
            array(
                'id' => 222,
                'code' => 'TN',
                'name' => 'Tunisia',

                'status' => '0',



            ),
            222 =>
            array(
                'id' => 223,
                'code' => 'TR',
                'name' => 'Turkey',

                'status' => '0',



            ),
            223 =>
            array(
                'id' => 224,
                'code' => 'TM',
                'name' => 'Turkmenistan',

                'status' => '0',



            ),
            224 =>
            array(
                'id' => 225,
                'code' => 'TC',
                'name' => 'Turks And Caicos Islands',

                'status' => '0',



            ),
            225 =>
            array(
                'id' => 226,
                'code' => 'TV',
                'name' => 'Tuvalu',

                'status' => '0',



            ),
            226 =>
            array(
                'id' => 227,
                'code' => 'UG',
                'name' => 'Uganda',

                'status' => '0',



            ),
            227 =>
            array(
                'id' => 228,
                'code' => 'UA',
                'name' => 'Ukraine',

                'status' => '0',



            ),
            228 =>
            array(
                'id' => 229,
                'code' => 'AE',
                'name' => 'United Arab Emirates',

                'status' => '0',



            ),
            229 =>
            array(
                'id' => 230,
                'code' => 'GB',
                'name' => 'United Kingdom',

                'status' => '0',



            ),
            230 =>
            array(
                'id' => 231,
                'code' => 'US',
                'name' => 'United States',

                'status' => '0',



            ),
            231 =>
            array(
                'id' => 232,
                'code' => 'UM',
                'name' => 'United States Minor Outlying Islands',

                'status' => '0',



            ),
            232 =>
            array(
                'id' => 233,
                'code' => 'UY',
                'name' => 'Uruguay',

                'status' => '0',



            ),
            233 =>
            array(
                'id' => 234,
                'code' => 'UZ',
                'name' => 'Uzbekistan',

                'status' => '0',



            ),
            234 =>
            array(
                'id' => 235,
                'code' => 'VU',
                'name' => 'Vanuatu',

                'status' => '0',



            ),
            235 =>
            array(
                'id' => 236,
                'code' => 'VA',
                'name' => 'Vatican City State (Holy See)',

                'status' => '0',



            ),
            236 =>
            array(
                'id' => 237,
                'code' => 'VE',
                'name' => 'Venezuela',

                'status' => '0',



            ),
            237 =>
            array(
                'id' => 238,
                'code' => 'VN',
                'name' => 'Vietnam',

                'status' => '0',



            ),
            238 =>
            array(
                'id' => 239,
                'code' => 'VG',
                'name' => 'Virgin Islands (British)',

                'status' => '0',



            ),
            239 =>
            array(
                'id' => 240,
                'code' => 'VI',
                'name' => 'Virgin Islands (US)',

                'status' => '0',



            ),
            240 =>
            array(
                'id' => 241,
                'code' => 'WF',
                'name' => 'Wallis And Futuna Islands',

                'status' => '0',



            ),
            241 =>
            array(
                'id' => 242,
                'code' => 'EH',
                'name' => 'Western Sahara',

                'status' => '0',



            ),
            242 =>
            array(
                'id' => 243,
                'code' => 'YE',
                'name' => 'Yemen',

                'status' => '0',



            ),
            243 =>
            array(
                'id' => 244,
                'code' => 'YU',
                'name' => 'Yugoslavia',

                'status' => '0',



            ),
            244 =>
            array(
                'id' => 245,
                'code' => 'ZM',
                'name' => 'Zambia',

                'status' => '0',



            ),
            245 =>
            array(
                'id' => 246,
                'code' => 'ZW',
                'name' => 'Zimbabwe',

                'status' => '0',



            ),
        ));
    }
}
