<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use PHPUnit\Framework\Constraint\Count;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
       $this->call([
        //    CountriesTableSeeder::class,
        //    StatesTableSeeder::class,
        //    CitySeeder::class,
        //     AdminSeeder::class,
        //     IdentificationTypeSeeder::class,
            BankSeeder::class,

       ]);
    }
}
