<?php

namespace Database\Seeders;

use App\Enums\Status;
use Illuminate\Support\Carbon;
use Illuminate\Database\Seeder;
use App\Models\IdentificationType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class IdentificationTypeSeeder extends Seeder
{
     /**
     * Run the database seeds.
     *
     * @return void
     */

         public function run()
    {
        IdentificationType::insert([
            ['name'=>'Drivers License',
            'slug'=>'drivers-license',
            'status'=>Status::Active->value,
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now(),
            ],
            ['name'=>'Permanent Voters Card',
            'slug'=>'voters-card',
            'status'=>Status::Active->value,
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now(),
            ],
            ['name'=>'National Identification Number',
            'slug'=>'nin',
            'status'=>Status::Active->value,
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now(),
            ],
            ['name'=>'International Passport',
            'slug'=>'international-passport',
            'status'=>Status::Active->value,
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now(),
            ],
        ]);
    }
    }

