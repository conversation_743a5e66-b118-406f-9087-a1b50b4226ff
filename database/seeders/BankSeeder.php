<?php

namespace Database\Seeders;

use App\Models\Bank;
use App\Enums\Status;
use Illuminate\Database\Seeder;
use App\Services\PayStackServices;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class BankSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */


    public function run(): void
    {

        // $paystackService = new PayStackServices();


        // foreach($paystackService->getBanks() as $key => $banks){
        //     Bank::create([
        //         'code'=>$banks['code'],
        //         'name'=>$banks['name'],
        //         'status'=>Status::Active->value,
        //         'user_id'=>'1'
        //     ]);
        // }


        DB::table('banks')->insert(array (
            0 =>
            array (
              'id' => 132,
              'code' => '560',
              'name' => 'Page MFBank',
            ),
            1 =>
            array (
              'id' => 133,
              'code' => '304',
              'name' => 'Stanbic Mobile Money',
            ),
            2 =>
            array (
              'id' => 134,
              'code' => '308',
              'name' => 'FortisMobile',
            ),
            3 =>
            array (
              'id' => 135,
              'code' => '328',
              'name' => 'TagPay',
            ),
            4 =>
            array (
              'id' => 136,
              'code' => '309',
              'name' => 'FBNMobile',
            ),
            5 =>
            array (
              'id' => 137,
              'code' => '011',
              'name' => 'First Bank of Nigeria',
            ),
            6 =>
            array (
              'id' => 138,
              'code' => '326',
              'name' => 'Sterling Mobile',
            ),
            7 =>
            array (
              'id' => 139,
              'code' => '990',
              'name' => 'Omoluabi Mortgage Bank',
            ),
            8 =>
            array (
              'id' => 140,
              'code' => '311',
              'name' => 'ReadyCash (Parkway)',
            ),
            9 =>
            array (
              'id' => 141,
              'code' => '057',
              'name' => 'Zenith Bank',
            ),
            10 =>
            array (
              'id' => 142,
              'code' => '068',
              'name' => 'Standard Chartered Bank',
            ),
            11 =>
            array (
              'id' => 143,
              'code' => '306',
              'name' => 'eTranzact',
            ),
            12 =>
            array (
              'id' => 144,
              'code' => '070',
              'name' => 'Fidelity Bank',
            ),
            13 =>
            array (
              'id' => 145,
              'code' => '023',
              'name' => 'CitiBank',
            ),
            14 =>
            array (
              'id' => 146,
              'code' => '215',
              'name' => 'Unity Bank',
            ),
            15 =>
            array (
              'id' => 147,
              'code' => '323',
              'name' => 'Access Money',
            ),
            16 =>
            array (
              'id' => 148,
              'code' => '302',
              'name' => 'Eartholeum',
            ),
            17 =>
            array (
              'id' => 149,
              'code' => '324',
              'name' => 'Hedonmark',
            ),
            18 =>
            array (
              'id' => 150,
              'code' => '325',
              'name' => 'MoneyBox',
            ),
            19 =>
            array (
              'id' => 151,
              'code' => '301',
              'name' => 'JAIZ Bank',
            ),
            20 =>
            array (
              'id' => 152,
              'code' => '050',
              'name' => 'Ecobank Plc',
            ),
            21 =>
            array (
              'id' => 153,
              'code' => '307',
              'name' => 'EcoMobile',
            ),
            22 =>
            array (
              'id' => 154,
              'code' => '318',
              'name' => 'Fidelity Mobile',
            ),
            23 =>
            array (
              'id' => 155,
              'code' => '319',
              'name' => 'TeasyMobile',
            ),
            24 =>
            array (
              'id' => 156,
              'code' => '999',
              'name' => 'NIP Virtual Bank',
            ),
            25 =>
            array (
              'id' => 157,
              'code' => '320',
              'name' => 'VTNetworks',
            ),
            26 =>
            array (
              'id' => 158,
              'code' => '221',
              'name' => 'Stanbic IBTC Bank',
            ),
            27 =>
            array (
              'id' => 159,
              'code' => '501',
              'name' => 'Fortis Microfinance Bank',
            ),
            28 =>
            array (
              'id' => 160,
              'code' => '329',
              'name' => 'PayAttitude Online',
            ),
            29 =>
            array (
              'id' => 161,
              'code' => '322',
              'name' => 'ZenithMobile',
            ),
            30 =>
            array (
              'id' => 162,
              'code' => '303',
              'name' => 'ChamsMobile',
            ),
            31 =>
            array (
              'id' => 163,
              'code' => '403',
              'name' => 'SafeTrust Mortgage Bank',
            ),
            32 =>
            array (
              'id' => 164,
              'code' => '551',
              'name' => 'Covenant Microfinance Bank',
            ),
            33 =>
            array (
              'id' => 165,
              'code' => '415',
              'name' => 'Imperial Homes Mortgage Bank',
            ),
            34 =>
            array (
              'id' => 166,
              'code' => '552',
              'name' => 'NPF MicroFinance Bank',
            ),
            35 =>
            array (
              'id' => 167,
              'code' => '526',
              'name' => 'Parralex',
            ),
            36 =>
            array (
              'id' => 168,
              'code' => '035',
              'name' => 'Wema Bank',
            ),
            37 =>
            array (
              'id' => 169,
              'code' => '084',
              'name' => 'Enterprise Bank',
            ),
            38 =>
            array (
              'id' => 170,
              'code' => '063',
              'name' => 'Diamond Bank',
            ),
            39 =>
            array (
              'id' => 171,
              'code' => '305',
              'name' => 'Paycom',
            ),
            40 =>
            array (
              'id' => 172,
              'code' => '100',
              'name' => 'SunTrust Bank',
            ),
            41 =>
            array (
              'id' => 173,
              'code' => '317',
              'name' => 'Cellulant',
            ),
            42 =>
            array (
              'id' => 174,
              'code' => '401',
              'name' => 'ASO Savings and & Loans',
            ),
            43 =>
            array (
              'id' => 175,
              'code' => '030',
              'name' => 'Heritage',
            ),
            44 =>
            array (
              'id' => 176,
              'code' => '402',
              'name' => 'Jubilee Life Mortgage Bank',
            ),
            45 =>
            array (
              'id' => 177,
              'code' => '058',
              'name' => 'GTBank Plc',
            ),
            46 =>
            array (
              'id' => 178,
              'code' => '032',
              'name' => 'Union Bank',
            ),
            47 =>
            array (
              'id' => 179,
              'code' => '232',
              'name' => 'Sterling Bank',
            ),
            48 =>
            array (
              'id' => 180,
              'code' => '076',
              'name' => 'Skye Bank',
            ),
            49 =>
            array (
              'id' => 181,
              'code' => '082',
              'name' => 'Keystone Bank',
            ),
            50 =>
            array (
              'id' => 182,
              'code' => '327',
              'name' => 'Pagatech',
            ),
            51 =>
            array (
              'id' => 183,
              'code' => '559',
              'name' => 'Coronation Merchant Bank',
            ),
            52 =>
            array (
              'id' => 184,
              'code' => '601',
              'name' => 'FSDH',
            ),
            53 =>
            array (
              'id' => 185,
              'code' => '313',
              'name' => 'Mkudi',
            ),
            54 =>
            array (
              'id' => 186,
              'code' => '214',
              'name' => 'First City Monument Bank',
            ),
            55 =>
            array (
              'id' => 187,
              'code' => '314',
              'name' => 'FET',
            ),
            56 =>
            array (
              'id' => 188,
              'code' => '523',
              'name' => 'Trustbond',
            ),
            57 =>
            array (
              'id' => 189,
              'code' => '315',
              'name' => 'GTMobile',
            ),
            58 =>
            array (
              'id' => 190,
              'code' => '033',
              'name' => 'United Bank for Africa',
            ),
            59 =>
            array (
              'id' => 191,
              'code' => '044',
              'name' => 'Access Bank',
            ),
            60 =>
            array (
              'id' => 567,
              'code' => '90115',
              'name' => 'TCF MFB',
            ),
        ));
    }
}
