<?php

use App\Enums\Gender;
use App\Enums\UserTypes;
use App\Enums\UserStatus;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
           $table->id();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('other_names')->nullable();
            $table->string('email')->unique();
            $table->date('data_of_birth')->nullable();
            $table->enum('gender',[Gender::Male->value,Gender::Female->value])->nullable();
            $table->enum('user_type',[UserTypes::User->value,UserTypes::Admin->value])->default(UserTypes::User->value);
            $table->string('phone')->nullable();
            $table->string('password');
            $table->string('profile_image')->nullable();
            $table->ipAddress('ip_address')->nullable();
            $table->string('address')->nullable();
            $table->foreignId('city_id')->nullable()->constrained('cities')->onDelete('set null');
            $table->foreignId('state_id')->nullable()->constrained('states')->onDelete('set null');
            $table->foreignId('country_id')->nullable()->constrained('countries')->onDelete('set null');
            $table->timestamp('last_login')->nullable();
            $table->decimal('wallet',10,2)->default(0)->nullable();
            $table->decimal('pending_balance',10,2)->default(0)->nullable();
            $table->enum('status',[UserStatus::Pending->value,UserStatus::Active->value,UserStatus::Banned->value,UserStatus::Suspended->value,UserStatus::Deactivated->value])->default(UserStatus::Pending->value);
            $table->timestamp('email_verified_at')->nullable();
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
