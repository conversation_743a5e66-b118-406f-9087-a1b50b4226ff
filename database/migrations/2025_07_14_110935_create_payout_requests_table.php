<?php

use App\Enums\Currency;
use App\Enums\TxStatus;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
       public function up(): void
    {
        Schema::create('payout_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('group_id')->constrained('groups')->onDelete('cascade');
            $table->decimal('payout_amount', 10, 2);
            $table->enum('currency', [Currency::Naira->value, Currency::Yuan->value])->default(Currency::Naira->value);
            $table->enum('status', [
                TxStatus::Pending->value,
                TxStatus::Active->value,
                TxStatus::Approved->value,
                TxStatus::Rejected->value,
                TxStatus::InReview->value,
                TxStatus::Inprogress->value,
                TxStatus::Completed->value,
                TxStatus::Accepted->value,
                TxStatus::Cancelled->value
            ])->default(TxStatus::Pending->value);
            $table->datetime('requested_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payout_requests');
    }
};
