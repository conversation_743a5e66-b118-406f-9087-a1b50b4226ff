<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('group_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('group_id')->constrained('groups')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->text('message');
            $table->enum('type', ['chat', 'payment', 'system'])->default('chat');
            $table->json('metadata')->nullable(); // For payment info, etc.
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
            
            $table->index(['group_id', 'created_at']);
        });
    }


    public function down()
    {
        Schema::dropIfExists('group_messages');
    }
};
