<?php

use App\Enums\TxStatus;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->string('task_name');
            $table->text('task_description');
            $table->integer('task_points');
             $table->enum('status', [TxStatus::Pending->value, TxStatus::Active->value, TxStatus::Approved->value, TxStatus::Accepted->value, TxStatus::Completed->value, TxStatus::Rejected->value, TxStatus::Cancelled->value, TxStatus::InReview->value, TxStatus::Inprogress->value])->default(TxStatus::Pending->value);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
