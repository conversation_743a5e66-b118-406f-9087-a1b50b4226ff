<?php

use App\Enums\TxStatus;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kyc_verifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('means_of_verification')->nullable();
            $table->string('proof_address')->nullable();
            $table->string('address')->nullable();
            $table->foreignId('identification_type_id')->constrained('identification_types')->onDelete('cascade');
            $table->enum('status', [TxStatus::Pending->value,TxStatus::Completed->value,TxStatus::Cancelled->value,TxStatus::Rejected->value])->default(TxStatus::Pending->value);
            $table->string('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kyc_verifications');
    }
};
