<?php

use App\Enums\TxStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contributions', function (Blueprint $table) {
            //
            $table->enum('status', [TxStatus::Pending->value, TxStatus::Active->value, TxStatus::Approved->value, TxStatus::Accepted->value, TxStatus::Completed->value, TxStatus::Rejected->value, TxStatus::Cancelled->value, TxStatus::InReview->value, TxStatus::Inprogress->value])->default(TxStatus::Pending->value)->after('amount')
                ->comment('The status of the contribution transaction');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contributions', function (Blueprint $table) {
            //
            $table->dropColumn('status');
        });
    }
};
