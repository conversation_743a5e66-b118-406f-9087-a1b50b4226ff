<?php

use App\Enums\Status;
use App\Enums\TxType;
use App\Enums\TxTypes;
use App\Enums\TxStatus;
use App\Enums\Frequency;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('group_id')->constrained('groups')->onDelete('cascade')->nullable();
            $table->enum('tx_type', [TxTypes::Contribution->value, TxTypes::Payout->value])->default(TxTypes::Contribution->value);
            $table->string('txref')->nullable();
            $table->decimal('amount', 10, 2);
            $table->enum('frequency', [Frequency::Weekly->value, Frequency::Monthly->value, Frequency::Biweekly->value, Frequency::Yearly->value, Frequency::Quarterly->value])->default(Frequency::Weekly->value);
            $table->enum('status', [
                TxStatus::Pending->value,
                TxStatus::Active->value,
                TxStatus::Approved->value,
                TxStatus::Rejected->value,
                TxStatus::InReview->value,
                TxStatus::Inprogress->value,
                TxStatus::Completed->value,
                TxStatus::Accepted->value,
                TxStatus::Cancelled->value
            ])->default(TxStatus::Pending->value);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
