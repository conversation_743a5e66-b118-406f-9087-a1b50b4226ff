<?php

use App\Enums\Currency;
use App\Enums\Frequency;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use App\Enums\GroupStatus;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('groups', function (Blueprint $table) {
            $table->id();
            $table->string('group_name');
            $table->decimal('amount',10,2);
            $table->enum('frequency', [Frequency::Weekly->value, Frequency::Monthly->value, Frequency::Biweekly->value, Frequency::Yearly->value, Frequency::Quarterly->value])->default(Frequency::Weekly->value);
            $table->enum('currency', [Currency::Naira->value, Currency::Yuan->value])->default(Currency::Naira->value);
            $table->integer('number_of_numbers');
            $table->string('group_rules')->nullable();
            $table->string('orderOfPayment')->nullable();
            $table->enum('status',[GroupStatus::Pending->value,GroupStatus::Active->value,GroupStatus::Banned->value,GroupStatus::Suspended->value,GroupStatus::Deactivated->value,GroupStatus::Closed->value])->default(GroupStatus::Pending->value);
            $table->string('group_link')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('groups');
    }
};
