<?php

use App\Enums\UserTypes;
use App\Enums\PageStatus;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('title')->nullable();
            $table->longText('content')->nullable();
            $table->string('slug')->unique();
            $table->enum('deletable', [UserTypes::Admin->value, UserTypes::User->value])->default(UserTypes::User->value);
            $table->enum('status', [PageStatus::Draft->value, PageStatus::Published->value])->default(PageStatus::Draft->value);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pages');
    }
};
