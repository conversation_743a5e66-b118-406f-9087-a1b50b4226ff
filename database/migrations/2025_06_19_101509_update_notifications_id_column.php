<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Drop the `id` column first (this will automatically drop the PK too)
        Schema::table('notifications', function (Blueprint $table) {
            $table->dropColumn('id');
        });

        // Add `uuid` as the new primary key
        Schema::table('notifications', function (Blueprint $table) {
            $table->uuid('id')->primary()->after('type');
        });
    }

    public function down(): void
    {
        // Drop the UUID `id` column
        Schema::table('notifications', function (Blueprint $table) {
            $table->dropColumn('id');
        });

        // Restore auto-incrementing integer `id`
        Schema::table('notifications', function (Blueprint $table) {
            $table->increments('id');
        });
    }
};

