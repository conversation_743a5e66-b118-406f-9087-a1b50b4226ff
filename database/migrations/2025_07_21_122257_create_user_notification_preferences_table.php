<?php


use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('user_notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            
            // Notification channels
            $table->boolean('email_notifications')->default(true);
            $table->boolean('sms_notifications')->default(false);
            $table->boolean('push_notifications')->default(true);
            $table->boolean('device_login_alerts')->default(true);
            
            // Notification categories
            $table->boolean('account_activity_notifications')->default(true);
            $table->boolean('transaction_notifications')->default(true);
            $table->boolean('system_notifications')->default(false);
            $table->boolean('marketing_notifications')->default(false);
            
            // Frequency settings
            $table->enum('email_frequency', ['immediate', 'daily', 'weekly', 'never'])->default('immediate');
            
            $table->timestamps();
            
            // Ensure one preference record per user
            $table->unique('user_id');
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_notification_preferences');
    }
};

