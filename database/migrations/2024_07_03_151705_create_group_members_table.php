<?php

use App\Enums\GroupStatus;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('group_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')->nullable();
            $table->foreignId('group_id')->constrained('groups')->onDelete('cascade')->nullable();
            $table->enum('status',[GroupStatus::Pending->value,GroupStatus::Active->value,GroupStatus::Banned->value,GroupStatus::Suspended->value,GroupStatus::Deactivated->value,GroupStatus::Closed->value])->default(GroupStatus::Pending->value);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('group_members');
    }
};
