<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('user_two_factor_auth', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            
            // 2FA Methods
            $table->boolean('sms_2fa')->default(false);
            $table->boolean('app_2fa')->default(false);
            $table->string('phone_2fa')->nullable();
            
            // 2FA Secrets and Codes
            $table->string('two_factor_secret')->nullable();
            $table->text('backup_codes')->nullable(); // JSON encoded backup codes
            $table->timestamp('backup_codes_generated_at')->nullable();
            
            // Recovery and verification
            $table->boolean('two_factor_confirmed')->default(false);
            $table->timestamp('two_factor_confirmed_at')->nullable();
            
            $table->timestamps();
            
            // Ensure one 2FA record per user
            $table->unique('user_id');
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_two_factor_auth');
    }
};
