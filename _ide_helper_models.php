<?php

// @formatter:off
// phpcs:ignoreFile
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON> <<EMAIL>>
 */


namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $code
 * @property string $name
 * @property int|null $user_id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Transaction|null $transaction
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Bank newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Bank newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Bank query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Bank whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Bank whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Bank whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Bank whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Bank whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Bank whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Bank whereUserId($value)
 */
	class Bank extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property int|null $state_id
 * @property int|null $user_id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read \App\Models\State|null $state
 * @method static \Illuminate\Database\Eloquent\Builder<static>|City newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|City newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|City query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|City whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|City whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|City whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|City whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|City whereStateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|City whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|City whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|City whereUserId($value)
 * @mixin \Eloquent
 */
	class City extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property string $amount
 * @property int $group_id
 * @property string|null $payment_proof
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribution newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribution newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribution query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribution whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribution whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribution whereGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribution whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribution wherePaymentProof($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribution whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribution whereUserId($value)
 */
	class Contribution extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string|null $code
 * @property string $name
 * @property \App\Enums\Status $status
 * @property int|null $phonecode Country phone code
 * @property int|null $user_id
 * @property string|null $lat
 * @property string|null $lng
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\State> $state
 * @property-read int|null $state_count
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereLng($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country wherePhonecode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereUserId($value)
 */
	class Country extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $group_name
 * @property string $amount
 * @property string $frequency
 * @property string $currency
 * @property int $number_of_numbers
 * @property string|null $group_rules
 * @property string|null $orderOfPayment
 * @property string $status
 * @property string|null $group_link
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereFrequency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereGroupLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereGroupName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereGroupRules($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereNumberOfNumbers($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereOrderOfPayment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Group whereUpdatedAt($value)
 */
	class Group extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property int $group_id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GroupMember newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GroupMember newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GroupMember query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GroupMember whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GroupMember whereGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GroupMember whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GroupMember whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GroupMember whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GroupMember whereUserId($value)
 */
	class GroupMember extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property \App\Enums\Status $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $user_id
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\kycVerification> $kyc
 * @property-read int|null $kyc_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentificationType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentificationType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentificationType query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentificationType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentificationType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentificationType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentificationType whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentificationType whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentificationType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentificationType whereUserId($value)
 */
	class IdentificationType extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $mediable_type
 * @property int $mediable_id
 * @property string $filename
 * @property string $name
 * @property string $path
 * @property string|null $mime_type
 * @property string $disk
 * @property int $file_size
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media whereDisk($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media whereFileSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media whereFilename($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media whereMediableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media whereMediableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media whereMimeType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Media whereUpdatedAt($value)
 */
	class Media extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int|null $user_id
 * @property string|null $title
 * @property string|null $content
 * @property string $slug
 * @property \App\Enums\UserTypes $deletable
 * @property \App\Enums\PageStatus $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereDeletable($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereUserId($value)
 */
	class Page extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property-read Profile|null $profile
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Profile newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Profile newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Profile query()
 */
	class Profile extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int|null $user_id
 * @property string $source
 * @property string $amount
 * @property string $currency
 * @property string|null $notes
 * @property string|null $revenue_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue whereRevenueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue whereSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Revenue whereUserId($value)
 */
	class Revenue extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property int $points
 * @property string $type
 * @property string|null $reason
 * @property string $status
 * @property string|null $rewarded_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward wherePoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereRewardedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereUserId($value)
 */
	class Reward extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string|null $value
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereValue($value)
 */
	class Setting extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $status
 * @property int|null $country_id
 * @property int|null $user_id
 * @property string|null $capital
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Cinema> $cinemas
 * @property-read int|null $cinemas_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\City> $cities
 * @property-read int|null $cities_count
 * @property-read \App\Models\Country|null $country
 * @property-read \App\Models\Country|null $state
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State whereCapital($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|State whereUserId($value)
 * @mixin \Eloquent
 */
	class State extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int|null $user_id
 * @property int $group_id
 * @property string $tx_type
 * @property string|null $txref
 * @property string $amount
 * @property string $frequency
 * @property \App\Enums\TxStatus $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Bank|null $bank
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction whereFrequency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction whereGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction whereTxType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction whereTxref($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Transaction whereUserId($value)
 */
	class Transaction extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $other_names
 * @property string $email
 * @property string|null $data_of_birth
 * @property string|null $gender
 * @property string $user_type
 * @property string|null $phone
 * @property string $password
 * @property string|null $profile_image
 * @property string|null $ip_address
 * @property string|null $address
 * @property int|null $city_id
 * @property int|null $state_id
 * @property int|null $country_id
 * @property string|null $last_login
 * @property string|null $wallet
 * @property string|null $pending_balance
 * @property string $status
 * @property string|null $profileimages
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property int|null $is_activated
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read \App\Models\kycVerification|null $kycVerification
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereDataOfBirth($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereIsActivated($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereLastLogin($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereOtherNames($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePendingBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereProfileImage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereProfileimages($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereStateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUserType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereWallet($value)
 */
	class User extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification query()
 */
	class Verification extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property string $token
 * @property string|null $email
 * @property int $is_activated
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property string|null $expires_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verifytoken newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verifytoken newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verifytoken query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verifytoken whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verifytoken whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verifytoken whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verifytoken whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verifytoken whereIsActivated($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verifytoken whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verifytoken whereUpdatedAt($value)
 */
	class Verifytoken extends \Eloquent {}
}

namespace App\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $means_of_verification
 * @property string|null $proof_address
 * @property string|null $address
 * @property int $identification_type_id
 * @property \App\Enums\TxStatus $status
 * @property string|null $description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\IdentificationType|null $identificationType
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification whereIdentificationTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification whereMeansOfVerification($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification whereProofAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|kycVerification whereUserId($value)
 */
	class kycVerification extends \Eloquent {}
}

