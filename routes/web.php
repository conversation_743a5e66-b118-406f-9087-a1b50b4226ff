<?php

use App\Models\Transaction;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Admin\TaskController;
use App\Http\Controllers\Admin\GroupsController;
use App\Http\Controllers\Admin\RewardController;
use App\Http\Controllers\Admin\RevenueController;
use App\Http\Controllers\Admin\RewardsController;
use App\Http\Controllers\Admin\RevenuesController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\LocationsController;
use App\Http\Controllers\User\UserProfileController;
use App\Http\Controllers\User\NotificationController;
use App\Http\Controllers\Admin\TransactionsController;
use App\Http\Controllers\User\UserDashboardController;
use App\Http\Controllers\Admin\PayoutRequestController;
use App\Http\Controllers\Admin\UserManagementController;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');


Route::controller(LocationsController::class)->prefix('location')->group(function () {
    Route::get('/country/json', 'getCountryJson')->name('location.get.country.json');
    Route::get('/state/{country_id}/json', 'getStateByCountryJson')->name('location.get.state.json');
    Route::get('/cities/{state_id}/json', 'getCitiesByStateJson')->name('location.get.cities.json');
    Route::get('/getstate', 'getsql')->name('getstate');
});


Route::prefix('admin')->middleware(['auth', 'admin', 'track.activity'])->group(function () {

     Route::get('/dashboard', [DashboardController::class, 'index'])->name('admin.dashboard');

    //  Groups

    Route::get('/groups/index', [GroupsController::class, 'viewGroups'])->name('admin.groups.index');
    Route::get('/groups/create', [GroupsController::class, 'createGroups'])->name('admin.groups.create');
    Route::post('/groups/store', [GroupsController::class, 'storeGroups'])->name('admin.groups.store');
    Route::get('/groups/{group}/edit', [GroupsController::class, 'editGroup'])->name('admin.groups.edit');
    Route::get('/groups/{id}/view', [GroupsController::class, 'showGroup'])->name('admin.groups.view');
    Route::patch('/groups/{group}/members/{member}/toggle-admin', [GroupsController::class, 'toggleMemberAdmin'])->name('admin.groups.members.toggle-admin');
   


    // Route::put('/groups/{group}/update', [GroupsController::class, 'updateGroups'])->name('admin.groups.update');
    Route::put('/groups/{id}/update', [GroupsController::class, 'updateGroups'])->name('admin.groups.update');
    Route::delete('/groups/{group}/destroy', [GroupsController::class, 'deleteGroup'])->name('admin.groups.destroy');

    Route::get('/groups/{group}/show', [GroupsController::class, 'showGroups'])->name('admin.groups.show');
    Route::patch('/admin/groups/{id}/suspend', [GroupsController::class, 'suspendGroup'])->name('admin.groups.suspend');

    Route::post('/groups/{group}/users/{user}/toggle-admin', [GroupsController::class, 'toggleGroupAdmin'])->name('admin.toggle.group');





    // user management
    Route::get('/users/index', [UserManagementController::class, 'viewUsers'])->name('admin.users.index');
    Route::get('/users/{id}/show', [UserManagementController::class, 'showUser'])->name('admin.users.show');
    Route::post('/users/store', [UserManagementController::class, 'store'])->name('admin.users.store');
    Route::get('/users/{id}/groups', [UserManagementController::class, 'viewUserGroups'])->name('admin.users.groups');
    Route::get('/users/{user}/groups/{group}/show', [UserManagementController::class, 'viewActiveGroup'])->name('admin.users.groups.show');
    Route::get('/users/{id}/transactions', [UserManagementController::class, 'viewUserTransactions'])->name('admin.users.transactions');
    Route::get('/users/{id}/documents', [UserManagementController::class, 'viewUserDocuments'])->name('admin.users.documents');
    Route::delete('/users/{id}/delete', [UserManagementController::class, 'destroyUser'])->name('admin.users.delete');
    Route::patch('/admin/users/{id}/suspend', [UserManagementController::class, 'suspendUser'])->name('admin.users.suspend');


    // transactions
    Route::get('/transactions/contributions', [TransactionsController::class, 'viewContributions'])->name('admin.transactions.contributions');
    Route::get('/transactions/payout-requests', [TransactionsController::class, 'viewPayouts'])->name('admin.transactions.payouts');
    Route::get('/transactions/failed-payouts', [TransactionsController::class, 'viewFailedPayouts'])->name('admin.transactions.failed-payouts');

    // Approve/Decline Payout Requests

Route::post('/transactions/payout-requests/{payoutRequest}/approve', [PayoutRequestController::class, 'approvePayouts'])->name('admin.transactions.payout-requests.approve');
Route::post('/transactions/payout-requests/{payoutRequest}/reject', [PayoutRequestController::class, 'rejectPayouts'])->name('admin.transactions.payout-requests.reject');
Route::post('/transactions/payout-requests/{id}/retry', [PayoutRequestController::class, 'retryPayouts'])->name('admin.transactions.payout-requests.retry');


// Revenue

Route::get('/revenues/index', [RevenuesController::class, 'viewRevenues'])->name('admin.revenues.index');

// tasks
Route::get('/tasks/index', [TaskController::class, 'viewTask'])->name('admin.tasks.index');
Route::post('/tasks/store', [TaskController::class, 'storeTask'])->name('admin.tasks.store');
Route::get('/tasks/{task}/edit', [TaskController::class, 'editTask'])->name('admin.tasks.edit');
Route::put('/tasks/{task}/update', [TaskController::class, 'updateTask'])->name('admin.tasks.update');
Route::delete('/tasks/{task}/destroy', [TaskController::class, 'destroyTask'])->name('admin.tasks.destroy');

// settings

//settings Controller routes
    Route::controller(SettingsController::class)->prefix('settings')->group(function() {
    Route::get('/','index')->name('admin.setting.general');
    Route::post('/update-general','updateGeneral')->name('admin.update.general');
    Route::post('/update-logo','uploadLogo')->name('admin.upload.logo');
    Route::post('/upload/profile-image','updateImage')->name('admin.upload.profile-image');
    Route::put('/profile/update','updateProfile')->name('admin.profile.update');

    });

    // location
    Route::get('/countries/index', [LocationsController::class, 'viewCountries'])->name('admin.countries.index');
    Route::get('/states/index', [LocationsController::class, 'viewStates'])->name('admin.states.index');
    Route::get('/cities/index', [LocationsController::class, 'viewCities'])->name('admin.cities.index');

    // Add these routes to your web.php file
Route::patch('/admin/location/country/{country}/toggle', [LocationsController::class, 'toggleCountry'])->name('admin.location.toggle-country');
Route::patch('/admin/location/state/{state}/toggle', [LocationsController::class, 'toggleState'])->name('admin.location.toggle-state');
Route::patch('/admin/location/city/{city}/toggle', [LocationsController::class, 'toggleCity'])->name('admin.location.toggle-city');


Route::patch('/admin/location/country/{country}/toggle', [LocationsController::class, 'toggleCountry'])->name('admin.location.toggle-country');








    // Route::get('/transactions/{id}/show', [UserManagementController::class, 'showTransaction'])->name('admin.transactions.show');
    // Route::get('/transactions/{id}/approve', [UserManagementController::class, 'approveTransaction'])->name('admin.transactions.approve');
    // Route::get('/transactions/{id}/decline', [UserManagementController::class, 'declineTransaction'])->name('admin.transactions.decline');




    // Supend

    Route::patch('/users/{user}/suspend', [UserManagementController::class, 'suspendUser'])->name('admin.users.suspend');
    Route::delete('/users/{user}', [UserManagementController::class, 'destroyUser'])->name('admin.users.destroy');


    // Group membership management
    Route::post('/users/{user}/groups/{group}/members/{member}/approve', [UserManagementController::class, 'approveGroupMember'])->name('admin.users.groups.members.approve');
    Route::post('/users/{user}/groups/{group}/members/{member}/decline', [UserManagementController::class, 'declineGroupMember'])->name('admin.users.groups.members.decline');

    // group message

 Route::post('/users/{user}/groups/{group}/messages', [UserManagementController::class, 'sendMessage'])->name('admin.users.groups.messages');
 Route::get('/users/{user}/groups/{group}/messages', [UserManagementController::class, 'getMessages'])->name('admin.users.groups.messages.get');

//  Approve Members

Route::patch('/group-members/{id}/approve', [UserManagementController::class, 'approve'])->name('groups.members.approve');
Route::delete('/group-members/{id}/decline', [UserManagementController::class, 'decline'])->name('groups.members.decline');  

// notitfication

// Route::put('/profile/update', [SettingsController::class, 'updateProfile'])->name('profile.update');
// Route::post('/profile/upload-image', [SettingsController::class, 'uploadProfileImage'])->name('upload.profile-image');
Route::put('/password/update', [SettingsController::class, 'updatePassword'])->name('admin.password.update');
Route::put('/notifications/update', [SettingsController::class, 'updateNotifications'])->name('admin.notifications.update');
Route::put('/2fa/update', [SettingsController::class, 'update2FA'])->name('2fa.update');
Route::post('/2fa/generate-backup-codes', [SettingsController::class, 'generateBackupCodes'])->name('2fa.generate-backup-codes');









    //settings Controller routes
    Route::controller(SettingsController::class)->prefix('settings')->group(function() {
    Route::get('/','index')->name('admin.setting.general');
    Route::post('/update-general','updateGeneral')->name('admin.update.general');
    Route::post('/update-logo','uploadLogo')->name('admin.upload.logo');
    });

});





Route::controller(LocationsController::class)->prefix('location')->group(function () {
    Route::get('/country/json', 'getCountryJson')->name('location.get.country.json');
    // Route::get('/state/{country_id}/json', 'getStateByCountryJson')->name('location.get.state.json');
    // Route::get('/cities/{state_id}/json', 'getCitiesByStateJson')->name('location.get.cities.json');
    Route::get('/getstate', 'getsql')->name('getstate');
    // Location API routes
Route::get('/location/state/{country}/json', [LocationsController::class, 'getStatesByCountry'])->name('location.states');
Route::get('/location/cities/{state}/json', [LocationsController::class, 'getCitiesByState'])->name('location.cities');
});







Route::middleware(['auth', 'verified', 'track.activity'])->prefix('user')->group(function() {
    // Dashboard route

    Route::get('/dashboard', [UserDashboardController::class, 'index'])->name('user.dashboard');

    Route::get('/profile/settings', [UserProfileController::class, 'settings'])->name('user.profile.settings');
            Route::put('/profile/{user}/update', [UserProfileController::class, 'update'])->name('user.profile.update');
            Route::post('/profile/{user}/image/update', [UserProfileController::class, 'updateProfileImage'])->name('c');
            Route::put('/profile/update/passwrd', [UserProfileController::class, 'UpdatePwd'])->name('user.profile.update.password ');

             // notification

     Route::get('/notifications/{id}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::get('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.markAllAsRead');
Route::get('/check-notifications', [NotificationController::class, 'checkNewNotifications'])->name('check.notifications');

});







require __DIR__ . '/auth.php';
