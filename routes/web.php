<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Admin\GroupsController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\LocationsController;
use App\Http\Controllers\User\UserProfileController;
use App\Http\Controllers\User\NotificationController;
use App\Http\Controllers\User\UserDashboardController;
use App\Http\Controllers\Admin\UserManagementController;


Route::get('/', function () {
    return view('welcome');
});

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');


Route::controller(LocationsController::class)->prefix('location')->group(function () {
    Route::get('/country/json', 'getCountryJson')->name('location.get.country.json');
    Route::get('/state/{country_id}/json', 'getStateByCountryJson')->name('location.get.state.json');
    Route::get('/cities/{state_id}/json', 'getCitiesByStateJson')->name('location.get.cities.json');
    Route::get('/getstate', 'getsql')->name('getstate');
});


Route::prefix('admin')->middleware(['auth', 'admin', 'track.activity'])->group(function () {

     Route::get('/dashboard', [DashboardController::class, 'index'])->name('admin.dashboard');

    //  Groups

    Route::get('/groups/index', [GroupsController::class, 'viewGroups'])->name('admin.groups.index');
    Route::get('/groups/create', [GroupsController::class, 'createGroups'])->name('admin.groups.create');
    Route::post('/groups/store', [GroupsController::class, 'storeGroups'])->name('admin.groups.store');
    Route::get('/groups/{group}/edit', [GroupsController::class, 'editGroups'])->name('admin.groups.edit');
    Route::put('/groups/{group}/update', [GroupsController::class, 'updateGroups'])->name('admin.groups.update');
    Route::delete('/groups/{group}/destroy', [GroupsController::class, 'deleteGroups'])->name('admin.groups.destroy');
    Route::get('/groups/{group}/show', [GroupsController::class, 'showGroups'])->name('admin.groups.show');

    // user management
    Route::get('/users/index', [UserManagementController::class, 'viewUsers'])->name('admin.users.index');
    Route::get('/users/{id}/show', [UserManagementController::class, 'showUser'])->name('admin.users.show');
    Route::post('/users/store', [UserManagementController::class, 'store'])->name('admin.users.store');
    Route::get('/users/{id}/groups', [UserManagementController::class, 'viewUserGroups'])->name('admin.users.groups');
    Route::get('/users/{user}/groups/{group}/show', [UserManagementController::class, 'viewActiveGroup'])->name('admin.users.groups.show');
    Route::get('/users/{id}/transactions', [UserManagementController::class, 'viewUserTransactions'])->name('admin.users.transactions');

    // Supend

    Route::patch('/users/{user}/suspend', [UserManagementController::class, 'suspendUser'])->name('admin.users.suspend');
    Route::delete('/users/{user}', [UserManagementController::class, 'destroyUser'])->name('admin.users.destroy');


    // Group membership management
    Route::post('/users/{user}/groups/{group}/members/{member}/approve', [UserManagementController::class, 'approveGroupMember'])->name('admin.users.groups.members.approve');
    Route::post('/users/{user}/groups/{group}/members/{member}/decline', [UserManagementController::class, 'declineGroupMember'])->name('admin.users.groups.members.decline');

    // group message

Route::post('/users/{user}/groups/{group}/messages', [UserManagementController::class, 'sendMessage'])->name('admin.users.groups.messages');
 Route::get('/users/{user}/groups/{group}/messages', [UserManagementController::class, 'getMessages'])->name('admin.users.groups.messages.get');

//  Approve Members

Route::patch('/group-members/{id}/approve', [UserManagementController::class, 'approve'])->name('groups.members.approve');
Route::delete('/group-members/{id}/decline', [UserManagementController::class, 'decline'])->name('groups.members.decline');  



         //settings Controller routes
         Route::controller(SettingsController::class)->prefix('settings')->group(function() {
            Route::get('/','index')->name('admin.setting.general');
            Route::post('/update-general','updateGeneral')->name('admin.update.general');
            Route::post('/update-logo','uploadLogo')->name('admin.upload.logo');
            });

});





Route::controller(LocationsController::class)->prefix('location')->group(function () {
    Route::get('/country/json', 'getCountryJson')->name('location.get.country.json');
    Route::get('/state/{country_id}/json', 'getStateByCountryJson')->name('location.get.state.json');
    Route::get('/cities/{state_id}/json', 'getCitiesByStateJson')->name('location.get.cities.json');
    Route::get('/getstate', 'getsql')->name('getstate');
});




Route::middleware(['auth', 'verified', 'track.activity'])->prefix('user')->group(function() {
    // Dashboard route

    Route::get('/dashboard', [UserDashboardController::class, 'index'])->name('user.dashboard');

    Route::get('/profile/settings', [UserProfileController::class, 'settings'])->name('user.profile.settings');
            Route::put('/profile/{user}/update', [UserProfileController::class, 'update'])->name('user.profile.update');
            Route::post('/profile/{user}/image/update', [UserProfileController::class, 'updateProfileImage'])->name('user.profile.image.update');
            Route::put('/profile/update/passwrd', [UserProfileController::class, 'UpdatePwd'])->name('user.profile.update.password ');

             // notification

     Route::get('/notifications/{id}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::get('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.markAllAsRead');
Route::get('/check-notifications', [NotificationController::class, 'checkNewNotifications'])->name('check.notifications');

});







require __DIR__ . '/auth.php';
