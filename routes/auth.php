<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Auth\PasswordController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\User\NotificationController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\ConfirmablePasswordController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\EmailVerificationPromptController;
use App\Http\Controllers\Auth\EmailVerificationNotificationController;

Route::middleware('guest')->group(function () {
    Route::get('register', [RegisteredUserController::class, 'create'])
                ->name('register');

    Route::post('register', [RegisteredUserController::class, 'store'])->name('register.store');

    // login
    Route::get('login', [AuthenticatedSessionController::class, 'create'])
                ->name('login');
    // login
    Route::post('login', [AuthenticatedSessionController::class, 'store']);





    // reset password
    Route::get('reset-password/{token}', [NewPasswordController::class, 'create'])
                ->name('password.reset');

    // reset password
    Route::post('reset-password', [NewPasswordController::class, 'store'])
                ->name('password.store');


     /* To check email verification */
    Route::post('/checkAvaliability', [RegisteredUserController::class, 'emailCheck'])->name('check_email_availability');




});

  // forget password
    Route::get('forgot-password', [PasswordResetLinkController::class, 'create'])
                ->name('password.request');

    // forget password
    Route::post('forgot-password', [PasswordResetLinkController::class, 'forgetStore'])
                ->name('password.email');
    // verify password

Route::get('verify-forgetpassword', EmailVerificationPromptController::class, '__invokeverifyForgetpassword')->name('forgetpassword.verification');

Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)->middleware(['signed', 'throttle:6,1'])->name('verification.verify');
Route::get('verify-email', [EmailVerificationPromptController::class, '__invoke'])->name('verification.notice');

Route::post('/verityotp', [EmailVerificationPromptController::class, 'userActivation'])->name('verifyotp');


    Route::post('email/verification-notification', [EmailVerificationNotificationController::class, 'store'])
                ->middleware('throttle:6,1')
                ->name('verification.send');

Route::post('/verityotp-forget', [EmailVerificationPromptController::class, 'forgetpasswordActivation'])->name('verifyotp-forget');

// OTP resend functionality
Route::post('/resend-otp', [EmailVerificationPromptController::class, 'resendOtp'])->name('resend.otp');
Route::post('/cleanup-expired-tokens', [EmailVerificationPromptController::class, 'cleanupExpiredTokens'])->name('cleanup.tokens');


Route::middleware('auth')->group(function () {

 Route::get('register-info', [RegisteredUserController::class, 'registerInfo'])
                ->name('register.info');

Route::post('store-info', [RegisteredUserController::class, 'storeInfo'])
                ->name('store.info');


 Route::get('/identification', [HomeController::class, 'getIdentification'])->name('identification');

 Route::get('/kyc', [RegisteredUserController::class, 'KycVericiation'])->name('kyc.verification');


Route::post('/kyc/upload', [RegisteredUserController::class, 'kycUpload'])->name('kyc.vericiation.upload');
Route::post('/upload-kyc', [RegisteredUserController::class, 'uploadKyc']);
Route::get('/kyc/{id}', [NotificationController::class, 'show'])->name('kyc.details');



Route::get('/selfie', [RegisteredUserController::class, 'SelfieVericiation'])->name('selfie.vericiation');

Route::post('/selfie/upload', [RegisteredUserController::class, 'selfieUpload'])->name('selfie.vericiation.upload');
Route::get('/success', [RegisteredUserController::class, 'Success'])->name('success');

                // otp

// Route::get('verify-email', EmailVerificationPromptController::class, '__invoke')
//                 ->name('verification.notice');






    Route::get('confirm-password', [ConfirmablePasswordController::class, 'show'])
                ->name('password.confirm');

    Route::post('confirm-password', [ConfirmablePasswordController::class, 'store']);

    Route::put('password', [PasswordController::class, 'update'])->name('password.update');

    Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
                ->name('logout');
});
